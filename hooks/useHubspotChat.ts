import React from 'react';

declare global {
  interface Window {
    hsConversationsOnReady: any[];
    HubSpotConversations: any;
  }
}

const useHubspotChat = () => {
  const portalId = 23819432;
  const [hasLoaded, setHasLoaded] = React.useState(false);
  const [activeConversation, setActiveConversation] = React.useState(false);
  const eventRef = React.useRef(null);

  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      window.hsConversationsOnReady = [
        () => {
          setHasLoaded(true);
        }
      ];

      let script = document.createElement('script');
      script.id = 'hs-script-loader';
      script.src = `//js-na1.hs-scripts.com/${portalId}.js`;
      script.async = true;

      document.body.appendChild(script);

      return () => {
        document.body.removeChild(script);
        window.hsConversationsOnReady = [];
      };
    }
  }, []);

  React.useEffect(() => {
    if (hasLoaded && window.HubSpotConversations) {
      eventRef.current = (payload: any) => {
        setActiveConversation(payload.conversation.conversationId);
      };

      window.HubSpotConversations.on('conversationStarted', eventRef.current);
    }

    return () => {
      if (window.HubSpotConversations && eventRef.current) {
        window.HubSpotConversations.off(
          'conversationStarted',
          eventRef.current
        );
      }
    };
  }, [hasLoaded]);

  const closeHandler = React.useCallback(() => {
    if (hasLoaded && window.HubSpotConversations) {
      window.HubSpotConversations.widget.close();
    }
  }, [hasLoaded]);

  return {
    hasLoaded,
    activeConversation,
    closeHandler
  };
};

export default useHubspotChat;
