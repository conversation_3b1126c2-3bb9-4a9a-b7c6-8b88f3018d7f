export class AsyncLock {
  public disable: () => void;
  public promise: Promise<void>;

  constructor() {
    this.disable = () => {
      // do nothing
    };
    this.promise = Promise.resolve();
  }

  enable() {
    this.promise = new Promise((resolve) => (this.disable = resolve));
  }
}

export const safeParseJson = (json: string) => {
  try {
    return JSON.parse(json);
  } catch (error) {
    return null;
  }
};

export const stringToColour = (str: string) => {
  let hash = 0;
  str.split('').forEach((char) => {
    hash = char.charCodeAt(0) + ((hash << 5) - hash);
  });
  let colour = '#';
  for (let i = 0; i < 3; i++) {
    const value = (hash >> (i * 8)) & 0xff;
    colour += value.toString(16).padStart(2, '0');
  }
  return colour.toLocaleUpperCase();
};

export const textColorOfHex = (str: string) => {
  return parseInt(str.substring(1), 16) > 0xffffff / 2 ? '#000000' : '#ffffff';
}