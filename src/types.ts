// Generated by Xata Codegen 0.23.2. Please do not edit.
import type { SchemaInference, XataRecord } from '@xata.io/client';
import { PROCESS_STATUS_CUSTOM_METRIC_EVENT, PROCESS_STATUS_METRIC_EVENT, PROCESS_STATUS_TOOL_METRIC_EVENT } from 'pages/api/constants/pipeline-status';
import { CPU_MEMORY_DISK_RECORD_TYPE, NETWORK_RECORD_TYPE, PROCESS_STATUS_RECORD_TYPE, TOOL_FINISHED_RECORD_TYPE, TOOL_RECORD_TYPE } from 'pages/api/constants/record-types';

const tables = [
  {
    name: 'Users',
    columns: [
      { name: 'name', type: 'string' },
      { name: 'email', type: 'email' },
      { name: 'rawData', type: 'text' },
      { name: 'companyType', type: 'string' },
      { name: 'companySize', type: 'string' },
      { name: 'jobTitle', type: 'string' },
      { name: 'environment', type: 'string' },
      { name: 'metaData', type: 'json' },
      { name: 'currentStepFormName', type: 'string' },
      { name: 'isFormSubmissionCompleted', type: 'bool' },
      { name: 'productGoals', type: 'multiple' },
      { name: 'variantKey', type: 'string' },
      { name: 'technologyStack', type: 'multiple' }
    ]
  },
  {
    name: 'logs',
    columns: [
      { name: 'logMetadata', type: 'json' },
      { name: 'message', type: 'string' },
      { name: 'level', type: 'string' },
      { name: 'source', type: 'string' },
      { name: 'thread', type: 'string' },
      { name: 'unixDate', type: 'float' },
      { name: 'humanDate', type: 'datetime' },
      { name: 'userId', type: 'string' },
      { name: 'serviceName', type: 'string' },
      { name: 'rawLog', type: 'json' },
      { name: 'properties', type: 'json' },
      { name: 'mem_used', type: 'float' },
      { name: 'mem_total', type: 'float' },
      { name: 'mem_free', type: 'float' },
      { name: 'cpu_p', type: 'float' },
      { name: 'user_p', type: 'float' },
      { name: 'system_p', type: 'float' },
      { name: 'eth0_rx_bytes', type: 'float' },
      { name: 'eth0_tx_bytes', type: 'float' },
      { name: 'eth0_rx_errors', type: 'float' },
      { name: 'eth0_tx_errors', type: 'float' },
      { name: 'eth0_rx_packets', type: 'float' },
      { name: 'eth0_tx_packets', type: 'float' },
      { name: 'recordType', type: 'string' },
      { name: 'mem_used_percent', type: 'float' },
      { name: 'read_size', type: 'float' },
      { name: 'write_size', type: 'float' },
      { name: 'process_status', type: 'string' },
      { name: 'process_type', type: 'string' },
      { name: 'event_type', type: 'string' },
      { name: 'run_name', type: 'string' },
      { name: 'disk_p', type: 'float' },
      { name: 'disk_utilization_p', type: 'float' }
      // opentelemetry - property to be disconnected from xata base first
      // { name: 'api_key', type: 'string' },
      // { name: 'user_id', type: 'string' },
      // { name: 'service_name', type: 'string' },
      // { name: 'run_id', type: 'string' },
      // { name: 'run_name', type: 'string' },
      // { name: 'timestamp', type: 'float' },
    ]
  },
  { name: 'RANDOM', columns: [] },
  {
    name: 'ApiKeys',
    columns: [
      { name: 'userId', type: 'string' },
      { name: 'serviceName', type: 'string' },
      { name: 'apiKey', type: 'string' },
      { name: 'isActive', type: 'bool' },
      { name: 'typeEnvironment', type: 'string' },
      { name: 'username', type: 'string' },
      { name: 'userImgUrl', type: 'string' }
    ]
  }
] as const;

export type SchemaTables = typeof tables;
export type InferredTypes = SchemaInference<SchemaTables>;

export type Users = InferredTypes['Users'];
export type UsersRecord = Users & XataRecord;

export type Logs = {
  api_key: string;
  user_id: string;
  service_name: string;
  run_id: string;
  run_name: string;
  timestamp: number;
  system_memory_total: number;
  system_memory_used: number;
  system_memory_available: number;
  system_memory_utilization: number;
  system_memory_swap_total: number;
  system_memory_swap_used: number;
  system_cpu_utilization: number;
  system_disk_io: number;
  system_disk_total_space: number;
  system_disk_available_space: number;
  system_disk_used_space: number;
  system_disk_utilization: number;
  record_type: string;
  process_status: string;
  human_date: string;
  properties: any;
  message: string;
  id: string;
  event_type: string;
  process_cpu_utilization: number;
  process_disk_io_read: number;
  process_disk_io_write: number;
  process_memory_used: number;
  process_memory_virtual: number;
  process_cpu_time: number;
  tool_name: string;
  tool_cmd: string;
  tool_pid: number;
  tool_binary_path: string;
};
export type LogsRecord = Logs;

export type RecordType =
  | 'cmdline'
  | 'syscall'
  | typeof CPU_MEMORY_DISK_RECORD_TYPE
  | typeof NETWORK_RECORD_TYPE
  | typeof PROCESS_STATUS_RECORD_TYPE
  | typeof TOOL_RECORD_TYPE
  | typeof TOOL_FINISHED_RECORD_TYPE
  | typeof PROCESS_STATUS_METRIC_EVENT
  | typeof PROCESS_STATUS_TOOL_METRIC_EVENT
  | typeof PROCESS_STATUS_CUSTOM_METRIC_EVENT;

export const RecordTypes = [
  'cmdline',
  'syscall',
  CPU_MEMORY_DISK_RECORD_TYPE,
  NETWORK_RECORD_TYPE,
  PROCESS_STATUS_RECORD_TYPE,
  TOOL_RECORD_TYPE,
  TOOL_FINISHED_RECORD_TYPE,
  PROCESS_STATUS_METRIC_EVENT,
  PROCESS_STATUS_CUSTOM_METRIC_EVENT
] as const;

export const RecordTypeDisplayNames = {
  cmdline: 'Command Line',
  syscall: 'System Call',
  [CPU_MEMORY_DISK_RECORD_TYPE]: 'CPU, Memory & Disk',
  [NETWORK_RECORD_TYPE]: 'Network',
  [PROCESS_STATUS_RECORD_TYPE]: 'Process Status',
  [TOOL_RECORD_TYPE]: 'Tool',
  [TOOL_FINISHED_RECORD_TYPE]: 'Tool Finished',
  [PROCESS_STATUS_METRIC_EVENT]: 'Metric Event',
  [PROCESS_STATUS_TOOL_METRIC_EVENT]: 'Tool Metric Event',
  [PROCESS_STATUS_CUSTOM_METRIC_EVENT]: 'Custom Metric Event'
} as const;

export type SystemDiskIODetails = {
  disk_available_space: number;
  disk_total_space: number;
  disk_utilization: number;
  disk_used_space: number;
  timestamp: number;
};

export type TeamMemberState = 'invited' | 'joined';

export type TeamMember = {
  userId: string;
  userName: string;
  userAvatarUrl: string;
  email: string;
  state: TeamMemberState;
  owner: boolean;
};

export type Team = {
  id: string;
  name: string;
  members: TeamMember[];
  ownerUserId: string;
  createdAt: Date;
  externalInvitations: ExternalInvitiation[];
  type: 'owner' | 'member';
};

export type PendingInvitation = {
  userId: string;
  teamId: string;
  teamName: string;
  teamUserName: string;
  teamUserAvatarUrl: string;
};

export type ExternalInvitiation = {
  teamId: string;
  code: string;
  email: string;
}

export type ServiceId = {
  name: string;
  teamId: string;
}


export type Random = InferredTypes['RANDOM'];
export type RandomRecord = Random & XataRecord;

export type ApiKeys = InferredTypes['ApiKeys'];
export type ApiKeysRecord = ApiKeys & XataRecord;
