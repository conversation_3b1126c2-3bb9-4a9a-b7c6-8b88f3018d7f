{"compilerOptions": {"target": "es6", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "incremental": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "baseUrl": ".", "paths": {"@/components/*": ["components/*"], "@/context/*": ["context/*"], "src/*": ["src/*"], "@/*": ["src/*"]}, "plugins": [{"name": "next"}], "strictNullChecks": false}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "pages", "components", "lib", "pages/_document.ts", "lib/sanity.js", "pages/auth/signin.js", "components/routes/billing/CheckoutForm.js", "components/routes/security/components/RuleSelection", "components/routes/objects/components", "components/routes/objects/code", "components/routes/objects/components/ViewCode", "context/grafana"], "exclude": ["node_modules", "dom-lib-local.ts", "**/dom-lib-local.ts", "flowsheets/**/*"]}