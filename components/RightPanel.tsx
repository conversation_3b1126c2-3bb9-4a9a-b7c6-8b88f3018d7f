interface PanelProps {
  TopComponent: React.ComponentType<any>;
  BottomComponent: React.ComponentType<any>;
  topProps?: any;
  bottomProps?: any;
}

export const RightPanel: React.FC<PanelProps> = ({
  TopComponent,
  BottomComponent,
  topProps,
  bottomProps
}) => {
  return (
    <div className="border-l-[1px] border-[#c1c9d0]/[.3] h-full flex flex-col bg-gradient-to-tl  from-[#2a2b32] to-[#000000]">
      <TopComponent {...topProps} />
      <BottomComponent {...bottomProps} />
    </div>
  );
};
