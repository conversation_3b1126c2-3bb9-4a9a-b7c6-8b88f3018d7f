import React, { useRef, useEffect, useState } from 'react';
import { twMerge } from 'tailwind-merge';

type StyledAppPageProps = {
  children: React.ReactNode;
  hasGrid?: boolean;
  gridSize?: string;
  className?: string;
};

export function StyledAppPageWrapper({
  children,
  hasGrid = false,
  gridSize = 'grid-cols-[1fr_1.2fr]',
  className
}: StyledAppPageProps) {
  const wrapperRef = useRef<HTMLDivElement>(null);
  const [minHeight, setMinHeight] = useState('auto');

  useEffect(() => {
    const updateHeight = () => {
      if (wrapperRef.current) {
        const topPosition = wrapperRef.current.getBoundingClientRect().top;
        const viewportHeight = window.innerHeight;
        const newMinHeight = `${viewportHeight - topPosition}px`;
        setMinHeight(newMinHeight);
      }
    };

    updateHeight();
    window.addEventListener('resize', updateHeight);

    return () => window.removeEventListener('resize', updateHeight);
  }, []);

  const gridClasses = hasGrid ? `grid ${gridSize} gap-8` : '';

  return (
    <div
      ref={wrapperRef}
      className="bg-black w-full overflow-x-hidden"
      style={{ minHeight }}
    >
      <div
        className={twMerge(
          'pl-[56px] pr-4 text-white py-8 text-sm duration-50 fadeIn w-full',
          className
        )}
      >
        <div className={twMerge('w-full', gridClasses)}>
          <div className="col-span-full max-w-full">{children}</div>
        </div>
      </div>
    </div>
  );
}

export const StyledSubTitle = ({
  className,
  text
}: {
  className?: string;
  text: string;
}) => (
  <div className={twMerge('text-[16px] my-4 break-words', className)}>
    {text}
  </div>
);
