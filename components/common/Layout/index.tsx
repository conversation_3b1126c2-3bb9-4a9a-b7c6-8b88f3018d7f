'use client';
import { useState, useEffect } from 'react';
import cn from 'clsx';

import { Footer } from '../Footer';
import { Navigation } from '../../navigation';
import { useRouter } from 'next/router';
import { NavigationLeftMenu } from '@/components/navigation/NavigationLeftMenu';

export function Layout({ children }) {
  const [ogUrl, setOgUrl] = useState('');
  const [isClient, setIsClient] = useState(false);
  const router = useRouter();

  useEffect(() => {
    setIsClient(true);
    if (typeof window !== 'undefined') {
      setOgUrl(`https://${window.location.host}${router.pathname}`);
    }
  }, [router.pathname]);

  // Only calculate isApplicationPage after client hydration
  const isApplicationPage = isClient && ogUrl.includes('/app/');

  return (
    <div className="bg-black min-h-screen flex flex-col text-white">
      <div className="flex flex-grow">
        {isApplicationPage && (
          <div className="w-[50px] flex-shrink-0">
            <NavigationLeftMenu />
          </div>
        )}
        <div className="flex flex-col flex-grow">
          <Navigation />
          <main
            className={cn(
              'flex-grow overflow-auto overflow-x-hidden',
              isApplicationPage && 'bg-brandBlack'
            )}
          >
            {children}
          </main>
          {!isApplicationPage && <Footer />}
        </div>
      </div>
    </div>
  );
}