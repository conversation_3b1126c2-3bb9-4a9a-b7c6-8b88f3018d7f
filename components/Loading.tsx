import React from 'react';

interface LoadingProps {
  isLoading: boolean;
  theme?: 'default' | 'random';
}

export const Loading: React.FC<LoadingProps> = ({
  isLoading,
  theme = 'default'
}) => {
  if (!isLoading) {
    return null; // Don't render anything if isLoading is false
  }

  // Define color options based on the theme
  const colorOptions =
    theme === 'random'
      ? ['border-green-200', 'border-purple-500', 'border-blue-500']
      : ['border-brandBlue'];

  // Randomly select a color from the array
  const randomColor =
    colorOptions[Math.floor(Math.random() * colorOptions.length)];

  return (
    <div className="flex justify-center items-center h-20">
      <div
        className={`animate-spin rounded-full h-8 w-8 border-t-4 border-b-4 ${randomColor}`}
      ></div>
    </div>
  );
};

export default Loading;
