import React, { useState } from 'react';
import cn from 'clsx';

interface RegionSelectionProps {
  regions?: string[];
  value: string;
  onSelect: (selectedRegion: string) => void;
  name: string;
  className?: string; // Optional className parameter
}

export const AWS_CLOUD_REGIONS: string[] = [
  'us-east-1 🇺🇸',
  'us-west-1 🇺🇸',
  'eu-west-1 🇮🇪',
  'eu-central-1 🇩🇪',
  'ap-south-1 🇮🇳'
];

export function RegionSelection({
  regions = AWS_CLOUD_REGIONS,
  onSelect,
  name,
  className // Receive the className prop
}: RegionSelectionProps) {
  const [selectedRegion, setSelectedRegion] = useState(regions[0]);

  const handleRegionSelect = (event: React.ChangeEvent<HTMLSelectElement>) => {
    // Extract the value without the emoji
    const selectedRegionWithEmoji = event.target.value;
    const selectedRegionWithoutEmoji = selectedRegionWithEmoji.split(' ')[0];

    setSelectedRegion(selectedRegionWithEmoji); // Set the selected region with emoji
    onSelect(selectedRegionWithoutEmoji); // Pass the selected region without emoji
  };

  if (!regions || !selectedRegion) throw new Error('Missing regions or value');

  return (
    <div
      className={cn(
        'relative text-right flex items-center justify-end',
        className
      )}
    >
      <select
        name={name}
        className={cn(
          'appearance-none w-full text-sm pl-4 pr-8 py-1 bg-transparent',
          'border border-solid border-[#c1c9d0]/[.3]',
          'rounded focus:outline-none focus:ring-2 focus:ring-blue-500'
        )}
        value={selectedRegion}
        onChange={handleRegionSelect}
      >
        {regions.map((region) => (
          <option key={region} value={region}>
            {region}
          </option>
        ))}
      </select>
    </div>
  );
}
