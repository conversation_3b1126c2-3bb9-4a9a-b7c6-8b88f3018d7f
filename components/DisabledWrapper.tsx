import { faExclamationTriangle } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useState } from 'react';

export function DisabledWrapper({ children }: { children: React.ReactNode }) {
  const [showMessage, setShowMessage] = useState(false);

  const handleClick = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    e.stopPropagation();
    setShowMessage(true);
    setTimeout(() => setShowMessage(false), 2800);
  };

  return (
    <div onClick={handleClick}>
      <div className="relative pointer-events-none">{children}</div>
      {showMessage && (
        <div className="fadeIn duration-50">
          <div className="absolute inset-0 bg-black opacity-50 flex justify-center items-center"></div>
          <div className="absolute inset-0 bg-transparent flex justify-center items-center mt-[-200px]">
            <div className="text-white text-lg text-left">
              <div>
                <FontAwesomeIcon
                  icon={faExclamationTriangle}
                  color="#FFA500"
                  className="mr-2"
                />
                Beta restriction
              </div>
              <div className="mt-2">
                Functionality is restricted in the{' '}
                <span className="decoration-brandBlue underline underline-offset-[10px] decoration-[5px]">
                  beta version{' '}
                </span>
                of the app
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
