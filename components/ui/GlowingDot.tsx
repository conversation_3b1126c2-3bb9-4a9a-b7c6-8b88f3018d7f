import cn from 'clsx';
import { OPERATION_STATUS_TYPES } from 'lib/constants/statusTypes';

export const GlowingDot = ({
  integrationStatus,
  className
}: {
  integrationStatus: string;
  className?: string;
}) => {
  return (
    <span
      className={cn(
        'animate-ping',
        integrationStatus === OPERATION_STATUS_TYPES.ONLINE && 'text-green-400',
        // 'text-[#50e3c2]') ||
        integrationStatus === OPERATION_STATUS_TYPES.STALE ||
          (integrationStatus === OPERATION_STATUS_TYPES.OFFLINE &&
            'text-orange-400'),
        // integrationStatus === OPERATION_STATUS_TYPES.OFFLINE && 'text-pink-700',
        integrationStatus === '' && 'hidden',
        'drop-shadow-lg shadow-lg shadow-green-500/20',
        className
      )}
    >
      ●
    </span>
  );
};
