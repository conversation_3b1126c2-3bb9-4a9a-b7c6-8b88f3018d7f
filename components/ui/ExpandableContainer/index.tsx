import { ChevronDown, ChevronRight } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import './styles.scss';

interface ExpandableContainerProps {
  title: string;
  children: React.ReactNode;
  icon?: React.ReactNode;
}

export const ExpendableContainerHeader = (props: {
  icon: React.ReactNode;
  onClick: () => void;
  children: any;
  displayChevron: boolean;
  isExpanded: boolean;
}) => {
  return (
    <div className="expandable-container-header" onClick={props.onClick}>
      <div className="expandable-container-icon">{props.icon}</div>
      <div className="expandable-container-message">{props.children}</div>
      <div className="expandable-container-chevron">
        {props.displayChevron ? (
          props.isExpanded ? (
            <ChevronRight />
          ) : (
            <ChevronDown />
          )
        ) : null}
      </div>
    </div>
  );
};

const ExpandableContainer = (props: ExpandableContainerProps) => {
  const { title, children } = props;
  const [expanded, setExpanded] = useState(false);
  const bodyRef = useRef<HTMLDivElement>(null);

  const recalculateHeight = () => {
    if (bodyRef.current) {
      bodyRef.current.style.maxHeight = `${bodyRef.current.scrollHeight + 5}px`;
    }
  };

  useEffect(() => {
    if (bodyRef.current) {
      bodyRef.current.style.maxHeight = expanded
        ? `${bodyRef.current.scrollHeight + 5}px`
        : '0';
    }
  }, [bodyRef.current, expanded]);

  return (
    <div className="expandable-container-item">
      <ExpendableContainerHeader
        icon={props.icon}
        onClick={() => setExpanded(!expanded)}
        displayChevron={true}
        isExpanded={expanded}
      >
        {title}
      </ExpendableContainerHeader>
      {children && (
        <div
          className={`expandable-container-body ${expanded ? 'expanded' : ''}`}
          onMouseMove={recalculateHeight}
          ref={bodyRef}
        >
          {children}
        </div>
      )}
    </div>
  );
};

export default ExpandableContainer;
