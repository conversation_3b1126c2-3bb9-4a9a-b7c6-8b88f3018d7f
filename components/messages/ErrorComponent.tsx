import { faExclamationCircle } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

interface ErrorComponentProps {
  text: string | null;
}

export const ErrorComponent: React.FC<ErrorComponentProps> = ({ text }) => {
  if (!text) return null;

  return (
    <div className="border-[rgb(193,201,208)] border-solid border-[1px] border-opacity-30 p-5 rounded-md flex items-center space-x-4">
      <FontAwesomeIcon
        icon={faExclamationCircle}
        size="1x"
        className="text-red-500"
      />
      <p>
        {text ||
          'The bucket is currently empty. Please upload an object following the instructions provided in the code snippets panel.'}
      </p>
    </div>
  );
};
