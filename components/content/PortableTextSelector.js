import { urlFor } from '../../libs/sanity';
import Image from 'next/image';

export const ptComponents = {
  types: {
    image: ({ value }) => {
      const urlSrc = urlFor(value).fit('max').auto('format').url();

      if (!value?.asset?._ref) {
        return null;
      }
      return (
        <div className="relative block">
          <div className="w-[100%] h-auto min-h-[230px] md:h-[400px] relative mt-8">
            <Image
              alt={value.alt || ' '}
              src={urlSrc}
              fill
              sizes="(max-width: 768px) 100vw,
              (max-width: 1200px) 50vw,
              33vw"
              style={{ borderRadius: '5px' }}
            />
          </div>
        </div>
      );
    }
  },
  block: {
    h2: ({ children }) => <h2 className="text-2xl mt-6 mb-3">{children}</h2>,
    h1: ({ children }) => <h2 className="text-4xl mt-6 mb-3">{children}</h2>,
    normal: ({ children }) => (
      <p className="text-blue mb-10 font-light">{children}</p>
    )
  },
  marks: {
    em: ({ children }) => <p>{children}</p>,
    span: ({ children }) => <p>{children}</p>,
    link: ({ value, children }) => {
      const target = (value?.href || '').startsWith('http')
        ? '_blank'
        : undefined;
      return (
        <a
          href={value?.href}
          target={target}
          rel={target === '_blank' && 'noreferrer noopener'}
          className="underline hover:opacity-60 decoration-green-600 hover:decoration-green-500 decoration-2 decoration-dashed underline-offset-4"
        >
          {children}
        </a>
      );
    }
  },
  list: {
    bullet: ({ children }) => <ul className="pl-2 list-disc">{children}</ul>,
    number: ({ children }) => <ol className="pl-2 list-decimal">{children}</ol>
  },
  listItem: {
    bullet: ({ children }) => (
      <li className="list-disc font-light">{children}</li>
    ),
    number: ({ children }) => (
      <li className="list-decimal font-light">{children}</li>
    )
  }
};
