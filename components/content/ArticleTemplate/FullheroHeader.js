import { Container } from 'components/ui';

export const FullHeroHeader = ({ children }) => {
  return (
    <div className="relative text-white overflow-auto w-full bg-gradient-to-tr from-[#00151a] to-[#0f191a]">
      <img
        className="w-full object-cover h-[46vh] md:h-96 opacity-50 mix-blend-luminosity"
        src="/assets/article-bg.webp"
        alt=""
      />
      <div className="top-5 md:top-14 absolute w-full">
        <Container className="md:max-w-3xl 2xl:max-w-4xl">
          <div className="m-auto">{children}</div>
        </Container>
      </div>
    </div>
  );
};
