import Link from 'next/link';

function classNames(...classes) {
  return classes.filter(Boolean).join(' ');
}

export const ContentCard = ({
  title,
  href,
  asHref,
  description,
  categoryHref,
  categoryColor,
  categoryName
}) => {
  return (
    <div key={title}>
      <div>
        <a href={categoryHref} className="inline-block">
          <span
            className={classNames(
              categoryColor,
              'inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium'
            )}
          >
            {categoryName}
          </span>
        </a>
      </div>
      <Link href={href} as={asHref} className="mt-4 block">
        <p className="text-xl font-semibold text-gray-900">{title}</p>
        <p className="mt-3 text-base text-gray-500">{description}</p>
      </Link>
    </div>
  );
};
