import React, { useState } from 'react';
import cn from 'clsx';

interface SelectionFieldDropdownProps {
  className?: string;
  options: string[];
  value?: string;
  defaultSelectedOption?: string;
  onSelect?: (selectedOption: string) => void;
  name: string;
}

export function SelectionFieldDropdown({
  className,
  options,
  value,
  defaultSelectedOption = options[0],
  onSelect,
  name
}: SelectionFieldDropdownProps) {
  const [selectedOption, setSelectedOption] = useState(
    value || defaultSelectedOption
  );

  const handleOptionSelect = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedOptionValue = event.target.value;
    setSelectedOption(selectedOptionValue);
    onSelect?.(selectedOptionValue);
    console.log('Option selected:', selectedOptionValue);
  };

  return (
    <div className="relative text-right flex items-center justify-end">
      <select
        name={name}
        className={cn(
          'appearance-none w-full text-sm pl-4 pr-8 py-1 bg-transparent',
          'border border-solid border-[#c1c9d0]/[.3]',
          'text-white',
          'rounded focus:outline-none focus:ring-2 focus:ring-blue-500',
          className
        )}
        value={selectedOption}
        onChange={handleOptionSelect}
      >
        {options.map((option) => (
          <option key={option} value={option}>
            {option}
          </option>
        ))}
      </select>
    </div>
  );
}
