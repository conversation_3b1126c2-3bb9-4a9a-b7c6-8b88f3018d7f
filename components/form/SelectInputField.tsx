import React, { useEffect } from 'react';
import { styled } from '@stitches/react';
import { violet, mauve, blackA } from '@radix-ui/colors';
import {
  CheckIcon,
  ChevronDownIcon,
  ChevronUpIcon
} from '@radix-ui/react-icons';
import * as SelectPrimitive from '@radix-ui/react-select';
import cn from 'clsx';

const textWhiteColor = '#ecedee';
const blackMauve1 = '#161618';
const selectedBlue = '#3e62dd';
const brandPurple = '#004CDC';

const StyledTrigger = styled(SelectPrimitive.SelectTrigger, {
  all: 'unset',
  display: 'inline-flex',
  alignItems: 'center',
  justifyContent: 'center',
  borderRadius: 4,
  padding: '0 15px',
  fontSize: 12,
  lineHeight: 1,
  height: 25,
  gap: 5,
  backgroundColor: blackMauve1,
  color: textWhiteColor,
  boxShadow: `0 2px 10px ${blackA.blackA7}`,
  '&:hover': { backgroundColor: selectedBlue, color: textWhiteColor },
  '&:focus': { boxShadow: `0 0 0 2px black` },
  '&[data-placeholder]': { color: textWhiteColor }
});

const StyledIcon = styled(SelectPrimitive.SelectIcon, {
  color: textWhiteColor
});

const StyledSeparator = styled(SelectPrimitive.Separator, {
  height: 1,
  backgroundColor: violet.violet6,
  margin: 5
});

const StyledContent = styled(SelectPrimitive.Content, {
  backgroundColor: '#151718',
  color: textWhiteColor,
  borderRadius: 6,
  boxShadow:
    '0px 10px 38px -10px rgba(22, 23, 24, 0.35), 0px 10px 20px -15px rgba(22, 23, 24, 0.2)'
});

const StyledViewport = styled(SelectPrimitive.Viewport, {
  padding: 5
});

function Content({ children, ...props }) {
  return (
    <SelectPrimitive.Portal>
      <StyledContent {...props}>{children}</StyledContent>
    </SelectPrimitive.Portal>
  );
}

const StyledItem = styled(SelectPrimitive.Item, {
  all: 'unset',
  fontSize: 13,
  lineHeight: 1,
  color: textWhiteColor,
  borderRadius: 3,
  display: 'flex',
  alignItems: 'center',
  height: 25,
  padding: '0 35px 0 25px',
  position: 'relative',
  userSelect: 'none',

  '&[data-disabled]': {
    color: mauve.mauve8,
    pointerEvents: 'none'
  },

  '&[data-highlighted]': {
    backgroundColor: brandPurple || '#202425',
    color: textWhiteColor
  }
});

const StyledItemIndicator = styled(SelectPrimitive.ItemIndicator, {
  position: 'absolute',
  left: 0,
  width: 25,
  display: 'inline-flex',
  alignItems: 'center',
  justifyContent: 'center'
});

const scrollButtonStyles = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  height: 25,
  backgroundColor: textWhiteColor,
  color: violet.violet11,
  cursor: 'default'
};

const StyledScrollUpButton = styled(
  SelectPrimitive.ScrollUpButton,
  scrollButtonStyles
);

const StyledScrollDownButton = styled(
  SelectPrimitive.ScrollDownButton,
  scrollButtonStyles
);

// Exports
export const Select = SelectPrimitive.Root;

export const SelectTrigger = StyledTrigger;
export const SelectSeperator = StyledSeparator;
export const SelectValue = SelectPrimitive.Value;
export const SelectIcon = StyledIcon;
export const SelectContent = Content;
export const SelectViewport = StyledViewport;
export const SelectItem = StyledItem;
export const SelectItemText = SelectPrimitive.ItemText;
export const SelectItemIndicator = StyledItemIndicator;
export const SelectScrollUpButton = StyledScrollUpButton;
export const SelectScrollDownButton = StyledScrollDownButton;

export const SelectInputField = ({
  options,
  defaultOption,
  className,
  onChange,
  register,
  setValue,
  name,
  placeholder,
  disabled = false
}: {
  options: any;
  defaultOption: any;
  className?: string;
  onChange?: any;
  register?: any;
  setValue?: any;
  name: string;
  placeholder?: string;
  disabled?: boolean;
}) => {
  const arr = options?.length > 1 ? options : ['n.a.'];

  useEffect(() => {
    setValue(name, defaultOption);
  }, [setValue, name, defaultOption]);
  return (
    <div className={cn('cursor-pointer', className)}>
      <Select
        defaultValue={defaultOption.name}
        onValueChange={onChange}
        disabled={disabled}
        {...register}
      >
        <SelectTrigger aria-label="Food">
          <SelectValue placeholder={placeholder || 'select a stream'} />
          <SelectIcon>
            <ChevronDownIcon />
          </SelectIcon>
        </SelectTrigger>
        <SelectContent>
          <SelectScrollUpButton>
            <ChevronUpIcon />
          </SelectScrollUpButton>
          <SelectViewport>
            {arr?.map((option, index) => {
              return (
                <div key={index}>
                  <SelectItem key={option.index} value={option.id}>
                    <SelectItemIndicator>
                      <CheckIcon />
                    </SelectItemIndicator>
                    <SelectItemText>{option.label}</SelectItemText>
                  </SelectItem>
                </div>
              );
            })}
          </SelectViewport>
          <SelectScrollDownButton>
            <ChevronDownIcon />
          </SelectScrollDownButton>
        </SelectContent>
      </Select>
    </div>
  );
};
