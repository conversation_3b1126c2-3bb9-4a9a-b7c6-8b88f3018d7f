// components/ConfettiComponent.tsx
import React, { useEffect } from 'react';
import confetti from 'canvas-confetti';

type ConfettiProps = {
  isVisible: boolean;
};

export const ConfettiComponent: React.FC<ConfettiProps> = ({ isVisible }) => {
  useEffect(() => {
    if (isVisible) {
      const count = 200;
      const defaults = {
        origin: { y: 0.7, x: 0.4 } // Shifted more to the left
      };

      function fire(particleRatio: number, opts: object) {
        confetti(
          Object.assign({}, defaults, opts, {
            particleCount: Math.floor(count * particleRatio)
          })
        );
      }

      fire(0.25, {
        spread: 26,
        startVelocity: 55
      });
      fire(0.2, {
        spread: 60
      });
      fire(0.1, {
        spread: 100,
        startVelocity: 25,
        decay: 0.5,
        scalar: 0.5
      });
      fire(0.1, {
        spread: 120,
        startVelocity: 45
      });
    }
  }, [isVisible]);

  return null;
};