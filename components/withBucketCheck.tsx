import { useRouter } from 'next/router';
import { useAppContext } from '@/context/AppContext';
import { useEffect } from 'react';

export const withBucketCheck = (Component) => {
  const WithBucketCheckComponent = () => {
    const context = useAppContext();
    const router = useRouter();

    const bucketName = context.activeBucketName;

    // Redirect if bucketName is undefined or empty
    useEffect(() => {
      if (!bucketName) {
        router.push('/app/pipelines');
      }
    }, [bucketName, router]);

    // Render Component only if bucketName is valid
    return bucketName ? <Component /> : null;
  };

  WithBucketCheckComponent.displayName = `WithBucketCheck(${getDisplayName(
    Component
  )})`;

  return WithBucketCheckComponent;
};

// Helper function to get the display name of a component
function getDisplayName(Component) {
  return Component.displayName || Component.name || 'Component';
}
