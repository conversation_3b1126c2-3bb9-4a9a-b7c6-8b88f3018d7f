import { memo } from 'react';

interface ResponseData {
  success: boolean;
  message?: string;
  error?: string;
  data?: {
    email: string;
    name: string;
    timestamp: string;
    grafanaStatus: string;
    credentials?: {
      username: string;
      password: string;
      organization: string;
      dashboardUrl: string;
    };
  };
}

interface GrafanaResponseProps {
  responseData: ResponseData | null;
  key?: string; // Add key prop for forcing re-render
}

export const GrafanaResponse = memo(function GrafanaResponse({ 
  responseData 
}: GrafanaResponseProps) {
  if (!responseData) return null;
  
  const bgColor = responseData.success 
    ? responseData.message === 'You are already registered in Grafana!' 
      ? 'bg-blue-900'
      : 'bg-green-900'
    : 'bg-red-900';
  
  return (
    <div className={`mt-4 p-4 ${bgColor} rounded-md`}>
      {responseData.message && (
        <div className="mb-2 text-white">
          {responseData.message === 'You are already registered in Grafana!' ? (
            <>
              <div className="font-semibold">Already Registered</div>
              <div>You are already registered in Grafana. You can proceed to the next step.</div>
            </>
          ) : responseData.success ? (
            <>
              <div className="font-semibold">Check your inbox!</div>
              <div>We've sent you an email with your Grafana credentials. Please check your inbox and then come back to continue.</div>
            </>
          ) : (
            responseData.message
          )}
        </div>
      )}
      
      {responseData.success && responseData.data?.credentials && (
        <div className="mt-4 p-4 bg-gray-800 rounded-md">
          <h3 className="text-white text-lg font-semibold mb-3">Your Grafana Credentials</h3>
          <div className="space-y-2">
            <div className="flex items-center">
              <span className="text-gray-300 w-32">Username:</span>
              <span className="text-white font-mono">{responseData.data.credentials.username}</span>
            </div>
            <div className="flex items-center">
              <span className="text-gray-300 w-32">Password:</span>
              <span className="text-white font-mono">{responseData.data.credentials.password}</span>
            </div>
            <div className="flex items-center">
              <span className="text-gray-300 w-32">Organization:</span>
              <span className="text-white">{responseData.data.credentials.organization}</span>
            </div>
            <div className="flex items-center">
              <span className="text-gray-300 w-32">Dashboard URL:</span>
              <a 
                href={responseData.data.credentials.dashboardUrl} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-400 hover:text-blue-300 underline"
              >
                {responseData.data.credentials.dashboardUrl}
              </a>
            </div>
          </div>
          <div className="mt-4 text-yellow-300 text-sm">
            Please save these credentials securely. You will need them to log in to your Grafana dashboard.
          </div>
        </div>
      )}
      
      {!responseData.success && (
        <pre className="text-white whitespace-pre-wrap break-words font-mono text-sm">
          {JSON.stringify(responseData, null, 2)}
        </pre>
      )}
    </div>
  );
});

GrafanaResponse.displayName = 'GrafanaResponse';
