import { useUser as useClerkUser } from '@clerk/nextjs';
import { useState } from 'react';
import { GrafanaButton } from './GrafanaButton';
import { GrafanaResponse } from './GrafanaResponse';

export function GrafanaSetup() {
  const [isLoading, setIsLoading] = useState(false);
  const [isGrafanaSetup, setIsGrafanaSetup] = useState(false);
  const [responseData, setResponseData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const { user } = useClerkUser();

  const username = user?.firstName || '';

  const setupGrafanaUser = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/grafana/create-grafana-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: user?.primaryEmailAddress?.emailAddress || '<EMAIL>',
          name: username || user?.id || 'random-test'
        })
      });

      const data = await response.json();
      
      if (!response.ok) {
        if (response.status === 409) {
          // User already exists in Grafana
          setIsGrafanaSetup(true);
          setResponseData({ 
            success: true,
            message: 'You are already registered in Grafana!'
          });
          return;
        }
        console.error('Grafana setup failed:', {
          status: response.status,
          statusText: response.statusText,
          data
        });
        throw new Error(data.error || 'Failed to create Grafana user');
      }
      
      console.log('Grafana user created:', data);
      setResponseData(data);
      setIsGrafanaSetup(true);
    } catch (error) {
      console.error('Error creating Grafana user:', error);
      setError(error instanceof Error ? error.message : 'Failed to create Grafana user');
      setIsGrafanaSetup(false);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full mb-8 relative">
      <GrafanaButton 
        onClick={setupGrafanaUser}
        isLoading={isLoading}
        isGrafanaSetup={isGrafanaSetup}
      />
      {error && (
        <div className="mt-4 p-4 bg-red-900 rounded-md text-white">
          <div className="font-semibold">Error:</div>
          <div>{error}</div>
        </div>
      )}
      {responseData && !error && (
        <div className={`mt-4 p-4 rounded-md text-white ${
          responseData.message === 'You are already registered in Grafana!' 
            ? 'bg-blue-900' 
            : responseData.success 
              ? 'bg-green-900' 
              : 'bg-red-900'
        }`}>
          <div className="font-semibold">
            {responseData.message === 'You are already registered in Grafana!' 
              ? 'Already Registered' 
              : responseData.success 
                ? 'Check your inbox!' 
                : 'Error'}
          </div>
          <div>
            {responseData.message === 'You are already registered in Grafana!' 
              ? 'You are already registered in Grafana. You can proceed to the next step.'
              : responseData.success 
                ? 'We\'ve sent you an email with your Grafana credentials. Please check your inbox and then come back to continue.'
                : responseData.message}
          </div>
        </div>
      )}
      <GrafanaResponse responseData={responseData} />
    </div>
  );
}
