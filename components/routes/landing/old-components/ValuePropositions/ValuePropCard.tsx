// import React from 'react';

// interface ValuePropositionColumnProps {
//   icon: React.ReactNode;
//   label: string;
//   title: string;
//   text: string;
// }

// export const ValuePropCard: React.FC<ValuePropositionColumnProps> = ({
//   icon,
//   label,
//   title,
//   text
// }) => {
//   return (
//     <div className="p-6 text-left text-lg border-black border shadow-lg transition-transform transform hover:shadow-2xl">
//       {icon}
//       <h3 className="text-blue-900 mt-6 font-medium">{label}</h3>
//       <h3 className="text-black my-4 font-bold">{title}</h3>
//       <p className="text-[18px]">{text}</p>
//     </div>
//   );
// };
