// import React from 'react';
// import { LpTitle } from '../TitleLp';
// import { PricingButton } from './PricingButton';
// import { ContainerWrapperLp } from '../../components/ContainerWrapperLp';
// import { useRouter } from 'next/router';
// import { SubTitleLp } from '../SubtitleLp';

// // Reusable column component
// interface PricingColumnProps {
//   title: string;
//   description: string[];
//   price: string;
//   details: string[];
//   buttonLabel: string;
// }

// const PricingColumn: React.FC<PricingColumnProps> = ({
//   title,
//   description,
//   price,
//   details,
//   buttonLabel
// }) => {
//   const router = useRouter();
//   return (
//     <div className="bg-black text-white p-8">
//       <h3 className="text-3xl font-bold mb-4">{title}</h3>
//       {description.map((desc, index) => (
//         <p className="leading-[18px]" key={index}>
//           {desc}
//         </p>
//       ))}
//       <p className="text-3xl my-4">{price}</p>
//       {details.map((detail, index) => (
//         <p className="leading-[18px]" key={index}>
//           {detail}
//         </p>
//       ))}
//       <PricingButton
//         text={buttonLabel}
//         onClick={() => router.push('/api/auth/login')}
//       />
//     </div>
//   );
// };

// export const PricingSectionLandingPage: React.FC = () => {
//   return (
//     <div className="bg-white max-sm:py-24 max-sm:pt-12">
//       <ContainerWrapperLp className="bg-white text-black pb-20 text-[15px] md:px-[72px] pt-14">
//         <LpTitle>Transparent pricing for companies and developers</LpTitle>
//         <SubTitleLp
//           className="mb-6"
//           text="We give you straightforward pricing, with no hidden fees"
//         />
//         <div className="grid md:grid-cols-2 gap-8 text-center">
//           <PricingColumn
//             title="Developers"
//             description={[
//               'Developer storage platform',
//               'for hobbyists and early-stage startups'
//             ]}
//             price="from $0 / month"
//             details={[
//               'For 25 GB in the Free tier',
//               'and metered pricing at $0.01 / 1 GB',
//               'for additional storage'
//             ]}
//             buttonLabel="Start building for free"
//           />
//           <PricingColumn
//             title="Businesses"
//             description={[
//               'Best for teams and projects',
//               'that need advanced features'
//             ]}
//             price="from $90 / month"
//             details={[
//               'For every 250 GB of storage',
//               'Deploy inside your own cloud infrastructure',
//               'Admin roles and dedicated support'
//             ]}
//             buttonLabel="Start 14 day free trial"
//           />
//         </div>
//       </ContainerWrapperLp>
//     </div>
//   );
// };
