// import React, { Suspense, useRef, useState, useEffect } from 'react';
// import cn from 'clsx';

// export function LottieFile({
//   isVisible,
//   className
// }: {
//   isVisible: boolean;
//   className?: string;
// }) {
//   const lottieRef = useRef(null);
//   const [lottie, setLottie] = useState(null);

//   useEffect(() => {
//     import('lottie-web').then((Lottie) => setLottie(Lottie.default));
//   }, []);

//   useEffect(() => {
//     if (lottie && lottieRef.current) {
//       const animation = lottie.loadAnimation({
//         container: lottieRef.current,
//         renderer: 'svg',
//         loop: true,
//         autoplay: true,
//         // path to your animation file, place it inside public folder
//         path: '/assets/animations/multiplayer-lottie.json'
//       });
//       animation.setSpeed(0.75);

//       return () => animation.destroy();
//     }
//   }, [lottie]);

//   if (isVisible) {
//     return (
//       <div className={cn('hidden min-h-[70px] md:min-h-[200px]', className)}>
//         <Suspense
//           fallback={
//             <img
//               className="rounded-lg shadow-xl ring-1 ring-black ring-opacity-5 md:max-w-[70vw] m-auto"
//               src="assets/product-screenshot-v2.png"
//               alt=""
//             />
//           }
//         >
//           <div
//             className="rounded-lg shadow-xl ring-1 ring-black ring-opacity-5 md:max-w-[80vw] m-auto fadeIn"
//             ref={lottieRef}
//           />
//         </Suspense>
//       </div>
//     );
//   } else return <div />;
// }
