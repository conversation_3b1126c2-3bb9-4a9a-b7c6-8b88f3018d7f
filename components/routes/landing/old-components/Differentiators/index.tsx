// import React from 'react';
// import { LpTitle } from '../TitleLp';
// import { arrayOfDifferentiators } from './constants';
// import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
// import { ContainerWrapperLp } from '../../components/ContainerWrapperLp';
// import { SubTitleLp } from '../SubtitleLp';

// export function Differentiators() {
//   return (
//     <ContainerWrapperLp className="bg-black py-20 pb-28 text-[18px] max-md:py-20">
//       <LpTitle className="text-white">
//         What makes Netrunner different from other storage services?
//       </LpTitle>
//       <SubTitleLp
//         className="text-[#90959D] mb-6"
//         text=" Whether you’re just starting out or you’re well down the path,
//         Netrunner is the fastest, most secure way to work with storage in your
//         applications"
//       />
//       <div className="grid md:grid-cols-3 gap-6">
//         {arrayOfDifferentiators.map((prop, index) => (
//           <div key={index} className="p-7 text-left text-lg bg-[#1E1D1E]">
//             <div className="h-11 w-11 bg-[#2D2D2D] rounded-[5px] flex justify-center items-center">
//               <FontAwesomeIcon
//                 className="h-7 w-7 text-brandBlue font-medium"
//                 icon={prop.icon}
//               />
//             </div>
//             <h3 className="my-5 font-medium text-white">{prop.title}</h3>
//             <p className="text-[#90959D] text-[18px]">{prop.text}</p>
//           </div>
//         ))}
//       </div>
//     </ContainerWrapperLp>
//   );
// }
