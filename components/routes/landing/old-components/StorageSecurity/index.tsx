// import Image from 'next/image';
// import { ContainerWrapperLp } from '../../components/ContainerWrapperLp';
// import { SubTitleLp } from '../SubtitleLp';
// import { LpTitle } from '../TitleLp';

// export function StorageSecurityScreenshot() {
//   return (
//     <div className="relative bg-white pt-[70px]">
//       <ContainerWrapperLp>
//         <LpTitle className="text-black">
//           Manage your storage security posture
//         </LpTitle>
//         <SubTitleLp
//           className="text-black"
//           text="Netrunner's storage security management platform combines visibility into where sensitive data is stored, who has access to that data, how it has been used, and the ability to increase the security of the stored data itself"
//         />
//         <Image
//           className="rounded-md max-w-full h-full"
//           src="/assets/security-posture-screenshot.png"
//           alt=""
//           width={1200}
//           height={500}
//         />
//       </ContainerWrapperLp>
//     </div>
//   );
// }
