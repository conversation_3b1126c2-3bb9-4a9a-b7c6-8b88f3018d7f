// import Image from 'next/image';
// import { ContainerWrapperLp } from '../components/ContainerWrapperLp';
// import { SubTitleLp } from '../components/SubtitleLp';
// import { LpTitle } from '../components/TitleLp';

// export function DevexScreenshot({ isVisible }: { isVisible: boolean }) {
//   if (!isVisible) {
//     return null;
//   }

//   return (
//     <div className="relative bg-white pt-[50px]">
//       <ContainerWrapperLp>
//         <LpTitle className="text-black">
//           Instant APIs and code snippets that just work
//         </LpTitle>
//         <SubTitleLp
//           className="my-6 mb-8 text-black " // text-[#90959D]
//           text="Use object storage APIs that are designed to provide developers with code that simply works. Say goodbye to complex implementation and hello to a seamless and efficient development experience"
//         />
//         <Image
//           className="rounded-md max-w-full h-full"
//           src="/assets/product-screenshot-workspace.png"
//           alt=""
//           width={1200}
//           height={500}
//         />
//       </ContainerWrapperLp>
//     </div>
//   );
// }
