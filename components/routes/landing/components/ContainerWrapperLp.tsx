import React from 'react';
import cn from 'clsx';

interface ContainerWrapperLpProps {
  children: React.ReactNode;
  className?: string;
}

export const ContainerWrapperLp: React.FC<ContainerWrapperLpProps> = ({
  children,
  className
}) => {
  return (
    <div
      className={cn(
        `md:px-[72px] max-w-[1200px] mx-auto`,
        'max-md:p-4',
        'max-sm:p-6]',

        className
      )}
    >
      {children}
    </div>
  );
};
