import React, { useState } from 'react';
import VideoModal from './VideoModal'; // Path to VideoModal component

const VideoThumbnail: React.FC = () => {
  const [isOpen, setOpen] = useState(false);

  return (
    <div className="relative flex justify-center items-center mt-12 px-4 sm:px-10">
      <img
        className="w-[1050px] h-[500px] max-sm:w-full max-sm:h-[300px] rounded-2xl max-sm:rounded-md object-cover"
        alt="Video thumbnail"
        src="/assets/tracer-product-preview-july-2024.webp"
      />

      <VideoModal isOpen={isOpen} onClose={() => setOpen(false)} />
    </div>
  );
};

export default VideoThumbnail;
