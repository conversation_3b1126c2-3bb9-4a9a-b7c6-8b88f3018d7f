import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheck } from '@fortawesome/free-solid-svg-icons';
import { ProceedButton } from './ProceedButton';
import { useRouter } from 'next/router';
import { useUser } from '@clerk/nextjs';

export const LoginSection: React.FC = () => {
  const router = useRouter();
  const user = useUser();

  return (
    <div className="w-full md:w-[40%] p-8 pt-12 bg-[#1a1a1a] border-r border-gray-800 relative">
      {/* Main content */}
      <div className="mt-20"> {/* Increased from mt-8 to mt-24 */}
        <h1 className="text-white text-3xl font-bold">
          Connect to Tracer
        </h1>

        <p className="text-lg md:text-xl mt-2 mb-8">
          <span className="text-white">Start observing by following just </span>
          <span className="text-[#4EC6B0]">5 simple steps</span>
        </p>
        <ProceedButton onClick={() => user.isSignedIn ? router.push('/grafana') : router.push('/sign-in')} text={user.isSignedIn ? 'Proceed to onboarding' : 'Proceed to sign in'} />
        {/* Feature list */}
        <div className="mt-12">
          <div className="flex items-center mb-4">
            <FontAwesomeIcon icon={faCheck} className="text-[#4EC6B0] text-xs mr-2" />
            <span className="text-white text-sm">
              Completely free - no hidden costs or trial limits
            </span>
          </div>

          <div className="flex items-center mb-4">
            <FontAwesomeIcon icon={faCheck} className="text-[#4EC6B0] text-xs mr-2" />
            <span className="text-white text-sm">
              Run your own pipelines in under 5 minutes
            </span>
          </div>

          <div className="flex items-center">
            <FontAwesomeIcon icon={faCheck} className="text-[#4EC6B0] text-xs mr-2" />
            <span className="text-white text-sm">
              Deploy anywhere - local or to the cloud via AWS, Azure or Google Cloud
            </span>
          </div>
        </div>
      </div>

      {/* Tech stack icons in the middle of the panel */}
      <div className="mt-24 flex justify-center items-center px-4">
        {/* Using a grid for more consistent spacing */}
        <div className="grid grid-cols-6 gap-2">
          <img 
            src="/assets/icons/tech-icon-1.png" 
            alt="AWS" 
            className="h-12 w-auto object-contain" 
            style={{ 
              marginLeft: '8px',
              transform: 'translateY(4px)' // Move down by 3px
            }}
          />
          <img
            src="/assets/icons/tech-icon-2.png"
            alt="Docker"
            className="h-9 w-auto object-contain mr-4"
            style={{ 
              filter: 'drop-shadow(0 0 0 transparent)',
              transform: 'translateY(7px) translateX(-2px)'  // Move down by 7px and left by 2px
            }}
          />
          <img src="/assets/icons/tech-icon-3.png" alt="Kubernetes" className="h-12 w-auto object-contain" />
          <img 
            src="/assets/icons/tech-icon-4.png" 
            alt="Terraform" 
            className="h-12 w-auto object-contain" 
            style={{ transform: 'translateX(3px)' }} // Move right by 3px
          />
          <img src="/assets/icons/tech-icon-5.png" alt="Python" className="h-12 w-auto object-contain" />
          <img src="/assets/icons/tech-icon-6.png" alt="Next.js" className="h-12 w-auto object-contain" />
        </div>
      </div>

      {/* Copyright text */}
      <div className="absolute bottom-2 left-0 right-0 text-center text-gray-500 text-sm">
        © 2025 Tracer Biotech Inc., All rights reserved.
      </div>
    </div>
  );
};
