import { CAccent } from '@/components/ui/CAccent';
import cn from 'clsx';
import { useDelayedFadeIn } from 'hooks/useDelayedFadeIn';
import { Card } from './Card';
import { faArrowRight, faPlay } from '@fortawesome/free-solid-svg-icons';
import { useUser } from '@clerk/nextjs';
import { useEffect, useState } from 'react';

function ImgFadeIn({ isFadeInReady }) {
  return (
    <div
      className={cn(
        'cursor-pointer opacity-0',
        isFadeInReady && 'opacity-100 fadeIn2s'
      )}
    ></div>
  );
}

function BrandImage() {
  return (
    <div className="md:col-span-4 md:col-start-9 lg:col-span-5 lg:col-start-8 flex justify-end relative max-[900px]:hidden ">
      <img
        src="https://www.highvail.com/wp-content/uploads/2020/04/AWS-Logo-halfwhite.png"
        className="lg:max-h-[370px] xl:max-h-[200px] pr-[35%] pt-[70px]"
        alt="hero screenshot of the demo"
      />
      <div className="absolute inset-0 bg-gradient-to-tr scale-y-125 from-black to-transparent"></div>
    </div>
  );
}

function GridColumnWrapper({ children }) {
  const [colSpan, setColSpan] = useState(6); // default value

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 900) {
        setColSpan(10);
      } else if (window.innerWidth > 900) {
        setColSpan(6);
      } else {
        setColSpan(7);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div
      className={cn(
        `col-start-2 md:col-start-2 grid sm:grid-cols-2 sm:gap-3 lg:gap-6 grid-auto-rows`,
        colSpan === 10 && 'col-span-10',
        colSpan === 7 && 'col-span-7',
        colSpan === 6 && 'col-span-6'
      )}
    >
      {children}
    </div>
  );
}

function HeroContent() {
  const { isSignedIn } = useUser();
  const isFadeInReady = useDelayedFadeIn(500);
  // const ctaPathStorage = isSignedIn ? '/app/pipelines' : '/sign-in';
  const ctaPathStorage = isSignedIn ? '/grafana' : '/sign-in';

  return (
    <div className="grid grid-cols-12 sm:mt-8">
      <GridColumnWrapper>
        <Card
          chipText="NEW"
          title="Bioinformatics Linux data collection agent"
          content="Extract process information directly from the Linux Kernel to pinpoint issues when no logs are available"
          buttonLabel="Get started"
          buttonType="blue"
          icon={faPlay}
          path={ctaPathStorage}
        />
        <div
          className={cn('opacity-0', isFadeInReady && 'opacity-100 fadeIn2s')}
        >
          <Card
            chipText="BETA"
            title="Proprietary error database to fix issues fast"
            content="Tracer monitors your pipelines for anomalies, independent of workflow language"
            buttonLabel="Learn more"
            buttonType="gray"
            path=""
            icon={faArrowRight}
            isIconVisible={true}
            // path="https://cloudchronicles.substack.com/p/cloud-chronicles-8-using-ai-to-bridge"
          />
        </div>
      </GridColumnWrapper>

      <BrandImage />
    </div>
  );
}

function HeroTitle() {
  return (
    <div className="grid grid-cols-12 lg:gap-8 pt-4 max-sm:py-6 bg-black text-white relative">
      <div className="col-start-2 col-span-10 mb-4">
        <h1 className="tracking-tight font-bold text-4xl sm:text-5xl">
          {/* Error Monitoring for Bioinformatics and AI Pipelines */}
          Error Monitoring On AWS
          {/* Integrate AWS S3 into Next.js */}
          <CAccent className={'mt-1'}>
            Pipelines Break. Tracer Fixes Them
          </CAccent>
        </h1>
      </div>
    </div>
  );
}

export function Hero() {
  const isFadeInReady = useDelayedFadeIn(2500);

  return (
    <div className="relative overflow-hidden">
      <HeroTitle />
      <HeroContent />
      <ImgFadeIn isFadeInReady={isFadeInReady} />
    </div>
  );
}
