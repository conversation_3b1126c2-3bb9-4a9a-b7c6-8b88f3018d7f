import React, { useState } from 'react';

export const DocumentationColumn: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('install');

  const tabs = [
    { id: 'install', label: 'Install Tracer' },
    { id: 'pipelines', label: 'Run Pipelines' },
    { id: 'dashboards', label: 'Login Dashboards' }
  ];

  return (
    <div className="documentation-column">
      {/* Preview Header */}
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-4 text-white flex items-center">
          <span className="mr-2">👁</span>
          Preview
        </h2>

        {/* Tab Navigation */}
        <div className="bg-gray-900 rounded-lg">
          <div className="px-6 pt-6 pb-0">
            <div className="flex space-x-8 border-b border-gray-700">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`pb-3 px-1 text-base font-medium transition-colors duration-200 relative ${
                    activeTab === tab.id
                      ? 'text-white'
                      : 'text-white hover:text-gray-300'
                  }`}
                >
                  {tab.label}
                  {activeTab === tab.id && (
                    <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-500"></div>
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'install' && (
              <div className="prose prose-invert max-w-none">
                <h3 className="text-lg font-semibold text-white mb-3">Installation Guide</h3>
                <p className="text-gray-400 mb-4">
                  Welcome to the Tracer installation guide. Follow the steps on the left to get started.
                </p>

                <h4 className="text-md font-semibold text-white mb-2">Prerequisites</h4>
                <ul className="text-gray-400 mb-4 list-disc list-inside">
                  <li>Node.js 18+ installed</li>
                  <li>Docker (for Docker installation)</li>
                  <li>Git for version control</li>
                </ul>

                <h4 className="text-md font-semibold text-white mb-2">Quick Start</h4>
                <p className="text-gray-400">
                  Choose your preferred installation method from the options on the left to begin.
                </p>
              </div>
            )}

            {activeTab === 'pipelines' && (
              <div className="prose prose-invert max-w-none">
                <h3 className="text-lg font-semibold text-white mb-3">Run Pipelines</h3>
                <p className="text-gray-400 mb-4">
                  Learn how to run and manage your Tracer pipelines.
                </p>
                <p className="text-gray-400">
                  Pipeline content will be displayed here.
                </p>
              </div>
            )}

            {activeTab === 'dashboards' && (
              <div className="prose prose-invert max-w-none">
                <h3 className="text-lg font-semibold text-white mb-3">Login Dashboards</h3>
                <p className="text-gray-400 mb-4">
                  Access and configure your Tracer dashboards.
                </p>
                <p className="text-gray-400">
                  Dashboard content will be displayed here.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
