import React from 'react';

export const DocumentationColumn: React.FC = () => {
  return (
    <div className="documentation-column">
      <h2 className="text-xl font-semibold mb-4 text-gray-300">Documentation column</h2>
      <div className="bg-gray-900 p-6 rounded-lg min-h-[400px]">
        <div className="prose prose-invert max-w-none">
          <h3 className="text-lg font-semibold text-white mb-3">Installation Guide</h3>
          <p className="text-gray-400 mb-4">
            Welcome to the Tracer installation guide. Follow the steps on the left to get started.
          </p>
          
          <h4 className="text-md font-semibold text-white mb-2">Prerequisites</h4>
          <ul className="text-gray-400 mb-4 list-disc list-inside">
            <li>Node.js 18+ installed</li>
            <li>Docker (for Docker installation)</li>
            <li>Git for version control</li>
          </ul>
          
          <h4 className="text-md font-semibold text-white mb-2">Quick Start</h4>
          <p className="text-gray-400">
            Choose your preferred installation method from the options on the left to begin.
          </p>
        </div>
      </div>
    </div>
  );
};
