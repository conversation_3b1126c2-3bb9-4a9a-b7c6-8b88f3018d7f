import React from 'react';

interface StepSectionProps {
  children: React.ReactNode;
  stepNumber?: string;
  stepTitle?: string;
  stepDescription?: string;
}

// StepSection: wraps each step's title and content with left border accent
export const StepSection: React.FC<StepSectionProps> = ({
  children,
  stepNumber = "Step 1",
  stepTitle = "Environment",
  stepDescription = "Choose the environment Tracer will run in."
}) => {
  return (
    <div className="step-section bg-gray-900 border-l-4 border-white p-8 rounded-lg">
      {/* Step indicator and title */}
      <div className="mb-8">
        <div className="text-sm text-white font-medium uppercase tracking-wider mb-3">
          {stepNumber}
        </div>
        <h2 className="text-5xl font-bold text-white mb-4">
          {stepTitle}
        </h2>
        <p className="text-gray-400 text-lg leading-relaxed">
          └ {stepDescription}
        </p>
      </div>

      {/* Step content */}
      {children}
    </div>
  );
};
