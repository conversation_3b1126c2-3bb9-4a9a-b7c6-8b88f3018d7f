import React from 'react';

interface StepSectionProps {
  children: React.ReactNode;
  title?: string;
}

// StepSection: wraps each step's title and content
export const StepSection: React.FC<StepSectionProps> = ({ children, title }) => {
  return (
    <div className="step-section mb-6">
      {title && (
        <h3 className="text-lg font-semibold mb-4 text-white">{title}</h3>
      )}
      {children}
    </div>
  );
};
