import React from 'react';

interface StepSectionProps {
  children: React.ReactNode;
  stepNumber?: string;
  stepTitle?: string;
  stepDescription?: string;
  useBulletPoint?: boolean;
  disabled?: boolean;
}

// StepSection: wraps each step's title and content with left border accent
export const StepSection: React.FC<StepSectionProps> = ({
  children,
  stepNumber = "Step 1",
  stepTitle = "Environment",
  stepDescription = "Choose the environment Tracer will run in.",
  useBulletPoint = true,
  disabled = false
}) => {
  return (
    <div className={`step-section border-l-4 p-8 rounded-lg ${
      disabled
        ? 'bg-gray-950 border-gray-700 opacity-60'
        : 'bg-gray-900 border-white'
    }`}>
      {/* Step indicator and title */}
      <div className="mb-8">
        <div className={`text-sm font-medium uppercase tracking-wider mb-3 ${
          disabled ? 'text-gray-500' : 'text-white'
        }`}>
          {stepNumber}
        </div>
        <h2 className={`text-5xl font-bold mb-4 ${
          disabled ? 'text-gray-600' : 'text-white'
        }`}>
          {stepTitle}
        </h2>
        <p className={`text-lg leading-relaxed ${
          disabled ? 'text-gray-600' : 'text-gray-400'
        }`}>
          {useBulletPoint ? '└ ' : ''}{stepDescription}
        </p>
      </div>

      {/* Step content */}
      <div className={disabled ? 'pointer-events-none' : ''}>
        {children}
      </div>
    </div>
  );
};
