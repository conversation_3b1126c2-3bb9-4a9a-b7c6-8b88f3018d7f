import React from 'react';
import Head from 'next/head';
import { StepsColumn } from './StepsColumn';
import { DocumentationColumn } from './DocumentationColumn';

export default function OnboardingInstallationPage() {
  return (
    <>
      <Head>
        <title>Welcome to Tracer - Setup</title>
        <meta name="description" content="Let's get you set up with your preferred tech stack" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className="min-h-screen bg-black text-white">
        <div className="container mx-auto px-4 py-8">
          {/* Header / Title Area */}
          <div className="mb-12">
            <h1 className="text-6xl font-bold text-white mb-6 tracking-tight">Welcome to Tracer</h1>
            <p className="text-2xl text-gray-400">Let's get you set up with your preferred tech stack.</p>
          </div>

          {/* CSS Grid layout: single column on mobile, two columns on desktop */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left column - Steps */}
            <div className="col-span-1">
              <StepsColumn />
            </div>

            {/* Right column - Documentation */}
            <div className="col-span-1">
              <DocumentationColumn />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
