import React from 'react';
import Head from 'next/head';
import { StepsColumn } from './StepsColumn';
import { DocumentationColumn } from './DocumentationColumn';

export default function OnboardingInstallationPage() {
  return (
    <>
      <Head>
        <title>Onboarding Installation - Tracer</title>
        <meta name="description" content="Install and configure Tracer on your system" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className="min-h-screen bg-black text-white">
        <div className="container mx-auto px-4 py-8">
          <h1 className="text-3xl font-bold mb-8">Onboarding Installation</h1>

          {/* CSS Grid layout: single column on mobile, two columns on desktop */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left column - Steps */}
            <div className="col-span-1">
              <StepsColumn />
            </div>

            {/* Right column - Documentation */}
            <div className="col-span-1">
              <DocumentationColumn />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
