import React from 'react';
import { StepSection } from './StepSection';
import { OptionCard } from './OptionCard';

export const StepsColumn: React.FC = () => {
  return (
    <div className="steps-column">
      <h2 className="text-xl font-semibold mb-4 text-gray-300">Steps column</h2>
      <div className="bg-gray-900 p-6 rounded-lg min-h-[400px]">
        <StepSection title="Step 1: Choose Installation Method">
          <div className="grid grid-cols-1 gap-4">
            <OptionCard label="Docker Installation">
              <p className="text-gray-400">Install using Docker containers</p>
            </OptionCard>
            <OptionCard label="Manual Installation">
              <p className="text-gray-400">Install directly on your system</p>
            </OptionCard>
          </div>
        </StepSection>
        
        <StepSection title="Step 2: Configuration">
          <OptionCard label="Basic Configuration">
            <p className="text-gray-400">Set up basic configuration options</p>
          </OptionCard>
        </StepSection>
      </div>
    </div>
  );
};
