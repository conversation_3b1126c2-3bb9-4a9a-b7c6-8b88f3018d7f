import React, { useState } from 'react';
import { Github, Cloud, Monitor } from 'lucide-react';
import { StepSection } from './StepSection';
import { OptionCard } from './OptionCard';

export const StepsColumn: React.FC = () => {
  const [selectedEnvironment, setSelectedEnvironment] = useState<string>('demo');

  const handleEnvironmentSelect = (environment: string) => {
    setSelectedEnvironment(environment);
  };

  return (
    <div className="steps-column">
      <StepSection
        stepNumber="Step 1"
        stepTitle="Environment"
        stepDescription="Choose the environment Tracer will run in."
      >
        {/* Three Environment Choice Cards */}
        <div className="grid grid-cols-3 gap-6">
          <OptionCard
            icon={<Github size={40} />}
            label="Demo Environment"
            multiLineLabel={true}
            selected={selectedEnvironment === 'demo'}
            onClick={() => handleEnvironmentSelect('demo')}
          />

          <OptionCard
            icon={<Cloud size={40} />}
            label="Cloud"
            selected={selectedEnvironment === 'cloud'}
            onClick={() => handleEnvironmentSelect('cloud')}
          />

          <OptionCard
            icon={<Monitor size={40} />}
            label="Local System"
            selected={selectedEnvironment === 'local'}
            onClick={() => handleEnvironmentSelect('local')}
          />
        </div>
      </StepSection>
    </div>
  );
};
