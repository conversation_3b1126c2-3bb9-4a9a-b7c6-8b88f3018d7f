import React, { useState } from 'react';
import { GitBranch, Cloud, Monitor, Apple, Computer, Terminal, Server, Database, Layers, Sliders, Settings } from 'lucide-react';
import { StepSection } from './StepSection';
import { OptionCard } from './OptionCard';

export const StepsColumn: React.FC = () => {
  const [selectedEnvironment, setSelectedEnvironment] = useState<string>('demo');
  const [selectedLocalSystem, setSelectedLocalSystem] = useState<string>('apple');
  const [selectedCloudProvider, setSelectedCloudProvider] = useState<string>('');
  const [selectedNodeType, setSelectedNodeType] = useState<string>('single');
  const [selectedPipeline, setSelectedPipeline] = useState<string>('demo');

  const handleEnvironmentSelect = (environment: string) => {
    setSelectedEnvironment(environment);
  };

  const handleLocalSystemSelect = (system: string) => {
    setSelectedLocalSystem(system);
  };

  const handleCloudProviderSelect = (provider: string) => {
    setSelectedCloudProvider(provider);
  };

  const handleNodeTypeSelect = (nodeType: string) => {
    setSelectedNodeType(nodeType);
  };

  const handlePipelineSelect = (pipeline: string) => {
    setSelectedPipeline(pipeline);
  };

  // Conditional logic for enabling/disabling steps and options
  const isDemoEnvironment = selectedEnvironment === 'demo';
  const isCloudEnvironment = selectedEnvironment === 'cloud';
  const isLocalEnvironment = selectedEnvironment === 'local';
  const isAwsSelected = selectedCloudProvider === 'aws';

  // Step 2 is disabled if Demo Environment is selected
  const isStep2Disabled = isDemoEnvironment;

  // Step 3 is disabled if Demo Environment is selected OR AWS is not selected
  const isStep3Disabled = isDemoEnvironment || !isAwsSelected;

  // Step 4 is disabled if Demo Environment is selected
  const isStep4Disabled = isDemoEnvironment;

  // Dynamic step numbering - when Step 3 is disabled, Step 4 becomes Step 3
  const pipelineStepNumber = isStep3Disabled ? "Step 3" : "Step 4";

  return (
    <div className="steps-column space-y-8">
      {/* Step 1: Environment */}
      <StepSection
        stepNumber="Step 1"
        stepTitle="Environment"
        stepDescription="Choose the environment Tracer will run in."
      >
        {/* Three Environment Choice Cards */}
        <div className="grid grid-cols-3 gap-6">
          <OptionCard
            icon={<GitBranch size={40} />}
            label="Demo Environment"
            multiLineLabel={true}
            selected={selectedEnvironment === 'demo'}
            onClick={() => handleEnvironmentSelect('demo')}
          />

          <OptionCard
            icon={<Cloud size={40} />}
            label="Cloud"
            selected={selectedEnvironment === 'cloud'}
            onClick={() => handleEnvironmentSelect('cloud')}
          />

          <OptionCard
            icon={<Monitor size={40} />}
            label="Local System"
            selected={selectedEnvironment === 'local'}
            onClick={() => handleEnvironmentSelect('local')}
          />
        </div>
      </StepSection>

      {/* Step 2: System */}
      <StepSection
        stepNumber="Step 2"
        stepTitle="System"
        stepDescription="Choose the exact system you will run Tracer in."
        disabled={isStep2Disabled}
      >
        {/* Cloud Providers Section */}
        <div className="mb-8">
          <h3 className={`text-base font-medium mb-4 ${isStep2Disabled ? 'text-gray-600' : 'text-white'}`}>cloud</h3>
          <div className="grid grid-cols-3 gap-6">
            <OptionCard
              icon={<Server size={40} />}
              label="Amazon AWS"
              multiLineLabel={true}
              selected={selectedCloudProvider === 'aws'}
              onClick={() => handleCloudProviderSelect('aws')}
              disabled={isStep2Disabled || isLocalEnvironment}
            />

            <OptionCard
              icon={<Cloud size={40} />}
              label="Google Cloud"
              multiLineLabel={true}
              selected={selectedCloudProvider === 'google'}
              onClick={() => handleCloudProviderSelect('google')}
              disabled={isStep2Disabled || isLocalEnvironment}
            />

            <OptionCard
              icon={<Database size={40} />}
              label="Azure"
              selected={selectedCloudProvider === 'azure'}
              onClick={() => handleCloudProviderSelect('azure')}
              disabled={isStep2Disabled || isLocalEnvironment}
            />
          </div>
        </div>

        {/* Local Systems Section */}
        <div>
          <h3 className={`text-base font-medium mb-4 ${isStep2Disabled ? 'text-gray-600' : 'text-white'}`}>local</h3>
          <div className="grid grid-cols-3 gap-6">
            <OptionCard
              icon={<Apple size={40} />}
              label="Apple"
              selected={selectedLocalSystem === 'apple'}
              onClick={() => handleLocalSystemSelect('apple')}
              disabled={isStep2Disabled || isCloudEnvironment}
            />

            <OptionCard
              icon={<Computer size={40} />}
              label="Microsoft"
              selected={selectedLocalSystem === 'microsoft'}
              onClick={() => handleLocalSystemSelect('microsoft')}
              disabled={isStep2Disabled || isCloudEnvironment}
            />

            <OptionCard
              icon={<Terminal size={40} />}
              label="Linux"
              selected={selectedLocalSystem === 'linux'}
              onClick={() => handleLocalSystemSelect('linux')}
              disabled={isStep2Disabled || isCloudEnvironment}
            />
          </div>
        </div>
      </StepSection>

      {/* Step 3: Nodes - Only render when enabled */}
      {!isStep3Disabled && (
        <StepSection
          stepNumber="Step 3"
          stepTitle="Nodes"
          stepDescription="Within AWS, you can choose to install Tracer inside your AWS Batch system"
        >
          {/* Node Type Selection Cards */}
          <div className="grid grid-cols-2 gap-6">
            <OptionCard
              icon={<Server size={40} />}
              label="Single Node"
              multiLineLabel={true}
              selected={selectedNodeType === 'single'}
              onClick={() => handleNodeTypeSelect('single')}
            />

            <OptionCard
              icon={<Layers size={40} />}
              label="Multi Node"
              multiLineLabel={true}
              selected={selectedNodeType === 'multi'}
              onClick={() => handleNodeTypeSelect('multi')}
            />
          </div>
        </StepSection>
      )}

      {/* Step 4: Pipeline */}
      <StepSection
        stepNumber="Step 4"
        stepTitle="Pipeline"
        stepDescription="Choose whether you want to run your own installed pipeline or use one of Tracer's templates"
        useBulletPoint={false}
        disabled={isStep4Disabled}
      >
        {/* Pipeline Selection Cards */}
        <div className="grid grid-cols-2 gap-6">
          <OptionCard
            icon={<Sliders size={40} />}
            label="Use Tracer Demo"
            multiLineLabel={true}
            selected={selectedPipeline === 'demo'}
            onClick={() => handlePipelineSelect('demo')}
            disabled={isStep4Disabled}
          />

          <OptionCard
            icon={<Settings size={40} />}
            label="Run Your Own"
            multiLineLabel={true}
            selected={selectedPipeline === 'own'}
            onClick={() => handlePipelineSelect('own')}
            disabled={isStep4Disabled}
          />
        </div>
      </StepSection>
    </div>
  );
};
