import React from 'react';

interface OptionCardProps {
  children: React.ReactNode;
  selected?: boolean;
  onClick?: () => void;
  icon?: React.ReactNode;
  label?: string;
}

// OptionCard: selectable card with icon + label + border states
export const OptionCard: React.FC<OptionCardProps> = ({ 
  children, 
  selected = false, 
  onClick,
  icon,
  label 
}) => {
  return (
    <div 
      className={`option-card p-4 border rounded-lg transition-colors cursor-pointer ${
        selected 
          ? 'border-blue-500 bg-blue-500/10' 
          : 'border-gray-700 hover:border-gray-500'
      }`}
      onClick={onClick}
    >
      {icon && (
        <div className="flex items-center mb-2">
          <div className="mr-3">{icon}</div>
          {label && <span className="text-white font-medium">{label}</span>}
        </div>
      )}
      {children}
    </div>
  );
};
