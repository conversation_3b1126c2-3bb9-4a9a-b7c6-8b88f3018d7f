import React from 'react';

interface OptionCardProps {
  selected?: boolean;
  onClick?: () => void;
  icon?: React.ReactNode;
  label?: string;
  multiLineLabel?: boolean;
  disabled?: boolean;
}

// OptionCard: selectable card with icon + label + border states for environment selection
export const OptionCard: React.FC<OptionCardProps> = ({
  selected = false,
  onClick,
  icon,
  label,
  multiLineLabel = false,
  disabled = false
}) => {
  return (
    <div
      className={`option-card p-8 border-2 rounded-lg transition-colors duration-200 flex flex-col items-center justify-center h-[160px] w-full ${
        disabled
          ? 'border-gray-800 bg-gray-900/50 cursor-not-allowed opacity-50'
          : selected
          ? 'border-white bg-gray-800/30 cursor-pointer'
          : 'border-gray-600 hover:border-gray-400 cursor-pointer'
      }`}
      onClick={disabled ? undefined : onClick}
    >
      {/* Icon */}
      {icon && (
        <div className={`mb-6 ${disabled ? 'text-gray-600' : 'text-white'}`}>
          {icon}
        </div>
      )}

      {/* Label */}
      {label && (
        <div className="text-center">
          {multiLineLabel ? (
            <div className={`font-medium text-base leading-tight transition-colors duration-200 ${
              disabled
                ? 'text-gray-600'
                : selected
                ? 'text-white'
                : 'text-gray-400'
            }`}>
              {label.split(' ').map((word, index) => (
                <div key={index}>{word}</div>
              ))}
            </div>
          ) : (
            <span className={`font-medium text-base transition-colors duration-200 ${
              disabled
                ? 'text-gray-600'
                : selected
                ? 'text-white'
                : 'text-gray-400'
            }`}>{label}</span>
          )}
        </div>
      )}
    </div>
  );
};
