import React from 'react';

interface OptionCardProps {
  selected?: boolean;
  onClick?: () => void;
  icon?: React.ReactNode;
  label?: string;
  multiLineLabel?: boolean;
}

// OptionCard: selectable card with icon + label + border states for environment selection
export const OptionCard: React.FC<OptionCardProps> = ({
  selected = false,
  onClick,
  icon,
  label,
  multiLineLabel = false
}) => {
  return (
    <div
      className={`option-card p-8 border rounded-lg transition-all duration-200 cursor-pointer flex flex-col items-center justify-center min-h-[160px] ${
        selected
          ? 'border-white border-2 bg-gray-800/30'
          : 'border-gray-600 hover:border-gray-400'
      }`}
      onClick={onClick}
    >
      {/* Icon */}
      {icon && (
        <div className="mb-6 text-white">
          {icon}
        </div>
      )}

      {/* Label */}
      {label && (
        <div className="text-center">
          {multiLineLabel ? (
            <div className="text-white font-medium text-base leading-tight">
              {label.split(' ').map((word, index) => (
                <div key={index}>{word}</div>
              ))}
            </div>
          ) : (
            <span className="text-white font-medium text-base">{label}</span>
          )}
        </div>
      )}
    </div>
  );
};
