import { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { faPlus } from '@fortawesome/free-solid-svg-icons';
import {
  API_METHOD_RULES,
  CATEGORY_API_METHODS,
  CATEGORY_CONNECTIONS,
  CATEGORY_CUSTOM,
  CATEGORY_DATA_ATTRIBUTES,
  CONNECTION_RULES,
  DATA_ATTRIBUTE_RULES,
  IRules
} from './rules';

// policy category types
export interface IPolicyCategory {
  label: string;
  icon: IconDefinition;
  rulesOptions: IRules;
  name:
    | typeof CATEGORY_CONNECTIONS
    | typeof CATEGORY_API_METHODS
    | typeof CATEGORY_DATA_ATTRIBUTES
    | typeof CATEGORY_CUSTOM;
}

export const POLICY_CATEGORY_DEFINITIONS: Record<
  | typeof CATEGORY_CONNECTIONS
  | typeof CATEGORY_API_METHODS
  | typeof CATEGORY_DATA_ATTRIBUTES
  | typeof CATEGORY_CUSTOM,
  IPolicyCategory
> = {
  [CATEGORY_CONNECTIONS]: {
    label: 'Connection',
    icon: faPlus,
    name: CATEGORY_CONNECTIONS,
    rulesOptions: CONNECTION_RULES
  },
  [CATEGORY_API_METHODS]: {
    label: 'API method',
    icon: faPlus,
    name: CATEGORY_API_METHODS,
    rulesOptions: API_METHOD_RULES
  },
  [CATEGORY_DATA_ATTRIBUTES]: {
    label: 'Data',
    icon: faPlus,
    name: CATEGORY_DATA_ATTRIBUTES,
    rulesOptions: DATA_ATTRIBUTE_RULES
  },
  [CATEGORY_CUSTOM]: {
    label: 'Custom',
    icon: faPlus,
    name: CATEGORY_CUSTOM,
    rulesOptions: API_METHOD_RULES
  }
};
