import { IPolicyRule } from '../policies';

// policy category definitions
export const CATEGORY_CONNECTIONS = 'CATEGORY_CONNECTIONS' as const;
export const CATEGORY_API_METHODS = 'CATEGORY_API_METHODS' as const;
export const CATEGORY_DATA_ATTRIBUTES = 'CATEGORY_DATA_ATTRIBUTES' as const;
export const CATEGORY_CUSTOM = 'CATEGORY_CUSTOM' as const;

export const CATEGORY_LABELS = {
  [CATEGORY_CONNECTIONS]: 'Permitted connections',
  [CATEGORY_API_METHODS]: 'Enabled API methods',
  [CATEGORY_DATA_ATTRIBUTES]: 'Data attribute conditions',
  [CATEGORY_CUSTOM]: 'Custom conditions'
};

export interface IRules {
  [key: string]: IPolicyRule;
}

export const API_METHOD_RULES: IRules = {
  post_object: {
    name: 'Upload object',
    ruleCategory: CA<PERSON>G<PERSON>Y_API_METHODS,
    ruleId: 'post_object',
    label: 'Allow uploading new objects'
  },
  get_object: {
    name: 'Get object',
    ruleCategory: CATEGORY_API_METHODS,
    ruleId: 'get_object',
    label: 'Allow retrieving objects'
  },
  list_objects: {
    name: 'List objects',
    ruleCategory: CATEGORY_API_METHODS,
    ruleId: 'list_objects',
    label: 'Allow listing all objects'
  },
  delete_object: {
    name: 'Delete object',
    ruleCategory: CATEGORY_API_METHODS,
    ruleId: 'delete_object',
    label: 'Allow deleting objects'
  },
  update_object: {
    name: 'Update object',
    ruleCategory: CATEGORY_API_METHODS,
    ruleId: 'update_object',
    label: 'Allow updating objects'
  }
};

export const CONNECTION_RULES: IRules = {
  localhost_3000: {
    ruleId: 'localhost_3000',
    name: 'Localhost:3000',
    ruleCategory: CATEGORY_CONNECTIONS,
    label: 'Allow connections to Localhost:3000'
  },
  localhost_XXXX: {
    ruleId: 'localhost_XXXX',
    name: 'Localhost:XXXX',
    ruleCategory: CATEGORY_CONNECTIONS,
    label: 'Allow connections to Localhost:XXXX'
  },
  nextjs_api: {
    ruleId: 'nextjs_api',
    name: 'NextJS/API',
    ruleCategory: CATEGORY_CONNECTIONS,
    label: 'Allow connections to NextJS/API'
  },
  aws_lambda: {
    ruleId: 'aws_lambda',
    name: 'AWS Lambda',
    ruleCategory: CATEGORY_CONNECTIONS,
    label: 'Allow connections to AWS Lambda'
  },
  ec2: {
    ruleId: 'ec2',
    name: 'EC2',
    ruleCategory: CATEGORY_CONNECTIONS,
    label: 'Allow connections to EC2'
  },
  vpc: {
    ruleId: 'vpc',
    name: 'VPC',
    ruleCategory: CATEGORY_CONNECTIONS,
    label: 'Allow connections to VPC'
  }
};

export const DATA_ATTRIBUTE_RULES: IRules = {
  images: {
    ruleId: 'images',
    ruleCategory: CATEGORY_DATA_ATTRIBUTES,
    name: 'Images',
    label: 'Images'
  },
  max_size_5gb: {
    ruleId: 'max_size_5gb',
    ruleCategory: CATEGORY_DATA_ATTRIBUTES,
    name: 'max_size_5gb',
    label: 'Max size 5GB'
  },
  max_size_10gb: {
    ruleId: 'max_size_10gb',
    ruleCategory: CATEGORY_DATA_ATTRIBUTES,
    name: 'max_size_10gb',
    label: 'Max size 10GB'
  },
  media_files: {
    ruleId: 'media_files',
    ruleCategory: CATEGORY_DATA_ATTRIBUTES,
    name: 'media_files',
    label: 'Media files'
  },
  all_files: {
    ruleId: 'all_files',
    ruleCategory: CATEGORY_DATA_ATTRIBUTES,
    name: 'all_files',
    label: 'All files'
  },
  pci_compliant: {
    ruleId: 'pci_compliant',
    ruleCategory: CATEGORY_DATA_ATTRIBUTES,
    name: 'pci_compliant',
    label: 'PCI compliant'
  },
  hipaa_compliant: {
    ruleId: 'hipaa_compliant',
    ruleCategory: CATEGORY_DATA_ATTRIBUTES,
    name: 'hipaa_compliant',
    label: 'HIPAA compliant'
  },
  gdpr_compliant: {
    ruleId: 'gdpr_compliant',
    ruleCategory: CATEGORY_DATA_ATTRIBUTES,
    name: 'gdpr_compliant',
    label: 'GDPR compliant'
  },
  soc2_compliant: {
    ruleId: 'soc2_compliant',
    ruleCategory: CATEGORY_DATA_ATTRIBUTES,
    name: 'soc2_compliant',
    label: 'SOC2 compliant'
  }
};
