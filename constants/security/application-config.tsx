import {
  faCloud,
  faCamera,
  faCloudUploadAlt,
  faDna,
  faDatabase
} from '@fortawesome/free-solid-svg-icons';
import { DEFAULT_SECURITY_POLICY, IPolicy } from './policies';

export interface IApplicationConfig {
  label: string;
  icon: typeof faCloud | typeof faCamera | typeof faCloudUploadAlt;
  id?: string;
  securityPolicy: IPolicy;
}

export const APPLICATION_CONFIGURATIONS: IApplicationConfig[] = [
  {
    label: 'Data lakes (Analytics & Big Data)',
    icon: faDatabase,
    id: 'data-lakes',
    securityPolicy: DEFAULT_SECURITY_POLICY
  },
  {
    label: 'Cloud storage for large batch process (AI & ML)',
    icon: faDatabase,
    id: 'ai-machine-learning',
    securityPolicy: DEFAULT_SECURITY_POLICY
  },
  {
    label: 'User photo uploads in Next.js app',
    icon: faCamera,
    id: 'user-photo-uploads',
    securityPolicy: DEFAULT_SECURITY_POLICY
  },
  {
    label: 'Single cell RNA sequencing data storage',
    icon: faDna,
    id: 'rna-sequencing',
    securityPolicy: DEFAULT_SECURITY_POLICY
  },
  {
    label: 'Cloud storage for node.js apps',
    icon: faCloud,
    id: 'nodejs-apps',
    securityPolicy: DEFAULT_SECURITY_POLICY
  },
  {
    label: 'Cloud storage for database engineering',
    icon: faDatabase,
    id: 'database-engineering',
    securityPolicy: DEFAULT_SECURITY_POLICY
  },
  {
    label: 'Cloud storage for media files',
    icon: faCloudUploadAlt,
    id: 'media-files',
    securityPolicy: DEFAULT_SECURITY_POLICY
  },
  {
    label: 'Default cloud storage template',
    icon: faCloud,
    id: 'default-cloud-storage',
    securityPolicy: DEFAULT_SECURITY_POLICY
  },
  {
    label: 'Storage for podcast episodes',
    icon: faCloud,
    id: 'podcast-episodes',
    securityPolicy: DEFAULT_SECURITY_POLICY
  },
  {
    label: 'Static website hosting',
    icon: faCloud,
    id: 'static-website-hosting',
    securityPolicy: DEFAULT_SECURITY_POLICY
  },
  {
    label: 'Cloud storage for webcontent',
    icon: faCloud,
    id: 'webcontent',
    securityPolicy: DEFAULT_SECURITY_POLICY
  }
];
