import { POLICY_CATEGORY_DEFINITIONS } from './policy-category';
import {
  CATEGORY_API_METHODS,
  CATEGORY_DATA_ATTRIBUTES,
  CATEGORY_CONNECTIONS,
  CONNECTION_RULES,
  API_METHOD_RULES,
  DATA_ATTRIBUTE_RULES
} from './rules';

export interface IPolicyRule {
  name: string;
  ruleId: string;
  ruleCategory: keyof typeof POLICY_CATEGORY_DEFINITIONS;
  label?: string;
}

export interface IPolicyStatements {
  CATEGORY_CONNECTIONS?: IPolicyRule[];
  CATEGORY_API_METHODS?: IPolicyRule[];
  CATEGORY_DATA_ATTRIBUTES?: IPolicyRule[];
  CATEGORY_CUSTOM?: IPolicyRule[];
}

export interface IPolicy {
  policyName: string;
  statements: IPolicyStatements;
}

export const DEFAULT_SECURITY_POLICY: IPolicy = {
  policyName: 'User photo uploads in Next.js app',
  statements: {
    [CATEGORY_CONNECTIONS]: [
      // rules
      CONNECTION_RULES.localhost_3000,
      CONNECTION_RULES.nextjs_api
    ],
    [CATEGORY_API_METHODS]: [
      // rules
      API_METHOD_RULES.post_object
    ],
    [CATEGORY_DATA_ATTRIBUTES]: [
      // rules
      DATA_ATTRIBUTE_RULES.images
    ]
  }
};
