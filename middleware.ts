// middleware.ts (or middleware.js)

import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server'

//
// 1. Define which routes should remain PUBLIC (no auth required):
//
const publicRoutes = createRouteMatcher([
  '/', 
  '/api/auth/me', 
  '/sign-in', 
  '/sign-up'
])

//
// 2. Define which routes should be IGNORED entirely by Clerk’s middleware:
//
const ignoredRoutes = createRouteMatcher([
  '/api/data-collector-api',
  '/api/stdout-capture',
  '/assets/:path*',
  '/api/error-db/identification',
  '/api/upload/presigned-put',
  '/api/logs-forward/dev',
  '/api/logs-forward/prod',
  '/assets/brand/:path*',
  '/fonts/:path*',
  '/fonts/Roboto/:path*',
  '/fonts/Roboto/Roboto-Regular.ttf',
  '/fonts/Roboto/Roboto-Bold.ttf',
  '/assets/product-screenshot-workspace.png',
  '/assets/hero-demo.png',
  '/assets/video-thumbnail.png',
  '/assets/netrunner-prouduct-screenshot.png',
  '/assets/brand/netrunner-main-character.png',
  '/apple-touch-icon.png',
  '/apple-touch-icon-precomposed.png',
  '/.well-known/appspecific/com.chrome.devtools.json'
])

export default clerkMiddleware(
  async (auth, req) => {
    // 3. If the request matches one of our “ignoredRoutes”, skip all Clerk checks:
    if (ignoredRoutes(req)) {
      return
    }

    // 4. Otherwise, if the request is NOT a “publicRoute”, protect it:
    if (!publicRoutes(req)) {
      await auth.protect()
    }

    // 5. If it is a public route, do nothing (no redirect, no 404).
  },
  { debug: false } // you can toggle debug: true in development
)

export const config = {
  matcher: [
    // Skip all Next.js internals and static files (images, CSS, JS, fonts, etc.)
    '/((?!_next|favicon\\.ico|.*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run Clerk on API routes (unless explicitly ignored above)
    '/(api)(.*)'
  ]
}
