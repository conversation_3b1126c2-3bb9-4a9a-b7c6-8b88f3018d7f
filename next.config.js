/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  compiler: {
    styledComponents: true
  },
  publicRuntimeConfig: {
    STRIPE_PUBLISHABLE_KEY: process.env.STRIPE_PUBLISHABLE_KEY,
    NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY,
  },
  async redirects() {
    return [
      {
        source: '/app/grafana',
        destination: '/grafana',
        permanent: true,
      },
    ]
  },
};

module.exports = nextConfig;
