interface GrafanaUserParams {
  email: string;
  name: string;
  role: string;
}

export const checkUserExists = async (email: string) => {
  try {
    const response = await fetch('/api/grafana/get-user-id', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email })
    });

    const data = await response.json();
    
    // If the response is not ok or the user is not found, return null
    if (!response.ok || !data.success) {
      return null;
    }
    
    return data;
  } catch (error) {
    console.error('Error checking user existence:', error);
    return null;
  }
};

export const createGrafanaUser = async (params: GrafanaUserParams) => {
  const response = await fetch('/api/grafana/create-grafana-user', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(params)
  });

  const data = await response.json();
  
  if (!response.ok) {
    console.error('Grafana setup failed:', {
      status: response.status,
      statusText: response.statusText,
      data
    });
    throw new Error(data.error || data.message || 'Failed to generate Grafana credentials');
  }

  return data;
};

