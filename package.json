{"name": "tracer-web-client", "author": "<PERSON>", "version": "0.1.1", "private": true, "dependencies": {"@clerk/nextjs": "^6.20.2", "@fortawesome/fontawesome-svg-core": "^6.5.1", "@fortawesome/free-brands-svg-icons": "^6.5.1", "@fortawesome/free-solid-svg-icons": "^6.5.1", "@fortawesome/react-fontawesome": "^0.2.0", "@radix-ui/colors": "^0.1.9", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-slot": "^1.2.3", "@stitches/react": "^1.2.8", "@tailwindcss/aspect-ratio": "^0.4.2", "@xata.io/client": "^0.29.3", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^1.2.1", "logrocket": "^8.1.2", "lucide-react": "^0.358.0", "next": "^14.2.5", "react": "^18.2.0", "react-dom": "^18.2.0", "styled-components": "^6.1.8", "swr": "^2.2.5", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "prettier-format": "prettier --config .prettierrc '**/*.ts' '**/*.js' --write"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@types/node": "^18.19.24", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "eslint": "^8.57.0", "eslint-plugin-emotion": "^10.0.27", "prettier": "^2.8.8", "tailwindcss": "^3.4.3", "typescript": "^5.5.4"}}