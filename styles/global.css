/* Tailwind */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Local Roboto font files */
@font-face {
  font-family: 'Roboto';
  src: url('/fonts/Roboto/Roboto-Light.ttf') format('truetype');
  font-weight: 300;
  font-display: swap;
}

@font-face {
  font-family: 'Roboto';
  src: url('/fonts/Roboto/Roboto-Regular.ttf') format('truetype');
  font-weight: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Roboto';
  src: url('/fonts/Roboto/Roboto-Bold.ttf') format('truetype');
  font-weight: 600;
  font-display: swap;
}

body {
  font-family: 'Roboto', sans-serif;
  padding: 0px 0px 0px;
  margin: 0 auto;
  background: black;

  --primary: #000000;
  --primary-2: #111;
  --secondary: #ffffff;
  --secondary-2: #f1f3f5;
  --hover: rgba(255, 255, 255, 0.075);
  --hover-1: rgba(255, 255, 255, 0.15);
  --hover-2: rgba(255, 255, 255, 0.25);
  --selection: var(--purple);
  --text-base: white;
  --text-primary: white;
  --text-secondary: black;
  --accent-9: #fff;
  --accent-8: #fafafa;
  --accent-7: #eaeaea;
  --accent-6: #999999;
  --accent-5: #888888;
  --accent-4: #666666;
  --accent-3: #444444;
  --accent-2: #333333;
  --accent-1: #111111;
  --accent-0: #000;
}

.animated {
  animation-duration: 1s;
  animation-fill-mode: both;
  -webkit-animation-duration: 1s;
  -webkit-animation-fill-mode: both;
}

.fadeIn {
  animation: fadeIn linear 1s;
  -webkit-animation: fadeIn linear 1s;
  -moz-animation: fadeIn linear 1s;
  -o-animation: fadeIn linear 1s;
  -ms-animation: fadeIn linear 1s;
}

.fadeIn2s {
  animation: fadeIn linear 2s;
  -webkit-animation: fadeIn linear 2s;
  -moz-animation: fadeIn linear 2s;
  -o-animation: fadeIn linear 2s;
  -ms-animation: fadeIn linear 2s;
}

.fadeIn3s {
  animation: fadeIn linear 3s;
  -webkit-animation: fadeIn linear 3s;
  -moz-animation: fadeIn linear 3s;
  -o-animation: fadeIn linear 3s;
  -ms-animation: fadeIn linear 3s;
}

.fadeIn4s {
  animation: fadeIn linear 4s;
  -webkit-animation: fadeIn linear 4s;
  -moz-animation: fadeIn linear 4s;
  -o-animation: fadeIn linear 4s;
  -ms-animation: fadeIn linear 4s;
}

@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.root {
  @apply bg-accent-0;
  animation: none;
  transition: none;
  min-width: 100%;
}

.dropdownRoot {
  @apply bg-accent-0;
  box-shadow: hsl(206 22% 7% / 45%) 0px 10px 38px -10px,
    hsl(206 22% 7% / 20%) 0px 10px 20px -15px;
  min-width: 14rem;
  will-change: transform, opacity;
  animation-duration: 600ms;
  animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
  animation-fill-mode: forwards;
  transform-origin: var(--radix-dropdown-menu-content-transform-origin);
  animation-name: slideIn;
}
.slideIn {
  @keyframes slideIn {
    0% {
      opacity: 0;
      transform: translateY(2px);
    }
    100% {
      opacity: 1;
      transform: translateY(0px);
    }
  }
}

/* // REMOVE EVERYTHING BELOW HERE !!!!!!!!!!! */
.ScrollAreaRoot {
  /* width: 200px;
  height: 225px; */
  /* overflow: hidden; */
  --scrollbar-size: 10px;
}

.ScrollAreaViewport {
  width: 100%;
  height: 100%;
  border-radius: inherit;
}

.ScrollAreaScrollbar[data-orientation='vertical'] {
  /* width: var(--scrollbar-size); */
}
.ScrollAreaScrollbar[data-orientation='horizontal'] {
  /* flex-direction: column; */
  /* height: var(--scrollbar-size); */
}

/* increase target size for touch devices https://www.w3.org/WAI/WCAG21/Understanding/target-size.html */
.ScrollAreaThumb::before {
  content: '';
  /* position: absolute; */
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  /* width: 100%;
  height: 100%; */
  min-width: 44px;
  min-height: 44px;
}
