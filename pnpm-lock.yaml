lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@aws-sdk/client-s3':
        specifier: ^3.534.0
        version: 3.651.1
      '@aws-sdk/s3-request-presigner':
        specifier: ^3.534.0
        version: 3.651.1
      '@chakra-ui/next-js':
        specifier: ^2.2.0
        version: 2.2.0(@chakra-ui/react@2.8.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(framer-motion@10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(next@15.4.0-canary.61(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(sass@1.78.0))(react@18.3.1)
      '@chakra-ui/react':
        specifier: ~2.8.2
        version: 2.8.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(framer-motion@10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@clerk/nextjs':
        specifier: ^4.29.9
        version: 4.31.5(next@15.4.0-canary.61(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(sass@1.78.0))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@clickhouse/client':
        specifier: ^1.1.0
        version: 1.6.0
      '@emotion/react':
        specifier: ^11.11.4
        version: 11.13.3(@types/react@18.3.5)(react@18.3.1)
      '@emotion/styled':
        specifier: ^11.11.0
        version: 11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1)
      '@fortawesome/fontawesome-svg-core':
        specifier: ^6.5.1
        version: 6.6.0
      '@fortawesome/free-brands-svg-icons':
        specifier: ^6.5.1
        version: 6.6.0
      '@fortawesome/free-solid-svg-icons':
        specifier: ^6.5.1
        version: 6.6.0
      '@fortawesome/react-fontawesome':
        specifier: ^0.2.0
        version: 0.2.2(@fortawesome/fontawesome-svg-core@6.6.0)(react@18.3.1)
      '@headlessui/react':
        specifier: ^1.7.18
        version: 1.7.19(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@heroicons/react':
        specifier: ^1.0.6
        version: 1.0.6(react@18.3.1)
      '@mui/material':
        specifier: ^5.15.13
        version: 5.16.7(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/colors':
        specifier: ^0.1.9
        version: 0.1.9
      '@radix-ui/react-dropdown-menu':
        specifier: ^2.0.6
        version: 2.1.1(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-icons':
        specifier: ^1.3.0
        version: 1.3.0(react@18.3.1)
      '@radix-ui/react-popover':
        specifier: ^1.0.7
        version: 1.1.1(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-progress':
        specifier: ^1.0.3
        version: 1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-scroll-area':
        specifier: ^1.0.5
        version: 1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-select':
        specifier: ^1.2.2
        version: 1.2.2(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-slot':
        specifier: ^1.0.2
        version: 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@stitches/react':
        specifier: ^1.2.8
        version: 1.2.8(react@18.3.1)
      '@stripe/react-stripe-js':
        specifier: ^2.6.2
        version: 2.8.0(@stripe/stripe-js@1.54.2)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@stripe/stripe-js':
        specifier: ^1.54.2
        version: 1.54.2
      '@tailwindcss/aspect-ratio':
        specifier: ^0.4.2
        version: 0.4.2(tailwindcss@3.4.11)
      '@tanstack/react-table':
        specifier: ^8.13.2
        version: 8.20.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@tremor/react':
        specifier: ^3.16.0
        version: 3.18.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(tailwindcss@3.4.11)
      '@types/pg':
        specifier: ^8.12.0
        version: 8.12.0
      '@xata.io/client':
        specifier: ^0.29.3
        version: 0.29.5(typescript@5.6.2)
      axios:
        specifier: ^1.6.8
        version: 1.7.7
      canvas-confetti:
        specifier: ^1.9.3
        version: 1.9.3
      chartjs-adapter-date-fns:
        specifier: ^3.0.0
        version: 3.0.0(chart.js@4.4.4)(date-fns@3.6.0)
      chartjs-adapter-luxon:
        specifier: ^1.3.1
        version: 1.3.1(chart.js@4.4.4)(luxon@3.5.0)
      chartjs-plugin-streaming:
        specifier: ^2.0.0
        version: 2.0.0(chart.js@4.4.4)
      chartjs-plugin-zoom:
        specifier: ^2.0.1
        version: 2.0.1(chart.js@4.4.4)
      class-variance-authority:
        specifier: ^0.7.0
        version: 0.7.0
      clsx:
        specifier: ^1.2.1
        version: 1.2.1
      date-fns:
        specifier: ^3.6.0
        version: 3.6.0
      dayjs:
        specifier: ^1.11.10
        version: 1.11.13
      eslint-config-next:
        specifier: ^14.1.3
        version: 14.2.11(eslint@8.57.0)(typescript@5.6.2)
      eslint-config-prettier:
        specifier: ^8.10.0
        version: 8.10.0(eslint@8.57.0)
      form-data:
        specifier: ^4.0.0
        version: 4.0.0
      framer-motion:
        specifier: ^10.18.0
        version: 10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      konva:
        specifier: ^8.4.3
        version: 8.4.3
      lodash:
        specifier: ^4.17.21
        version: 4.17.21
      logrocket:
        specifier: ^8.1.2
        version: 8.1.3
      lucide-react:
        specifier: ^0.358.0
        version: 0.358.0(react@18.3.1)
      luxon:
        specifier: ^3.4.4
        version: 3.5.0
      mailgun.js:
        specifier: ^10.2.1
        version: 10.2.3
      moment:
        specifier: ^2.30.1
        version: 2.30.1
      nanoid:
        specifier: ^5.0.6
        version: 5.0.7
      next:
        specifier: 15.4.0-canary.61
        version: 15.4.0-canary.61(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(sass@1.78.0)
      openai:
        specifier: ^4.29.0
        version: 4.61.0
      pg:
        specifier: ^8.15.6
        version: 8.15.6
      react:
        specifier: ^18.2.0
        version: 18.3.1
      react-day-picker:
        specifier: ^8.10.0
        version: 8.10.1(date-fns@3.6.0)(react@18.3.1)
      react-dom:
        specifier: ^18.2.0
        version: 18.3.1(react@18.3.1)
      react-hook-form:
        specifier: ^7.51.0
        version: 7.53.0(react@18.3.1)
      react-konva:
        specifier: ^18.2.10
        version: 18.2.10(konva@8.4.3)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react-konva-utils:
        specifier: ^0.3.2
        version: 0.3.2(konva@8.4.3)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react-syntax-highlighter:
        specifier: ^15.5.0
        version: 15.5.0(react@18.3.1)
      reactflow:
        specifier: ^11.11.2
        version: 11.11.4(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      sass:
        specifier: ^1.77.8
        version: 1.78.0
      stripe:
        specifier: ^12.18.0
        version: 12.18.0
      styled-components:
        specifier: ^6.1.8
        version: 6.1.13(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      swr:
        specifier: ^2.2.5
        version: 2.2.5(react@18.3.1)
      tailwind-merge:
        specifier: ^2.2.1
        version: 2.5.2
      tailwindcss-animate:
        specifier: ^1.0.7
        version: 1.0.7(tailwindcss@3.4.11)
      use-image:
        specifier: ^1.1.1
        version: 1.1.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      uuid:
        specifier: ^9.0.1
        version: 9.0.1
    devDependencies:
      '@tailwindcss/forms':
        specifier: ^0.5.7
        version: 0.5.9(tailwindcss@3.4.11)
      '@types/node':
        specifier: ^18.19.24
        version: 18.19.50
      '@types/react':
        specifier: ^18.3.3
        version: 18.3.5
      '@types/react-dom':
        specifier: ^18.3.0
        version: 18.3.0
      '@typescript-eslint/eslint-plugin':
        specifier: ^5.62.0
        version: 5.62.0(@typescript-eslint/parser@5.62.0(eslint@8.57.0)(typescript@5.6.2))(eslint@8.57.0)(typescript@5.6.2)
      '@typescript-eslint/parser':
        specifier: ^5.62.0
        version: 5.62.0(eslint@8.57.0)(typescript@5.6.2)
      eslint:
        specifier: ^8.57.0
        version: 8.57.0
      eslint-plugin-emotion:
        specifier: ^10.0.27
        version: 10.0.27
      prettier:
        specifier: ^2.8.8
        version: 2.8.8
      tailwindcss:
        specifier: ^3.4.3
        version: 3.4.11
      typescript:
        specifier: ^5.5.4
        version: 5.6.2

packages:

  '@alloc/quick-lru@5.2.0':
    resolution: {integrity: sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==}
    engines: {node: '>=10'}

  '@aws-crypto/crc32@5.2.0':
    resolution: {integrity: sha512-nLbCWqQNgUiwwtFsen1AdzAtvuLRsQS8rYgMuxCrdKf9kOssamGLuPwyTY9wyYblNr9+1XM8v6zoDTPPSIeANg==}
    engines: {node: '>=16.0.0'}

  '@aws-crypto/crc32c@5.2.0':
    resolution: {integrity: sha512-+iWb8qaHLYKrNvGRbiYRHSdKRWhto5XlZUEBwDjYNf+ly5SVYG6zEoYIdxvf5R3zyeP16w4PLBn3rH1xc74Rag==}

  '@aws-crypto/sha1-browser@5.2.0':
    resolution: {integrity: sha512-OH6lveCFfcDjX4dbAvCFSYUjJZjDr/3XJ3xHtjn3Oj5b9RjojQo8npoLeA/bNwkOkrSQ0wgrHzXk4tDRxGKJeg==}

  '@aws-crypto/sha256-browser@5.2.0':
    resolution: {integrity: sha512-AXfN/lGotSQwu6HNcEsIASo7kWXZ5HYWvfOmSNKDsEqC4OashTp8alTmaz+F7TC2L083SFv5RdB+qU3Vs1kZqw==}

  '@aws-crypto/sha256-js@5.2.0':
    resolution: {integrity: sha512-FFQQyu7edu4ufvIZ+OadFpHHOt+eSTBaYaki44c+akjg7qZg9oOQeLlk77F6tSYqjDAFClrHJk9tMf0HdVyOvA==}
    engines: {node: '>=16.0.0'}

  '@aws-crypto/supports-web-crypto@5.2.0':
    resolution: {integrity: sha512-iAvUotm021kM33eCdNfwIN//F77/IADDSs58i+MDaOqFrVjZo9bAal0NK7HurRuWLLpF1iLX7gbWrjHjeo+YFg==}

  '@aws-crypto/util@5.2.0':
    resolution: {integrity: sha512-4RkU9EsI6ZpBve5fseQlGNUWKMa1RLPQ1dnjnQoe07ldfIzcsGb5hC5W0Dm7u423KWzawlrpbjXBrXCEv9zazQ==}

  '@aws-sdk/client-s3@3.651.1':
    resolution: {integrity: sha512-xNm+ixNRcotyrHgjUGGEyara6kCKgDdW2EVjHBZa5T+tbmtyqezwH3UzbSDZ6MlNoLhJMfR7ozuwYTIOARoBfA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/client-sso-oidc@3.651.1':
    resolution: {integrity: sha512-PKwAyTJW8pgaPIXm708haIZWBAwNycs25yNcD7OQ3NLcmgGxvrx6bSlhPEGcvwdTYwQMJsdx8ls+khlYbLqTvQ==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      '@aws-sdk/client-sts': ^3.651.1

  '@aws-sdk/client-sso@3.651.1':
    resolution: {integrity: sha512-Fm8PoMgiBKmmKrY6QQUGj/WW6eIiQqC1I0AiVXfO+Sqkmxcg3qex+CZBAYrTuIDnvnc/89f9N4mdL8V9DRn03Q==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/client-sts@3.651.1':
    resolution: {integrity: sha512-4X2RqLqeDuVLk+Omt4X+h+Fa978Wn+zek/AM4HSPi4C5XzRBEFLRRtOQUvkETvIjbEwTYQhm0LdgzcBH4bUqIg==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/core@3.651.1':
    resolution: {integrity: sha512-eqOq3W39K+5QTP5GAXtmP2s9B7hhM2pVz8OPe5tqob8o1xQgkwdgHerf3FoshO9bs0LDxassU/fUSz1wlwqfqg==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-env@3.649.0':
    resolution: {integrity: sha512-tViwzM1dauksA3fdRjsg0T8mcHklDa8EfveyiQKK6pUJopkqV6FQx+X5QNda0t/LrdEVlFZvwHNdXqOEfc83TA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-http@3.649.0':
    resolution: {integrity: sha512-ODAJ+AJJq6ozbns6ejGbicpsQ0dyMOpnGlg0J9J0jITQ05DKQZ581hdB8APDOZ9N8FstShP6dLZflSj8jb5fNA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-ini@3.651.1':
    resolution: {integrity: sha512-yOzPC3GbwLZ8IYzke4fy70ievmunnBUni/MOXFE8c9kAIV+/RMC7IWx14nAAZm0gAcY+UtCXvBVZprFqmctfzA==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      '@aws-sdk/client-sts': ^3.651.1

  '@aws-sdk/credential-provider-node@3.651.1':
    resolution: {integrity: sha512-QKA74Qs83FTUz3jS39kBuNbLAnm6cgDqomm7XS/BkYgtUq+1lI9WL97astNIuoYvumGIS58kuIa+I3ycOA4wgw==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-process@3.649.0':
    resolution: {integrity: sha512-6VYPQpEVpU+6DDS/gLoI40ppuNM5RPIEprK30qZZxnhTr5wyrGOeJ7J7wbbwPOZ5dKwta290BiJDU2ipV8Y9BQ==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-sso@3.651.1':
    resolution: {integrity: sha512-7jeU+Jbn65aDaNjkjWDQcXwjNTzpYNKovkSSRmfVpP5WYiKerVS5mrfg3RiBeiArou5igCUtYcOKlRJiGRO47g==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-web-identity@3.649.0':
    resolution: {integrity: sha512-XVk3WsDa0g3kQFPmnCH/LaCtGY/0R2NDv7gscYZSXiBZcG/fixasglTprgWSp8zcA0t7tEIGu9suyjz8ZwhymQ==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      '@aws-sdk/client-sts': ^3.649.0

  '@aws-sdk/middleware-bucket-endpoint@3.649.0':
    resolution: {integrity: sha512-ZdDICtUU4YZkrVllTUOH1Fj/F3WShLhkfNKJE3HJ/yj6pS8JS9P2lWzHiHkHiidjrHSxc6NuBo6vuZ+182XLbw==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-expect-continue@3.649.0':
    resolution: {integrity: sha512-pW2id/mWNd+L0/hZKp5yL3J+8rTwsamu9E69Hc5pM3qTF4K4DTZZ+A0sQbY6duIvZvc8IbQHbSMulBOLyWNP3A==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-flexible-checksums@3.651.1':
    resolution: {integrity: sha512-cFlXSzhdRKU1vOFTIYC3HzkN7Dwwcf07rKU1sB/PrDy4ztLhGgAwvcRwj2AqErZB62C5AdN4l7peB1Iw/oSxRQ==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-host-header@3.649.0':
    resolution: {integrity: sha512-PjAe2FocbicHVgNNwdSZ05upxIO7AgTPFtQLpnIAmoyzMcgv/zNB5fBn3uAnQSAeEPPCD+4SYVEUD1hw1ZBvEg==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-location-constraint@3.649.0':
    resolution: {integrity: sha512-O9AXhaFUQx34UTnp/cKCcaWW/IVk4mntlWfFjsIxvRatamKaY33b5fOiakGG+J1t0QFK0niDBSvOYUR1fdlHzw==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-logger@3.649.0':
    resolution: {integrity: sha512-qdqRx6q7lYC6KL/NT9x3ShTL0TBuxdkCczGzHzY3AnOoYUjnCDH7Vlq867O6MAvb4EnGNECFzIgtkZkQ4FhY5w==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-recursion-detection@3.649.0':
    resolution: {integrity: sha512-IPnO4wlmaLRf6IYmJW2i8gJ2+UPXX0hDRv1it7Qf8DpBW+lGyF2rnoN7NrFX0WIxdGOlJF1RcOr/HjXb2QeXfQ==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-sdk-s3@3.651.1':
    resolution: {integrity: sha512-4BameU35aBSzrm3L/Iphc6vFLRhz6sBwgQf09mqPA2ZlX/YFqVe8HbS8wM4DG02W8A2MRTnHXRIfFoOrErp2sw==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-ssec@3.649.0':
    resolution: {integrity: sha512-r/WBIpX+Kcx+AV5vJ+LbdDOuibk7spBqcFK2LytQjOZKPksZNRAM99khbFe9vr9S1+uDmCLVjAVkIfQ5seJrOw==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-user-agent@3.649.0':
    resolution: {integrity: sha512-q6sO10dnCXoxe9thobMJxekhJumzd1j6dxcE1+qJdYKHJr6yYgWbogJqrLCpWd30w0lEvnuAHK8lN2kWLdJxJw==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/region-config-resolver@3.649.0':
    resolution: {integrity: sha512-xURBvdQXvRvca5Du8IlC5FyCj3pkw8Z75+373J3Wb+vyg8GjD14HfKk1Je1HCCQDyIE9VB/scYDcm9ri0ppePw==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/s3-request-presigner@3.651.1':
    resolution: {integrity: sha512-PNoZkSDjvZs/ekm79jJzZMBp+3oCG74/6K/SPKKyUWiFMfrYIsnQD2y/V75n9s/2Vxie08Bgf2jroX41uQAFAw==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/signature-v4-multi-region@3.651.1':
    resolution: {integrity: sha512-aLPCMq4c/A9DmdZLhufWOgfHN2Vgft65dB2tfbATjs6kZjusSaDFxWzjmWX3y8i2ZQ+vU0nAkkWIHFJdf+47fA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/token-providers@3.649.0':
    resolution: {integrity: sha512-ZBqr+JuXI9RiN+4DSZykMx5gxpL8Dr3exIfFhxMiwAP3DQojwl0ub8ONjMuAjq9OvmX6n+jHZL6fBnNgnNFC8w==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      '@aws-sdk/client-sso-oidc': ^3.649.0

  '@aws-sdk/types@3.649.0':
    resolution: {integrity: sha512-PuPw8RysbhJNlaD2d/PzOTf8sbf4Dsn2b7hwyGh7YVG3S75yTpxSAZxrnhKsz9fStgqFmnw/jUfV/G+uQAeTVw==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/util-arn-parser@3.568.0':
    resolution: {integrity: sha512-XUKJWWo+KOB7fbnPP0+g/o5Ulku/X53t7i/h+sPHr5xxYTJJ9CYnbToo95mzxe7xWvkLrsNtJ8L+MnNn9INs2w==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/util-endpoints@3.649.0':
    resolution: {integrity: sha512-bZI1Wc3R/KibdDVWFxX/N4AoJFG4VJ92Dp4WYmOrVD6VPkb8jPz7ZeiYc7YwPl8NoDjYyPneBV0lEoK/V8OKAA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/util-format-url@3.649.0':
    resolution: {integrity: sha512-I5olOLkXQRJWAaoTSTXcycNBJ26daeEpgxYD6VPpQma9StFVK7a0MbHa1QGkOy9eVTTuf6xb2U1eiCWDWn3TXA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/util-locate-window@3.568.0':
    resolution: {integrity: sha512-3nh4TINkXYr+H41QaPelCceEB2FXP3fxp93YZXB/kqJvX0U9j0N0Uk45gvsjmEPzG8XxkPEeLIfT2I1M7A6Lig==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/util-user-agent-browser@3.649.0':
    resolution: {integrity: sha512-IY43r256LhKAvdEVQO/FPdUyVpcZS5EVxh/WHVdNzuN1bNLoUK2rIzuZqVA0EGguvCxoXVmQv9m50GvG7cGktg==}

  '@aws-sdk/util-user-agent-node@3.649.0':
    resolution: {integrity: sha512-x5DiLpZDG/AJmCIBnE3Xhpwy35QIo3WqNiOpw6ExVs1NydbM/e90zFPSfhME0FM66D/WorigvluBxxwjxDm/GA==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      aws-crt: '>=1.0.0'
    peerDependenciesMeta:
      aws-crt:
        optional: true

  '@aws-sdk/xml-builder@3.649.0':
    resolution: {integrity: sha512-XVESKkK7m5LdCVzZ3NvAja40BEyCrfPqtaiFAAhJIvW2U1Edyugf2o3XikuQY62crGT6BZagxJFgOiLKvuTiTg==}
    engines: {node: '>=16.0.0'}

  '@babel/code-frame@7.24.7':
    resolution: {integrity: sha512-BcYH1CVJBO9tvyIZ2jVeXgSIMvGZ2FDRvDdOIVQyuklNKSsx+eppDEBq/g47Ayw+RqNFE+URvOShmf+f/qwAlA==}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.25.6':
    resolution: {integrity: sha512-VPC82gr1seXOpkjAAKoLhP50vx4vGNlF4msF64dSFq1P8RfB+QAuJWGHPXXPc8QyfVWwwB/TNNU4+ayZmHNbZw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.24.7':
    resolution: {integrity: sha512-8AyH3C+74cgCVVXow/myrynrAGv+nTVg5vKu2nZph9x7RcRwzmh0VFallJuFTZ9mx6u4eSdXZfcOzSqTUm0HCA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.24.8':
    resolution: {integrity: sha512-pO9KhhRcuUyGnJWwyEgnRJTSIZHiT+vMD0kPeD+so0l7mxkMT19g3pjY9GTnHySck/hDzq+dtW/4VgnMkippsQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.24.7':
    resolution: {integrity: sha512-rR+PBcQ1SMQDDyF6X0wxtG8QyLCgUB0eRAGguqRLfkCA87l7yAP7ehq8SNj96OOGTO8OBV70KhuFYcIkHXOg0w==}
    engines: {node: '>=6.9.0'}

  '@babel/highlight@7.24.7':
    resolution: {integrity: sha512-EStJpq4OuY8xYfhGVXngigBJRWxftKX9ksiGDnmlY3o7B/V7KIAc9X4oiK87uPJSc/vs5L869bem5fhZa8caZw==}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.25.6':
    resolution: {integrity: sha512-trGdfBdbD0l1ZPmcJ83eNxB9rbEax4ALFTF7fN386TMYbeCQbyme5cOEXQhbGXKebwGaB/J52w1mrklMcbgy6Q==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/runtime@7.25.6':
    resolution: {integrity: sha512-VBj9MYyDb9tuLq7yzqjgzt6Q+IBQLrGZfdjOekyEirZPHxXWoTSGUTMrpsfi58Up73d13NfYLv8HT9vmznjzhQ==}
    engines: {node: '>=6.9.0'}

  '@babel/template@7.25.0':
    resolution: {integrity: sha512-aOOgh1/5XzKvg1jvVz7AVrx2piJ2XBi227DHmbY6y+bM9H2FlN+IfecYu4Xl0cNiiVejlsCri89LUsbj8vJD9Q==}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.25.6':
    resolution: {integrity: sha512-9Vrcx5ZW6UwK5tvqsj0nGpp/XzqthkT0dqIc9g1AdtygFToNtTF67XzYS//dm+SAK9cp3B9R4ZO/46p63SCjlQ==}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.25.6':
    resolution: {integrity: sha512-/l42B1qxpG6RdfYf343Uw1vmDjeNhneUXtzhojE7pDgfpEypmRhI6j1kr17XCVv4Cgl9HdAiQY2x0GwKm7rWCw==}
    engines: {node: '>=6.9.0'}

  '@chakra-ui/accordion@2.3.1':
    resolution: {integrity: sha512-FSXRm8iClFyU+gVaXisOSEw0/4Q+qZbFRiuhIAkVU6Boj0FxAMrlo9a8AV5TuF77rgaHytCdHk0Ng+cyUijrag==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      framer-motion: '>=4.0.0'
      react: '>=18'

  '@chakra-ui/alert@2.2.2':
    resolution: {integrity: sha512-jHg4LYMRNOJH830ViLuicjb3F+v6iriE/2G5T+Sd0Hna04nukNJ1MxUmBPE+vI22me2dIflfelu2v9wdB6Pojw==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/anatomy@2.2.2':
    resolution: {integrity: sha512-MV6D4VLRIHr4PkW4zMyqfrNS1mPlCTiCXwvYGtDFQYr+xHFfonhAuf9WjsSc0nyp2m0OdkSLnzmVKkZFLo25Tg==}

  '@chakra-ui/avatar@2.3.0':
    resolution: {integrity: sha512-8gKSyLfygnaotbJbDMHDiJoF38OHXUYVme4gGxZ1fLnQEdPVEaIWfH+NndIjOM0z8S+YEFnT9KyGMUtvPrBk3g==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/breadcrumb@2.2.0':
    resolution: {integrity: sha512-4cWCG24flYBxjruRi4RJREWTGF74L/KzI2CognAW/d/zWR0CjiScuJhf37Am3LFbCySP6WSoyBOtTIoTA4yLEA==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/breakpoint-utils@2.0.8':
    resolution: {integrity: sha512-Pq32MlEX9fwb5j5xx8s18zJMARNHlQZH2VH1RZgfgRDpp7DcEgtRW5AInfN5CfqdHLO1dGxA7I3MqEuL5JnIsA==}

  '@chakra-ui/button@2.1.0':
    resolution: {integrity: sha512-95CplwlRKmmUXkdEp/21VkEWgnwcx2TOBG6NfYlsuLBDHSLlo5FKIiE2oSi4zXc4TLcopGcWPNcm/NDaSC5pvA==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/card@2.2.0':
    resolution: {integrity: sha512-xUB/k5MURj4CtPAhdSoXZidUbm8j3hci9vnc+eZJVDqhDOShNlD6QeniQNRPRys4lWAQLCbFcrwL29C8naDi6g==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/checkbox@2.3.2':
    resolution: {integrity: sha512-85g38JIXMEv6M+AcyIGLh7igNtfpAN6KGQFYxY9tBj0eWvWk4NKQxvqqyVta0bSAyIl1rixNIIezNpNWk2iO4g==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/clickable@2.1.0':
    resolution: {integrity: sha512-flRA/ClPUGPYabu+/GLREZVZr9j2uyyazCAUHAdrTUEdDYCr31SVGhgh7dgKdtq23bOvAQJpIJjw/0Bs0WvbXw==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/close-button@2.1.1':
    resolution: {integrity: sha512-gnpENKOanKexswSVpVz7ojZEALl2x5qjLYNqSQGbxz+aP9sOXPfUS56ebyBrre7T7exuWGiFeRwnM0oVeGPaiw==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/color-mode@2.2.0':
    resolution: {integrity: sha512-niTEA8PALtMWRI9wJ4LL0CSBDo8NBfLNp4GD6/0hstcm3IlbBHTVKxN6HwSaoNYfphDQLxCjT4yG+0BJA5tFpg==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/control-box@2.1.0':
    resolution: {integrity: sha512-gVrRDyXFdMd8E7rulL0SKeoljkLQiPITFnsyMO8EFHNZ+AHt5wK4LIguYVEq88APqAGZGfHFWXr79RYrNiE3Mg==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/counter@2.1.0':
    resolution: {integrity: sha512-s6hZAEcWT5zzjNz2JIWUBzRubo9la/oof1W7EKZVVfPYHERnl5e16FmBC79Yfq8p09LQ+aqFKm/etYoJMMgghw==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/css-reset@2.3.0':
    resolution: {integrity: sha512-cQwwBy5O0jzvl0K7PLTLgp8ijqLPKyuEMiDXwYzl95seD3AoeuoCLyzZcJtVqaUZ573PiBdAbY/IlZcwDOItWg==}
    peerDependencies:
      '@emotion/react': '>=10.0.35'
      react: '>=18'

  '@chakra-ui/descendant@3.1.0':
    resolution: {integrity: sha512-VxCIAir08g5w27klLyi7PVo8BxhW4tgU/lxQyujkmi4zx7hT9ZdrcQLAted/dAa+aSIZ14S1oV0Q9lGjsAdxUQ==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/dom-utils@2.1.0':
    resolution: {integrity: sha512-ZmF2qRa1QZ0CMLU8M1zCfmw29DmPNtfjR9iTo74U5FPr3i1aoAh7fbJ4qAlZ197Xw9eAW28tvzQuoVWeL5C7fQ==}

  '@chakra-ui/editable@3.1.0':
    resolution: {integrity: sha512-j2JLrUL9wgg4YA6jLlbU88370eCRyor7DZQD9lzpY95tSOXpTljeg3uF9eOmDnCs6fxp3zDWIfkgMm/ExhcGTg==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/event-utils@2.0.8':
    resolution: {integrity: sha512-IGM/yGUHS+8TOQrZGpAKOJl/xGBrmRYJrmbHfUE7zrG3PpQyXvbLDP1M+RggkCFVgHlJi2wpYIf0QtQlU0XZfw==}

  '@chakra-ui/focus-lock@2.1.0':
    resolution: {integrity: sha512-EmGx4PhWGjm4dpjRqM4Aa+rCWBxP+Rq8Uc/nAVnD4YVqkEhBkrPTpui2lnjsuxqNaZ24fIAZ10cF1hlpemte/w==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/form-control@2.2.0':
    resolution: {integrity: sha512-wehLC1t4fafCVJ2RvJQT2jyqsAwX7KymmiGqBu7nQoQz8ApTkGABWpo/QwDh3F/dBLrouHDoOvGmYTqft3Mirw==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/hooks@2.2.1':
    resolution: {integrity: sha512-RQbTnzl6b1tBjbDPf9zGRo9rf/pQMholsOudTxjy4i9GfTfz6kgp5ValGjQm2z7ng6Z31N1cnjZ1AlSzQ//ZfQ==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/icon@3.2.0':
    resolution: {integrity: sha512-xxjGLvlX2Ys4H0iHrI16t74rG9EBcpFvJ3Y3B7KMQTrnW34Kf7Da/UC8J67Gtx85mTHW020ml85SVPKORWNNKQ==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/image@2.1.0':
    resolution: {integrity: sha512-bskumBYKLiLMySIWDGcz0+D9Th0jPvmX6xnRMs4o92tT3Od/bW26lahmV2a2Op2ItXeCmRMY+XxJH5Gy1i46VA==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/input@2.1.2':
    resolution: {integrity: sha512-GiBbb3EqAA8Ph43yGa6Mc+kUPjh4Spmxp1Pkelr8qtudpc3p2PJOOebLpd90mcqw8UePPa+l6YhhPtp6o0irhw==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/layout@2.3.1':
    resolution: {integrity: sha512-nXuZ6WRbq0WdgnRgLw+QuxWAHuhDtVX8ElWqcTK+cSMFg/52eVP47czYBE5F35YhnoW2XBwfNoNgZ7+e8Z01Rg==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/lazy-utils@2.0.5':
    resolution: {integrity: sha512-UULqw7FBvcckQk2n3iPO56TMJvDsNv0FKZI6PlUNJVaGsPbsYxK/8IQ60vZgaTVPtVcjY6BE+y6zg8u9HOqpyg==}

  '@chakra-ui/live-region@2.1.0':
    resolution: {integrity: sha512-ZOxFXwtaLIsXjqnszYYrVuswBhnIHHP+XIgK1vC6DePKtyK590Wg+0J0slDwThUAd4MSSIUa/nNX84x1GMphWw==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/media-query@3.3.0':
    resolution: {integrity: sha512-IsTGgFLoICVoPRp9ykOgqmdMotJG0CnPsKvGQeSFOB/dZfIujdVb14TYxDU4+MURXry1MhJ7LzZhv+Ml7cr8/g==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/menu@2.2.1':
    resolution: {integrity: sha512-lJS7XEObzJxsOwWQh7yfG4H8FzFPRP5hVPN/CL+JzytEINCSBvsCDHrYPQGp7jzpCi8vnTqQQGQe0f8dwnXd2g==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      framer-motion: '>=4.0.0'
      react: '>=18'

  '@chakra-ui/modal@2.3.1':
    resolution: {integrity: sha512-TQv1ZaiJMZN+rR9DK0snx/OPwmtaGH1HbZtlYt4W4s6CzyK541fxLRTjIXfEzIGpvNW+b6VFuFjbcR78p4DEoQ==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      framer-motion: '>=4.0.0'
      react: '>=18'
      react-dom: '>=18'

  '@chakra-ui/next-js@2.2.0':
    resolution: {integrity: sha512-brCz0UEOlImX4Np2jDIaljZJkW6kiDSuXG5erxvYjZlklLhmti1zj0o1sSjt5yff1xndfgHoOJb+BYG5wx+vDg==}
    peerDependencies:
      '@chakra-ui/react': '>=2.4.0'
      '@emotion/react': '>=11'
      next: '>=13'
      react: '>=18'

  '@chakra-ui/number-input@2.1.2':
    resolution: {integrity: sha512-pfOdX02sqUN0qC2ysuvgVDiws7xZ20XDIlcNhva55Jgm095xjm8eVdIBfNm3SFbSUNxyXvLTW/YQanX74tKmuA==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/number-utils@2.0.7':
    resolution: {integrity: sha512-yOGxBjXNvLTBvQyhMDqGU0Oj26s91mbAlqKHiuw737AXHt0aPllOthVUqQMeaYLwLCjGMg0jtI7JReRzyi94Dg==}

  '@chakra-ui/object-utils@2.1.0':
    resolution: {integrity: sha512-tgIZOgLHaoti5PYGPTwK3t/cqtcycW0owaiOXoZOcpwwX/vlVb+H1jFsQyWiiwQVPt9RkoSLtxzXamx+aHH+bQ==}

  '@chakra-ui/pin-input@2.1.0':
    resolution: {integrity: sha512-x4vBqLStDxJFMt+jdAHHS8jbh294O53CPQJoL4g228P513rHylV/uPscYUHrVJXRxsHfRztQO9k45jjTYaPRMw==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/popover@2.2.1':
    resolution: {integrity: sha512-K+2ai2dD0ljvJnlrzesCDT9mNzLifE3noGKZ3QwLqd/K34Ym1W/0aL1ERSynrcG78NKoXS54SdEzkhCZ4Gn/Zg==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      framer-motion: '>=4.0.0'
      react: '>=18'

  '@chakra-ui/popper@3.1.0':
    resolution: {integrity: sha512-ciDdpdYbeFG7og6/6J8lkTFxsSvwTdMLFkpVylAF6VNC22jssiWfquj2eyD4rJnzkRFPvIWJq8hvbfhsm+AjSg==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/portal@2.1.0':
    resolution: {integrity: sha512-9q9KWf6SArEcIq1gGofNcFPSWEyl+MfJjEUg/un1SMlQjaROOh3zYr+6JAwvcORiX7tyHosnmWC3d3wI2aPSQg==}
    peerDependencies:
      react: '>=18'
      react-dom: '>=18'

  '@chakra-ui/progress@2.2.0':
    resolution: {integrity: sha512-qUXuKbuhN60EzDD9mHR7B67D7p/ZqNS2Aze4Pbl1qGGZfulPW0PY8Rof32qDtttDQBkzQIzFGE8d9QpAemToIQ==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/provider@2.4.2':
    resolution: {integrity: sha512-w0Tef5ZCJK1mlJorcSjItCSbyvVuqpvyWdxZiVQmE6fvSJR83wZof42ux0+sfWD+I7rHSfj+f9nzhNaEWClysw==}
    peerDependencies:
      '@emotion/react': ^11.0.0
      '@emotion/styled': ^11.0.0
      react: '>=18'
      react-dom: '>=18'

  '@chakra-ui/radio@2.1.2':
    resolution: {integrity: sha512-n10M46wJrMGbonaghvSRnZ9ToTv/q76Szz284gv4QUWvyljQACcGrXIONUnQ3BIwbOfkRqSk7Xl/JgZtVfll+w==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/react-children-utils@2.0.6':
    resolution: {integrity: sha512-QVR2RC7QsOsbWwEnq9YduhpqSFnZGvjjGREV8ygKi8ADhXh93C8azLECCUVgRJF2Wc+So1fgxmjLcbZfY2VmBA==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/react-context@2.1.0':
    resolution: {integrity: sha512-iahyStvzQ4AOwKwdPReLGfDesGG+vWJfEsn0X/NoGph/SkN+HXtv2sCfYFFR9k7bb+Kvc6YfpLlSuLvKMHi2+w==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/react-env@3.1.0':
    resolution: {integrity: sha512-Vr96GV2LNBth3+IKzr/rq1IcnkXv+MLmwjQH6C8BRtn3sNskgDFD5vLkVXcEhagzZMCh8FR3V/bzZPojBOyNhw==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/react-types@2.0.7':
    resolution: {integrity: sha512-12zv2qIZ8EHwiytggtGvo4iLT0APris7T0qaAWqzpUGS0cdUtR8W+V1BJ5Ocq+7tA6dzQ/7+w5hmXih61TuhWQ==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/react-use-animation-state@2.1.0':
    resolution: {integrity: sha512-CFZkQU3gmDBwhqy0vC1ryf90BVHxVN8cTLpSyCpdmExUEtSEInSCGMydj2fvn7QXsz/za8JNdO2xxgJwxpLMtg==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/react-use-callback-ref@2.1.0':
    resolution: {integrity: sha512-efnJrBtGDa4YaxDzDE90EnKD3Vkh5a1t3w7PhnRQmsphLy3g2UieasoKTlT2Hn118TwDjIv5ZjHJW6HbzXA9wQ==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/react-use-controllable-state@2.1.0':
    resolution: {integrity: sha512-QR/8fKNokxZUs4PfxjXuwl0fj/d71WPrmLJvEpCTkHjnzu7LnYvzoe2wB867IdooQJL0G1zBxl0Dq+6W1P3jpg==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/react-use-disclosure@2.1.0':
    resolution: {integrity: sha512-Ax4pmxA9LBGMyEZJhhUZobg9C0t3qFE4jVF1tGBsrLDcdBeLR9fwOogIPY9Hf0/wqSlAryAimICbr5hkpa5GSw==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/react-use-event-listener@2.1.0':
    resolution: {integrity: sha512-U5greryDLS8ISP69DKDsYcsXRtAdnTQT+jjIlRYZ49K/XhUR/AqVZCK5BkR1spTDmO9H8SPhgeNKI70ODuDU/Q==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/react-use-focus-effect@2.1.0':
    resolution: {integrity: sha512-xzVboNy7J64xveLcxTIJ3jv+lUJKDwRM7Szwn9tNzUIPD94O3qwjV7DDCUzN2490nSYDF4OBMt/wuDBtaR3kUQ==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/react-use-focus-on-pointer-down@2.1.0':
    resolution: {integrity: sha512-2jzrUZ+aiCG/cfanrolsnSMDykCAbv9EK/4iUyZno6BYb3vziucmvgKuoXbMPAzWNtwUwtuMhkby8rc61Ue+Lg==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/react-use-interval@2.1.0':
    resolution: {integrity: sha512-8iWj+I/+A0J08pgEXP1J1flcvhLBHkk0ln7ZvGIyXiEyM6XagOTJpwNhiu+Bmk59t3HoV/VyvyJTa+44sEApuw==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/react-use-latest-ref@2.1.0':
    resolution: {integrity: sha512-m0kxuIYqoYB0va9Z2aW4xP/5b7BzlDeWwyXCH6QpT2PpW3/281L3hLCm1G0eOUcdVlayqrQqOeD6Mglq+5/xoQ==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/react-use-merge-refs@2.1.0':
    resolution: {integrity: sha512-lERa6AWF1cjEtWSGjxWTaSMvneccnAVH4V4ozh8SYiN9fSPZLlSG3kNxfNzdFvMEhM7dnP60vynF7WjGdTgQbQ==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/react-use-outside-click@2.2.0':
    resolution: {integrity: sha512-PNX+s/JEaMneijbgAM4iFL+f3m1ga9+6QK0E5Yh4s8KZJQ/bLwZzdhMz8J/+mL+XEXQ5J0N8ivZN28B82N1kNw==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/react-use-pan-event@2.1.0':
    resolution: {integrity: sha512-xmL2qOHiXqfcj0q7ZK5s9UjTh4Gz0/gL9jcWPA6GVf+A0Od5imEDa/Vz+533yQKWiNSm1QGrIj0eJAokc7O4fg==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/react-use-previous@2.1.0':
    resolution: {integrity: sha512-pjxGwue1hX8AFcmjZ2XfrQtIJgqbTF3Qs1Dy3d1krC77dEsiCUbQ9GzOBfDc8pfd60DrB5N2tg5JyHbypqh0Sg==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/react-use-safe-layout-effect@2.1.0':
    resolution: {integrity: sha512-Knbrrx/bcPwVS1TorFdzrK/zWA8yuU/eaXDkNj24IrKoRlQrSBFarcgAEzlCHtzuhufP3OULPkELTzz91b0tCw==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/react-use-size@2.1.0':
    resolution: {integrity: sha512-tbLqrQhbnqOjzTaMlYytp7wY8BW1JpL78iG7Ru1DlV4EWGiAmXFGvtnEt9HftU0NJ0aJyjgymkxfVGI55/1Z4A==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/react-use-timeout@2.1.0':
    resolution: {integrity: sha512-cFN0sobKMM9hXUhyCofx3/Mjlzah6ADaEl/AXl5Y+GawB5rgedgAcu2ErAgarEkwvsKdP6c68CKjQ9dmTQlJxQ==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/react-use-update-effect@2.1.0':
    resolution: {integrity: sha512-ND4Q23tETaR2Qd3zwCKYOOS1dfssojPLJMLvUtUbW5M9uW1ejYWgGUobeAiOVfSplownG8QYMmHTP86p/v0lbA==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/react-utils@2.0.12':
    resolution: {integrity: sha512-GbSfVb283+YA3kA8w8xWmzbjNWk14uhNpntnipHCftBibl0lxtQ9YqMFQLwuFOO0U2gYVocszqqDWX+XNKq9hw==}
    peerDependencies:
      react: '>=18'

  '@chakra-ui/react@2.8.2':
    resolution: {integrity: sha512-Hn0moyxxyCDKuR9ywYpqgX8dvjqwu9ArwpIb9wHNYjnODETjLwazgNIliCVBRcJvysGRiV51U2/JtJVrpeCjUQ==}
    peerDependencies:
      '@emotion/react': ^11.0.0
      '@emotion/styled': ^11.0.0
      framer-motion: '>=4.0.0'
      react: '>=18'
      react-dom: '>=18'

  '@chakra-ui/select@2.1.2':
    resolution: {integrity: sha512-ZwCb7LqKCVLJhru3DXvKXpZ7Pbu1TDZ7N0PdQ0Zj1oyVLJyrpef1u9HR5u0amOpqcH++Ugt0f5JSmirjNlctjA==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/shared-utils@2.0.5':
    resolution: {integrity: sha512-4/Wur0FqDov7Y0nCXl7HbHzCg4aq86h+SXdoUeuCMD3dSj7dpsVnStLYhng1vxvlbUnLpdF4oz5Myt3i/a7N3Q==}

  '@chakra-ui/skeleton@2.1.0':
    resolution: {integrity: sha512-JNRuMPpdZGd6zFVKjVQ0iusu3tXAdI29n4ZENYwAJEMf/fN0l12sVeirOxkJ7oEL0yOx2AgEYFSKdbcAgfUsAQ==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/skip-nav@2.1.0':
    resolution: {integrity: sha512-Hk+FG+vadBSH0/7hwp9LJnLjkO0RPGnx7gBJWI4/SpoJf3e4tZlWYtwGj0toYY4aGKl93jVghuwGbDBEMoHDug==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/slider@2.1.0':
    resolution: {integrity: sha512-lUOBcLMCnFZiA/s2NONXhELJh6sY5WtbRykPtclGfynqqOo47lwWJx+VP7xaeuhDOPcWSSecWc9Y1BfPOCz9cQ==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/spinner@2.1.0':
    resolution: {integrity: sha512-hczbnoXt+MMv/d3gE+hjQhmkzLiKuoTo42YhUG7Bs9OSv2lg1fZHW1fGNRFP3wTi6OIbD044U1P9HK+AOgFH3g==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/stat@2.1.1':
    resolution: {integrity: sha512-LDn0d/LXQNbAn2KaR3F1zivsZCewY4Jsy1qShmfBMKwn6rI8yVlbvu6SiA3OpHS0FhxbsZxQI6HefEoIgtqY6Q==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/stepper@2.3.1':
    resolution: {integrity: sha512-ky77lZbW60zYkSXhYz7kbItUpAQfEdycT0Q4bkHLxfqbuiGMf8OmgZOQkOB9uM4v0zPwy2HXhe0vq4Dd0xa55Q==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/styled-system@2.9.2':
    resolution: {integrity: sha512-To/Z92oHpIE+4nk11uVMWqo2GGRS86coeMmjxtpnErmWRdLcp1WVCVRAvn+ZwpLiNR+reWFr2FFqJRsREuZdAg==}

  '@chakra-ui/switch@2.1.2':
    resolution: {integrity: sha512-pgmi/CC+E1v31FcnQhsSGjJnOE2OcND4cKPyTE+0F+bmGm48Q/b5UmKD9Y+CmZsrt/7V3h8KNczowupfuBfIHA==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      framer-motion: '>=4.0.0'
      react: '>=18'

  '@chakra-ui/system@2.6.2':
    resolution: {integrity: sha512-EGtpoEjLrUu4W1fHD+a62XR+hzC5YfsWm+6lO0Kybcga3yYEij9beegO0jZgug27V+Rf7vns95VPVP6mFd/DEQ==}
    peerDependencies:
      '@emotion/react': ^11.0.0
      '@emotion/styled': ^11.0.0
      react: '>=18'

  '@chakra-ui/table@2.1.0':
    resolution: {integrity: sha512-o5OrjoHCh5uCLdiUb0Oc0vq9rIAeHSIRScc2ExTC9Qg/uVZl2ygLrjToCaKfaaKl1oQexIeAcZDKvPG8tVkHyQ==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/tabs@3.0.0':
    resolution: {integrity: sha512-6Mlclp8L9lqXmsGWF5q5gmemZXOiOYuh0SGT/7PgJVNPz3LXREXlXg2an4MBUD8W5oTkduCX+3KTMCwRrVrDYw==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/tag@3.1.1':
    resolution: {integrity: sha512-Bdel79Dv86Hnge2PKOU+t8H28nm/7Y3cKd4Kfk9k3lOpUh4+nkSGe58dhRzht59lEqa4N9waCgQiBdkydjvBXQ==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/textarea@2.1.2':
    resolution: {integrity: sha512-ip7tvklVCZUb2fOHDb23qPy/Fr2mzDOGdkrpbNi50hDCiV4hFX02jdQJdi3ydHZUyVgZVBKPOJ+lT9i7sKA2wA==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@chakra-ui/theme-tools@2.1.2':
    resolution: {integrity: sha512-Qdj8ajF9kxY4gLrq7gA+Azp8CtFHGO9tWMN2wfF9aQNgG9AuMhPrUzMq9AMQ0MXiYcgNq/FD3eegB43nHVmXVA==}
    peerDependencies:
      '@chakra-ui/styled-system': '>=2.0.0'

  '@chakra-ui/theme-utils@2.0.21':
    resolution: {integrity: sha512-FjH5LJbT794r0+VSCXB3lT4aubI24bLLRWB+CuRKHijRvsOg717bRdUN/N1fEmEpFnRVrbewttWh/OQs0EWpWw==}

  '@chakra-ui/theme@3.3.1':
    resolution: {integrity: sha512-Hft/VaT8GYnItGCBbgWd75ICrIrIFrR7lVOhV/dQnqtfGqsVDlrztbSErvMkoPKt0UgAkd9/o44jmZ6X4U2nZQ==}
    peerDependencies:
      '@chakra-ui/styled-system': '>=2.8.0'

  '@chakra-ui/toast@7.0.2':
    resolution: {integrity: sha512-yvRP8jFKRs/YnkuE41BVTq9nB2v/KDRmje9u6dgDmE5+1bFt3bwjdf9gVbif4u5Ve7F7BGk5E093ARRVtvLvXA==}
    peerDependencies:
      '@chakra-ui/system': 2.6.2
      framer-motion: '>=4.0.0'
      react: '>=18'
      react-dom: '>=18'

  '@chakra-ui/tooltip@2.3.1':
    resolution: {integrity: sha512-Rh39GBn/bL4kZpuEMPPRwYNnccRCL+w9OqamWHIB3Qboxs6h8cOyXfIdGxjo72lvhu1QI/a4KFqkM3St+WfC0A==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      framer-motion: '>=4.0.0'
      react: '>=18'
      react-dom: '>=18'

  '@chakra-ui/transition@2.1.0':
    resolution: {integrity: sha512-orkT6T/Dt+/+kVwJNy7zwJ+U2xAZ3EU7M3XCs45RBvUnZDr/u9vdmaM/3D/rOpmQJWgQBwKPJleUXrYWUagEDQ==}
    peerDependencies:
      framer-motion: '>=4.0.0'
      react: '>=18'

  '@chakra-ui/utils@2.0.15':
    resolution: {integrity: sha512-El4+jL0WSaYYs+rJbuYFDbjmfCcfGDmRY95GO4xwzit6YAPZBLcR65rOEwLps+XWluZTy1xdMrusg/hW0c1aAA==}

  '@chakra-ui/visually-hidden@2.2.0':
    resolution: {integrity: sha512-KmKDg01SrQ7VbTD3+cPWf/UfpF5MSwm3v7MWi0n5t8HnnadT13MF0MJCDSXbBWnzLv1ZKJ6zlyAOeARWX+DpjQ==}
    peerDependencies:
      '@chakra-ui/system': '>=2.0.0'
      react: '>=18'

  '@clerk/backend@0.38.13':
    resolution: {integrity: sha512-K6ftfgVmxiA6Bio/0W+Ama/sDVaI8xBV8SbtOHYLARD2k3lpLM3HDnAVPx3AjyDHygsXmhbj/5Ag5o7+EKSCAg==}
    engines: {node: '>=14'}

  '@clerk/clerk-react@4.32.3':
    resolution: {integrity: sha512-qLKazE3ERUcQwzIaRzLH7BszConlTRsDZuW5hDrFA4T3KNv5ReaFP7W2nW+dvfCz1UC6RC3E4622k1fmfXC6+A==}
    engines: {node: '>=14'}
    peerDependencies:
      react: '>=16'

  '@clerk/clerk-sdk-node@4.13.21':
    resolution: {integrity: sha512-g9cCaw2ySR/CVFhE5IhL7FC5YdHUFtq8/TUkM8WB5PaxEKV+dhl+JdDJRHFXQm0JwT7inm1pgNXgmyc2eOgRhg==}
    engines: {node: '>=14'}

  '@clerk/nextjs@4.31.5':
    resolution: {integrity: sha512-teacggQ58mfWOfWkW6urbXRPLpVo4FluZPgklR/Q6Q0WGEIaUF3qYnP1sSYv6j/Adjy3OTFdfjjfnFLB/tyipA==}
    engines: {node: '>=14'}
    peerDependencies:
      next: '>=10'
      react: ^17.0.2 || ^18.0.0-0
      react-dom: ^17.0.2 || ^18.0.0-0

  '@clerk/shared@1.4.1':
    resolution: {integrity: sha512-3rlZy0Hadnb1dw6x+4MGEC7dpZLlIVY3mZTwWRRS4CILWowVAccwfW84paN2XNlM12lJgMc+w66WNdw19XFtpg==}
    peerDependencies:
      react: '>=16'
    peerDependenciesMeta:
      react:
        optional: true

  '@clerk/types@3.65.3':
    resolution: {integrity: sha512-q2BvCBK3H9SxLyoSsybAFTcbW6s50JPY8JACFqzenBbByOyolgIXPJNE0MStUUnNc7lRrPDkoLuZZf71X7JqIw==}
    engines: {node: '>=14'}

  '@clickhouse/client-common@1.6.0':
    resolution: {integrity: sha512-DPdfPUIrBumWff8JdE2oLqnOIN1DwxXt48juV+tB6zaonufQR1QImtUGEhIFIqRWJU7zErtQ1eKfkwjLY0MYDg==}

  '@clickhouse/client@1.6.0':
    resolution: {integrity: sha512-cjuOL5O11Y/axYIvSaNgZjZqD53BBbScrG0DCgZ7XBCz5KWlA5lIG3fP6A4jME277q8ZUYAQxUOlJjeF18872A==}
    engines: {node: '>=16'}

  '@emnapi/runtime@1.4.3':
    resolution: {integrity: sha512-pBPWdu6MLKROBX05wSNKcNb++m5Er+KQ9QkB+WVM+pW2Kx9hoSrVTnu3BdkI5eBLZoKu/J6mW/B6i6bJB2ytXQ==}

  '@emotion/babel-plugin@11.12.0':
    resolution: {integrity: sha512-y2WQb+oP8Jqvvclh8Q55gLUyb7UFvgv7eJfsj7td5TToBrIUtPay2kMrZi4xjq9qw2vD0ZR5fSho0yqoFgX7Rw==}

  '@emotion/cache@11.13.1':
    resolution: {integrity: sha512-iqouYkuEblRcXmylXIwwOodiEK5Ifl7JcX7o6V4jI3iW4mLXX3dmt5xwBtIkJiQEXFAI+pC8X0i67yiPkH9Ucw==}

  '@emotion/hash@0.9.2':
    resolution: {integrity: sha512-MyqliTZGuOm3+5ZRSaaBGP3USLw6+EGykkwZns2EPC5g8jJ4z9OrdZY9apkl3+UP9+sdz76YYkwCKP5gh8iY3g==}

  '@emotion/is-prop-valid@0.8.8':
    resolution: {integrity: sha512-u5WtneEAr5IDG2Wv65yhunPSMLIpuKsbuOktRojfrEiEvRyC85LgPMZI63cr7NUqT8ZIGdSVg8ZKGxIug4lXcA==}

  '@emotion/is-prop-valid@1.2.2':
    resolution: {integrity: sha512-uNsoYd37AFmaCdXlg6EYD1KaPOaRWRByMCYzbKUX4+hhMfrxdVSelShywL4JVaAeM/eHUOSprYBQls+/neX3pw==}

  '@emotion/is-prop-valid@1.3.0':
    resolution: {integrity: sha512-SHetuSLvJDzuNbOdtPVbq6yMMMlLoW5Q94uDqJZqy50gcmAjxFkVqmzqSGEFq9gT2iMuIeKV1PXVWmvUhuZLlQ==}

  '@emotion/memoize@0.7.4':
    resolution: {integrity: sha512-Ja/Vfqe3HpuzRsG1oBtWTHk2PGZ7GR+2Vz5iYGelAw8dx32K0y7PjVuxK6z1nMpZOqAFsRUPCkK1YjJ56qJlgw==}

  '@emotion/memoize@0.8.1':
    resolution: {integrity: sha512-W2P2c/VRW1/1tLox0mVUalvnWXxavmv/Oum2aPsRcoDJuob75FC3Y8FbpfLwUegRcxINtGUMPq0tFCvYNTBXNA==}

  '@emotion/memoize@0.9.0':
    resolution: {integrity: sha512-30FAj7/EoJ5mwVPOWhAyCX+FPfMDrVecJAM+Iw9NRoSl4BBAQeqj4cApHHUXOVvIPgLVDsCFoz/hGD+5QQD1GQ==}

  '@emotion/react@11.13.3':
    resolution: {integrity: sha512-lIsdU6JNrmYfJ5EbUCf4xW1ovy5wKQ2CkPRM4xogziOxH1nXxBSjpC9YqbFAP7circxMfYp+6x676BqWcEiixg==}
    peerDependencies:
      '@types/react': '*'
      react: '>=16.8.0'
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@emotion/serialize@1.3.1':
    resolution: {integrity: sha512-dEPNKzBPU+vFPGa+z3axPRn8XVDetYORmDC0wAiej+TNcOZE70ZMJa0X7JdeoM6q/nWTMZeLpN/fTnD9o8MQBA==}

  '@emotion/sheet@1.4.0':
    resolution: {integrity: sha512-fTBW9/8r2w3dXWYM4HCB1Rdp8NLibOw2+XELH5m5+AkWiL/KqYX6dc0kKYlaYyKjrQ6ds33MCdMPEwgs2z1rqg==}

  '@emotion/styled@11.13.0':
    resolution: {integrity: sha512-tkzkY7nQhW/zC4hztlwucpT8QEZ6eUzpXDRhww/Eej4tFfO0FxQYWRyg/c5CCXa4d/f174kqeXYjuQRnhzf6dA==}
    peerDependencies:
      '@emotion/react': ^11.0.0-rc.0
      '@types/react': '*'
      react: '>=16.8.0'
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@emotion/unitless@0.10.0':
    resolution: {integrity: sha512-dFoMUuQA20zvtVTuxZww6OHoJYgrzfKM1t52mVySDJnMSEa08ruEvdYQbhvyu6soU+NeLVd3yKfTfT0NeV6qGg==}

  '@emotion/unitless@0.8.1':
    resolution: {integrity: sha512-KOEGMu6dmJZtpadb476IsZBclKvILjopjUii3V+7MnXIQCYh8W3NgNcgwo21n9LXZX6EDIKvqfjYxXebDwxKmQ==}

  '@emotion/use-insertion-effect-with-fallbacks@1.1.0':
    resolution: {integrity: sha512-+wBOcIV5snwGgI2ya3u99D7/FJquOIniQT1IKyDsBmEgwvpxMNeS65Oib7OnE2d2aY+3BU4OiH+0Wchf8yk3Hw==}
    peerDependencies:
      react: '>=16.8.0'

  '@emotion/utils@1.4.0':
    resolution: {integrity: sha512-spEnrA1b6hDR/C68lC2M7m6ALPUHZC0lIY7jAS/B/9DuuO1ZP04eov8SMv/6fwRd8pzmsn2AuJEznRREWlQrlQ==}

  '@emotion/weak-memoize@0.4.0':
    resolution: {integrity: sha512-snKqtPW01tN0ui7yu9rGv69aJXr/a/Ywvl11sUjNtEcRc+ng/mQriFL0wLXMef74iHa/EkftbDzU9F8iFbH+zg==}

  '@eslint-community/eslint-utils@4.4.0':
    resolution: {integrity: sha512-1/sA4dwrzBAyeUoQ6oxahHKmrZvsnLCg4RfxW3ZFGGmQkSNQPFNLV9CUEFQP1x9EYXHTo5p6xdhZM1Ne9p/AfA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

  '@eslint-community/regexpp@4.11.1':
    resolution: {integrity: sha512-m4DVN9ZqskZoLU5GlWZadwDnYo3vAEydiUayB9widCl9ffWx2IvPnp6n3on5rJmziJSw9Bv+Z3ChDVdMwXCY8Q==}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}

  '@eslint/eslintrc@2.1.4':
    resolution: {integrity: sha512-269Z39MS6wVJtsoUl10L60WdkhJVdPG24Q4eZTH3nnF6lpvSShEK3wQjDX9JRWAUPvPh7COouPpU9IrqaZFvtQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@eslint/js@8.57.0':
    resolution: {integrity: sha512-Ys+3g2TaW7gADOJzPt83SJtCDhMjndcDMFVQ/Tj9iA1BfJzFKD9mAUXT3OenpuPHbI6P/myECxRJrofUsDx/5g==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@floating-ui/core@1.6.8':
    resolution: {integrity: sha512-7XJ9cPU+yI2QeLS+FCSlqNFZJq8arvswefkZrYI1yQBbftw6FyrZOxYSh+9S7z7TpeWlRt9zJ5IhM1WIL334jA==}

  '@floating-ui/dom@1.6.11':
    resolution: {integrity: sha512-qkMCxSR24v2vGkhYDo/UzxfJN3D4syqSjyuTFz6C7XcpU1pASPRieNI0Kj5VP3/503mOfYiGY891ugBX1GlABQ==}

  '@floating-ui/react-dom@1.3.0':
    resolution: {integrity: sha512-htwHm67Ji5E/pROEAr7f8IKFShuiCKHwUC/UY4vC3I5jiSvGFAYnSYiZO5MlGmads+QqvUkR9ANHEguGrDv72g==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'

  '@floating-ui/react-dom@2.1.2':
    resolution: {integrity: sha512-06okr5cgPzMNBy+Ycse2A6udMi4bqwW/zgBF/rwjcNqWkyr82Mcg8b0vjX8OJpZFy/FKjJmw6wV7t44kK6kW7A==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'

  '@floating-ui/react@0.19.2':
    resolution: {integrity: sha512-JyNk4A0Ezirq8FlXECvRtQOX/iBe5Ize0W/pLkrZjfHW9GUV7Xnq6zm6fyZuQzaHHqEnVizmvlA96e1/CkZv+w==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'

  '@floating-ui/utils@0.2.8':
    resolution: {integrity: sha512-kym7SodPp8/wloecOpcmSnWJsK7M0E5Wg8UcFA+uO4B9s5d0ywXOEro/8HM9x0rW+TljRzul/14UYz3TleT3ig==}

  '@fortawesome/fontawesome-common-types@6.6.0':
    resolution: {integrity: sha512-xyX0X9mc0kyz9plIyryrRbl7ngsA9jz77mCZJsUkLl+ZKs0KWObgaEBoSgQiYWAsSmjz/yjl0F++Got0Mdp4Rw==}
    engines: {node: '>=6'}

  '@fortawesome/fontawesome-svg-core@6.6.0':
    resolution: {integrity: sha512-KHwPkCk6oRT4HADE7smhfsKudt9N/9lm6EJ5BVg0tD1yPA5hht837fB87F8pn15D8JfTqQOjhKTktwmLMiD7Kg==}
    engines: {node: '>=6'}

  '@fortawesome/free-brands-svg-icons@6.6.0':
    resolution: {integrity: sha512-1MPD8lMNW/earme4OQi1IFHtmHUwAKgghXlNwWi9GO7QkTfD+IIaYpIai4m2YJEzqfEji3jFHX1DZI5pbY/biQ==}
    engines: {node: '>=6'}

  '@fortawesome/free-solid-svg-icons@6.6.0':
    resolution: {integrity: sha512-IYv/2skhEDFc2WGUcqvFJkeK39Q+HyPf5GHUrT/l2pKbtgEIv1al1TKd6qStR5OIwQdN1GZP54ci3y4mroJWjA==}
    engines: {node: '>=6'}

  '@fortawesome/react-fontawesome@0.2.2':
    resolution: {integrity: sha512-EnkrprPNqI6SXJl//m29hpaNzOp1bruISWaOiRtkMi/xSvHJlzc2j2JAYS7egxt/EbjSNV/k6Xy0AQI6vB2+1g==}
    peerDependencies:
      '@fortawesome/fontawesome-svg-core': ~1 || ~6
      react: '>=16.3'

  '@headlessui/react@1.7.19':
    resolution: {integrity: sha512-Ll+8q3OlMJfJbAKM/+/Y2q6PPYbryqNTXDbryx7SXLIDamkF6iQFbriYHga0dY44PvDhvvBWCx1Xj4U5+G4hOw==}
    engines: {node: '>=10'}
    peerDependencies:
      react: ^16 || ^17 || ^18
      react-dom: ^16 || ^17 || ^18

  '@headlessui/tailwindcss@0.2.1':
    resolution: {integrity: sha512-2+5+NZ+RzMyrVeCZOxdbvkUSssSxGvcUxphkIfSVLpRiKsj+/63T2TOL9dBYMXVfj/CGr6hMxSRInzXv6YY7sA==}
    engines: {node: '>=10'}
    peerDependencies:
      tailwindcss: ^3.0

  '@heroicons/react@1.0.6':
    resolution: {integrity: sha512-JJCXydOFWMDpCP4q13iEplA503MQO3xLoZiKum+955ZCtHINWnx26CUxVxxFQu/uLb4LW3ge15ZpzIkXKkJ8oQ==}
    peerDependencies:
      react: '>= 16'

  '@humanwhocodes/config-array@0.11.14':
    resolution: {integrity: sha512-3T8LkOmg45BV5FICb15QQMsyUSWrQ8AygVfC7ZG32zOalnqrilm018ZVCw0eapXux8FtA33q8PSRSstjee3jSg==}
    engines: {node: '>=10.10.0'}
    deprecated: Use @eslint/config-array instead

  '@humanwhocodes/module-importer@1.0.1':
    resolution: {integrity: sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==}
    engines: {node: '>=12.22'}

  '@humanwhocodes/object-schema@2.0.3':
    resolution: {integrity: sha512-93zYdMES/c1D69yZiKDBj0V24vqNzB/koF26KPaagAfd3P/4gUlh3Dys5ogAK+Exi9QyzlD8x/08Zt7wIKcDcA==}
    deprecated: Use @eslint/object-schema instead

  '@img/sharp-darwin-arm64@0.34.2':
    resolution: {integrity: sha512-OfXHZPppddivUJnqyKoi5YVeHRkkNE2zUFT2gbpKxp/JZCFYEYubnMg+gOp6lWfasPrTS+KPosKqdI+ELYVDtg==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [arm64]
    os: [darwin]

  '@img/sharp-darwin-x64@0.34.2':
    resolution: {integrity: sha512-dYvWqmjU9VxqXmjEtjmvHnGqF8GrVjM2Epj9rJ6BUIXvk8slvNDJbhGFvIoXzkDhrJC2jUxNLz/GUjjvSzfw+g==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [x64]
    os: [darwin]

  '@img/sharp-libvips-darwin-arm64@1.1.0':
    resolution: {integrity: sha512-HZ/JUmPwrJSoM4DIQPv/BfNh9yrOA8tlBbqbLz4JZ5uew2+o22Ik+tHQJcih7QJuSa0zo5coHTfD5J8inqj9DA==}
    cpu: [arm64]
    os: [darwin]

  '@img/sharp-libvips-darwin-x64@1.1.0':
    resolution: {integrity: sha512-Xzc2ToEmHN+hfvsl9wja0RlnXEgpKNmftriQp6XzY/RaSfwD9th+MSh0WQKzUreLKKINb3afirxW7A0fz2YWuQ==}
    cpu: [x64]
    os: [darwin]

  '@img/sharp-libvips-linux-arm64@1.1.0':
    resolution: {integrity: sha512-IVfGJa7gjChDET1dK9SekxFFdflarnUB8PwW8aGwEoF3oAsSDuNUTYS+SKDOyOJxQyDC1aPFMuRYLoDInyV9Ew==}
    cpu: [arm64]
    os: [linux]

  '@img/sharp-libvips-linux-arm@1.1.0':
    resolution: {integrity: sha512-s8BAd0lwUIvYCJyRdFqvsj+BJIpDBSxs6ivrOPm/R7piTs5UIwY5OjXrP2bqXC9/moGsyRa37eYWYCOGVXxVrA==}
    cpu: [arm]
    os: [linux]

  '@img/sharp-libvips-linux-ppc64@1.1.0':
    resolution: {integrity: sha512-tiXxFZFbhnkWE2LA8oQj7KYR+bWBkiV2nilRldT7bqoEZ4HiDOcePr9wVDAZPi/Id5fT1oY9iGnDq20cwUz8lQ==}
    cpu: [ppc64]
    os: [linux]

  '@img/sharp-libvips-linux-s390x@1.1.0':
    resolution: {integrity: sha512-xukSwvhguw7COyzvmjydRb3x/09+21HykyapcZchiCUkTThEQEOMtBj9UhkaBRLuBrgLFzQ2wbxdeCCJW/jgJA==}
    cpu: [s390x]
    os: [linux]

  '@img/sharp-libvips-linux-x64@1.1.0':
    resolution: {integrity: sha512-yRj2+reB8iMg9W5sULM3S74jVS7zqSzHG3Ol/twnAAkAhnGQnpjj6e4ayUz7V+FpKypwgs82xbRdYtchTTUB+Q==}
    cpu: [x64]
    os: [linux]

  '@img/sharp-libvips-linuxmusl-arm64@1.1.0':
    resolution: {integrity: sha512-jYZdG+whg0MDK+q2COKbYidaqW/WTz0cc1E+tMAusiDygrM4ypmSCjOJPmFTvHHJ8j/6cAGyeDWZOsK06tP33w==}
    cpu: [arm64]
    os: [linux]

  '@img/sharp-libvips-linuxmusl-x64@1.1.0':
    resolution: {integrity: sha512-wK7SBdwrAiycjXdkPnGCPLjYb9lD4l6Ze2gSdAGVZrEL05AOUJESWU2lhlC+Ffn5/G+VKuSm6zzbQSzFX/P65A==}
    cpu: [x64]
    os: [linux]

  '@img/sharp-linux-arm64@0.34.2':
    resolution: {integrity: sha512-D8n8wgWmPDakc83LORcfJepdOSN6MvWNzzz2ux0MnIbOqdieRZwVYY32zxVx+IFUT8er5KPcyU3XXsn+GzG/0Q==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [arm64]
    os: [linux]

  '@img/sharp-linux-arm@0.34.2':
    resolution: {integrity: sha512-0DZzkvuEOqQUP9mo2kjjKNok5AmnOr1jB2XYjkaoNRwpAYMDzRmAqUIa1nRi58S2WswqSfPOWLNOr0FDT3H5RQ==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [arm]
    os: [linux]

  '@img/sharp-linux-s390x@0.34.2':
    resolution: {integrity: sha512-EGZ1xwhBI7dNISwxjChqBGELCWMGDvmxZXKjQRuqMrakhO8QoMgqCrdjnAqJq/CScxfRn+Bb7suXBElKQpPDiw==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [s390x]
    os: [linux]

  '@img/sharp-linux-x64@0.34.2':
    resolution: {integrity: sha512-sD7J+h5nFLMMmOXYH4DD9UtSNBD05tWSSdWAcEyzqW8Cn5UxXvsHAxmxSesYUsTOBmUnjtxghKDl15EvfqLFbQ==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [x64]
    os: [linux]

  '@img/sharp-linuxmusl-arm64@0.34.2':
    resolution: {integrity: sha512-NEE2vQ6wcxYav1/A22OOxoSOGiKnNmDzCYFOZ949xFmrWZOVII1Bp3NqVVpvj+3UeHMFyN5eP/V5hzViQ5CZNA==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [arm64]
    os: [linux]

  '@img/sharp-linuxmusl-x64@0.34.2':
    resolution: {integrity: sha512-DOYMrDm5E6/8bm/yQLCWyuDJwUnlevR8xtF8bs+gjZ7cyUNYXiSf/E8Kp0Ss5xasIaXSHzb888V1BE4i1hFhAA==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [x64]
    os: [linux]

  '@img/sharp-wasm32@0.34.2':
    resolution: {integrity: sha512-/VI4mdlJ9zkaq53MbIG6rZY+QRN3MLbR6usYlgITEzi4Rpx5S6LFKsycOQjkOGmqTNmkIdLjEvooFKwww6OpdQ==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [wasm32]

  '@img/sharp-win32-arm64@0.34.2':
    resolution: {integrity: sha512-cfP/r9FdS63VA5k0xiqaNaEoGxBg9k7uE+RQGzuK9fHt7jib4zAVVseR9LsE4gJcNWgT6APKMNnCcnyOtmSEUQ==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [arm64]
    os: [win32]

  '@img/sharp-win32-ia32@0.34.2':
    resolution: {integrity: sha512-QLjGGvAbj0X/FXl8n1WbtQ6iVBpWU7JO94u/P2M4a8CFYsvQi4GW2mRy/JqkRx0qpBzaOdKJKw8uc930EX2AHw==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [ia32]
    os: [win32]

  '@img/sharp-win32-x64@0.34.2':
    resolution: {integrity: sha512-aUdT6zEYtDKCaxkofmmJDJYGCf0+pJg3eU9/oBuqvEeoB9dKI6ZLc/1iLJCTuJQDO4ptntAlkUmHgGjyuobZbw==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [x64]
    os: [win32]

  '@isaacs/cliui@8.0.2':
    resolution: {integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==}
    engines: {node: '>=12'}

  '@jridgewell/gen-mapping@0.3.5':
    resolution: {integrity: sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/set-array@1.2.1':
    resolution: {integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/sourcemap-codec@1.5.0':
    resolution: {integrity: sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==}

  '@jridgewell/trace-mapping@0.3.25':
    resolution: {integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==}

  '@kurkle/color@0.3.2':
    resolution: {integrity: sha512-fuscdXJ9G1qb7W8VdHi+IwRqij3lBkosAm4ydQtEmbY58OzHXqQhvlxqEkoz0yssNVn38bcpRWgA9PP+OGoisw==}

  '@mui/core-downloads-tracker@5.16.7':
    resolution: {integrity: sha512-RtsCt4Geed2/v74sbihWzzRs+HsIQCfclHeORh5Ynu2fS4icIKozcSubwuG7vtzq2uW3fOR1zITSP84TNt2GoQ==}

  '@mui/material@5.16.7':
    resolution: {integrity: sha512-cwwVQxBhK60OIOqZOVLFt55t01zmarKJiJUWbk0+8s/Ix5IaUzAShqlJchxsIQ4mSrWqgcKCCXKtIlG5H+/Jmg==}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      '@emotion/react': ^11.5.0
      '@emotion/styled': ^11.3.0
      '@types/react': ^17.0.0 || ^18.0.0
      react: ^17.0.0 || ^18.0.0
      react-dom: ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@emotion/react':
        optional: true
      '@emotion/styled':
        optional: true
      '@types/react':
        optional: true

  '@mui/private-theming@5.16.6':
    resolution: {integrity: sha512-rAk+Rh8Clg7Cd7shZhyt2HGTTE5wYKNSJ5sspf28Fqm/PZ69Er9o6KX25g03/FG2dfpg5GCwZh/xOojiTfm3hw==}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      '@types/react': ^17.0.0 || ^18.0.0
      react: ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@mui/styled-engine@5.16.6':
    resolution: {integrity: sha512-zaThmS67ZmtHSWToTiHslbI8jwrmITcN93LQaR2lKArbvS7Z3iLkwRoiikNWutx9MBs8Q6okKvbZq1RQYB3v7g==}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      '@emotion/react': ^11.4.1
      '@emotion/styled': ^11.3.0
      react: ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@emotion/react':
        optional: true
      '@emotion/styled':
        optional: true

  '@mui/system@5.16.7':
    resolution: {integrity: sha512-Jncvs/r/d/itkxh7O7opOunTqbbSSzMTHzZkNLM+FjAOg+cYAZHrPDlYe1ZGKUYORwwb2XexlWnpZp0kZ4AHuA==}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      '@emotion/react': ^11.5.0
      '@emotion/styled': ^11.3.0
      '@types/react': ^17.0.0 || ^18.0.0
      react: ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@emotion/react':
        optional: true
      '@emotion/styled':
        optional: true
      '@types/react':
        optional: true

  '@mui/types@7.2.16':
    resolution: {integrity: sha512-qI8TV3M7ShITEEc8Ih15A2vLzZGLhD+/UPNwck/hcls2gwg7dyRjNGXcQYHKLB5Q7PuTRfrTkAoPa2VV1s67Ag==}
    peerDependencies:
      '@types/react': ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@mui/utils@5.16.6':
    resolution: {integrity: sha512-tWiQqlhxAt3KENNiSRL+DIn9H5xNVK6Jjf70x3PnfQPz1MPBdh7yyIcAyVBT9xiw7hP3SomRhPR7hzBMBCjqEA==}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      '@types/react': ^17.0.0 || ^18.0.0
      react: ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@next/env@15.4.0-canary.61':
    resolution: {integrity: sha512-69POa6nDa+1/CgHUiDxBUXFQFB5EdlrshdXzCaDcsbVNYeZKoGnrKHweQCzfmKGXvRcM3dziuhucVIwv68hbVw==}

  '@next/eslint-plugin-next@14.2.11':
    resolution: {integrity: sha512-7mw+xW7Y03Ph4NTCcAzYe+vu4BNjEHZUfZayyF3Y1D9RX6c5NIe25m1grHEAkyUuaqjRxOYhnCNeglOkIqLkBA==}

  '@next/swc-darwin-arm64@15.4.0-canary.61':
    resolution: {integrity: sha512-rM5TWBNlC3pLISxQ2yZfWXqqKx/B9OjhQcfTWHFJsuEzJuxVEDn0AxexjE/0jo+mEm8Xgwdwo0QA1bcGaRxAxQ==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [darwin]

  '@next/swc-darwin-x64@15.4.0-canary.61':
    resolution: {integrity: sha512-R9WsMuXxpfhKpwcsYrDcpP+bsIPj8uPSSaZgNgxHQUovA1tCGVQO06ExddbGcnuvrs+J105/1NxuVafJlkJxjg==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [darwin]

  '@next/swc-linux-arm64-gnu@15.4.0-canary.61':
    resolution: {integrity: sha512-NcWW7Th1UCq9A+FKONHoUwoyJYbxDWu6MIG/SwjV0FMyEBtCwHU5YbN+hicj/B2KW2OBbA/dhhj30Pa468A0YQ==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]

  '@next/swc-linux-arm64-musl@15.4.0-canary.61':
    resolution: {integrity: sha512-E4Ha94KEWiQK+BNMG8C8qi17PAl476JbrpwzmwQVi6CI58lJuN5KMBxqlCaOeeF582muO2YmjU5TDfBA/EY1ug==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]

  '@next/swc-linux-x64-gnu@15.4.0-canary.61':
    resolution: {integrity: sha512-5Y3qFsEs0WHaL8n64iCWJpH92YSBVWDTdtbkA8YHobo5BuBDVimIMgrLbSzAVkHmd0GkMcoOEGXgN0+LwC8T5A==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]

  '@next/swc-linux-x64-musl@15.4.0-canary.61':
    resolution: {integrity: sha512-7e3htZt4fpwjcG/iNTRtFwaAiqw0QFPp+U0arBxVH/bikKMeTqbflKvfD4RnGWkSB8FQbSgAyGvLQWEE22WPhA==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]

  '@next/swc-win32-arm64-msvc@15.4.0-canary.61':
    resolution: {integrity: sha512-ABj9Wpk/4XZAgJfflG2KrhDvusDO+r2I/0Byr1C2DXmY5wUhcFSJFXViAQX98PufO7t6Jg5flPC7uqBf4Hp0eQ==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [win32]

  '@next/swc-win32-x64-msvc@15.4.0-canary.61':
    resolution: {integrity: sha512-WWzzuq52/Mu7/zr5nm4V2r6U+wnra6BxNLh1d4ZoxGkAPGkVPSOMmE/dTcVPjsADszOJwJzjVDq/KAYEftqEyw==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [win32]

  '@nodelib/fs.scandir@2.1.5':
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}

  '@nodelib/fs.stat@2.0.5':
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}

  '@nodelib/fs.walk@1.2.8':
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}

  '@nolyfill/is-core-module@1.0.39':
    resolution: {integrity: sha512-nn5ozdjYQpUCZlWGuxcJY/KpxkWQs4DcbMCmKojjyrYDEAGy4Ce19NN4v5MduafTwJlbKc99UA8YhSVqq9yPZA==}
    engines: {node: '>=12.4.0'}

  '@peculiar/asn1-schema@2.3.13':
    resolution: {integrity: sha512-3Xq3a01WkHRZL8X04Zsfg//mGaA21xlL4tlVn4v2xGT0JStiztATRkMwa5b+f/HXmY2smsiLXYK46Gwgzvfg3g==}

  '@peculiar/json-schema@1.1.12':
    resolution: {integrity: sha512-coUfuoMeIB7B8/NMekxaDzLhaYmp0HZNPEjYRm9goRou8UZIC3z21s0sL9AWoCw4EG876QyO3kYrc61WNF9B/w==}
    engines: {node: '>=8.0.0'}

  '@peculiar/webcrypto@1.4.1':
    resolution: {integrity: sha512-eK4C6WTNYxoI7JOabMoZICiyqRRtJB220bh0Mbj5RwRycleZf9BPyZoxsTvpP0FpmVS2aS13NKOuh5/tN3sIRw==}
    engines: {node: '>=10.12.0'}

  '@pkgjs/parseargs@0.11.0':
    resolution: {integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==}
    engines: {node: '>=14'}

  '@popperjs/core@2.11.8':
    resolution: {integrity: sha512-P1st0aksCrn9sGZhp8GMYwBnQsbvAWsZAX44oXNNvLHGqAOcoVxmjZiohstwQ7SqKnbR47akdNi+uleWD8+g6A==}

  '@radix-ui/colors@0.1.9':
    resolution: {integrity: sha512-Vxq944ErPJsdVepjEUhOLO9ApUVOocA63knc+V2TkJ09D/AVOjiMIgkca/7VoYgODcla0qbSIBjje0SMfZMbAw==}

  '@radix-ui/number@1.0.1':
    resolution: {integrity: sha512-T5gIdVO2mmPW3NNhjNgEP3cqMXjXL9UbO0BzWcXfvdBs+BohbQxvd/K5hSVKmn9/lbTdsQVKbUcP5WLCwvUbBg==}

  '@radix-ui/number@1.1.0':
    resolution: {integrity: sha512-V3gRzhVNU1ldS5XhAPTom1fOIo4ccrjjJgmE+LI2h/WaFpHmx0MQApT+KZHnx8abG6Avtfcz4WoEciMnpFT3HQ==}

  '@radix-ui/primitive@1.0.1':
    resolution: {integrity: sha512-yQ8oGX2GVsEYMWGxcovu1uGWPCxV5BFfeeYxqPmuAzUyLT9qmaMXSAhXpb0WrspIeqYzdJpkh2vHModJPgRIaw==}

  '@radix-ui/primitive@1.1.0':
    resolution: {integrity: sha512-4Z8dn6Upk0qk4P74xBhZ6Hd/w0mPEzOOLxy4xiPXOXqjF7jZS0VAKk7/x/H6FyY2zCkYJqePf1G5KmkmNJ4RBA==}

  '@radix-ui/react-arrow@1.0.3':
    resolution: {integrity: sha512-wSP+pHsB/jQRaL6voubsQ/ZlrGBHHrOjmBnr19hxYgtS0WvAFwZhK2WP/YY5yF9uKECCEEDGxuLxq1NBK51wFA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-arrow@1.1.0':
    resolution: {integrity: sha512-FmlW1rCg7hBpEBwFbjHwCW6AmWLQM6g/v0Sn8XbP9NvmSZ2San1FpQeyPtufzOMSIx7Y4dzjlHoifhp+7NkZhw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-collection@1.0.3':
    resolution: {integrity: sha512-3SzW+0PW7yBBoQlT8wNcGtaxaD0XSu0uLUFgrtHY08Acx05TaHaOmVLR73c0j/cqpDy53KBMO7s0dx2wmOIDIA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-collection@1.1.0':
    resolution: {integrity: sha512-GZsZslMJEyo1VKm5L1ZJY8tGDxZNPAoUeQUIbKeJfoi7Q4kmig5AsgLMYYuyYbfjd8fBmFORAIwYAkXMnXZgZw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-compose-refs@1.0.1':
    resolution: {integrity: sha512-fDSBgd44FKHa1FRMU59qBMPFcl2PZE+2nmqunj+BWFyYYjnhIDWL2ItDs3rrbJDQOtzt5nIebLCQc4QRfz6LJw==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-compose-refs@1.1.0':
    resolution: {integrity: sha512-b4inOtiaOnYf9KWyO3jAeeCG6FeyfY6ldiEPanbUjWd+xIk5wZeHa8yVwmrJ2vderhu/BQvzCrJI0lHd+wIiqw==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-context@1.0.1':
    resolution: {integrity: sha512-ebbrdFoYTcuZ0v4wG5tedGnp9tzcV8awzsxYph7gXUyvnNLuTIcCk1q17JEbnVhXAKG9oX3KtchwiMIAYp9NLg==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-context@1.1.0':
    resolution: {integrity: sha512-OKrckBy+sMEgYM/sMmqmErVn0kZqrHPJze+Ql3DzYsDDp0hl0L62nx/2122/Bvps1qz645jlcu2tD9lrRSdf8A==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-direction@1.0.1':
    resolution: {integrity: sha512-RXcvnXgyvYvBEOhCBuddKecVkoMiI10Jcm5cTI7abJRAHYfFxeu+FBQs/DvdxSYucxR5mna0dNsL6QFlds5TMA==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-direction@1.1.0':
    resolution: {integrity: sha512-BUuBvgThEiAXh2DWu93XsT+a3aWrGqolGlqqw5VU1kG7p/ZH2cuDlM1sRLNnY3QcBS69UIz2mcKhMxDsdewhjg==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-dismissable-layer@1.0.4':
    resolution: {integrity: sha512-7UpBa/RKMoHJYjie1gkF1DlK8l1fdU/VKDpoS3rCCo8YBJR294GwcEHyxHw72yvphJ7ld0AXEcSLAzY2F/WyCg==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-dismissable-layer@1.1.0':
    resolution: {integrity: sha512-/UovfmmXGptwGcBQawLzvn2jOfM0t4z3/uKffoBlj724+n3FvBbZ7M0aaBOmkp6pqFYpO4yx8tSVJjx3Fl2jig==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-dropdown-menu@2.1.1':
    resolution: {integrity: sha512-y8E+x9fBq9qvteD2Zwa4397pUVhYsh9iq44b5RD5qu1GMJWBCBuVg1hMyItbc6+zH00TxGRqd9Iot4wzf3OoBQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-focus-guards@1.0.1':
    resolution: {integrity: sha512-Rect2dWbQ8waGzhMavsIbmSVCgYxkXLxxR3ZvCX79JOglzdEy4JXMb98lq4hPxUbLr77nP0UOGf4rcMU+s1pUA==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-focus-guards@1.1.0':
    resolution: {integrity: sha512-w6XZNUPVv6xCpZUqb/yN9DL6auvpGX3C/ee6Hdi16v2UUy25HV2Q5bcflsiDyT/g5RwbPQ/GIT1vLkeRb+ITBw==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-focus-scope@1.0.3':
    resolution: {integrity: sha512-upXdPfqI4islj2CslyfUBNlaJCPybbqRHAi1KER7Isel9Q2AtSJ0zRBZv8mWQiFXD2nyAJ4BhC3yXgZ6kMBSrQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-focus-scope@1.1.0':
    resolution: {integrity: sha512-200UD8zylvEyL8Bx+z76RJnASR2gRMuxlgFCPAe/Q/679a/r0eK3MBVYMb7vZODZcffZBdob1EGnky78xmVvcA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-icons@1.3.0':
    resolution: {integrity: sha512-jQxj/0LKgp+j9BiTXz3O3sgs26RNet2iLWmsPyRz2SIcR4q/4SbazXfnYwbAr+vLYKSfc7qxzyGQA1HLlYiuNw==}
    peerDependencies:
      react: ^16.x || ^17.x || ^18.x

  '@radix-ui/react-id@1.0.1':
    resolution: {integrity: sha512-tI7sT/kqYp8p96yGWY1OAnLHrqDgzHefRBKQ2YAkBS5ja7QLcZ9Z/uY7bEjPUatf8RomoXM8/1sMj1IJaE5UzQ==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-id@1.1.0':
    resolution: {integrity: sha512-EJUrI8yYh7WOjNOqpoJaf1jlFIH2LvtgAl+YcFqNCa+4hj64ZXmPkAKOFs/ukjz3byN6bdb/AVUqHkI8/uWWMA==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-menu@2.1.1':
    resolution: {integrity: sha512-oa3mXRRVjHi6DZu/ghuzdylyjaMXLymx83irM7hTxutQbD+7IhPKdMdRHD26Rm+kHRrWcrUkkRPv5pd47a2xFQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-popover@1.1.1':
    resolution: {integrity: sha512-3y1A3isulwnWhvTTwmIreiB8CF4L+qRjZnK1wYLO7pplddzXKby/GnZ2M7OZY3qgnl6p9AodUIHRYGXNah8Y7g==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-popper@1.1.2':
    resolution: {integrity: sha512-1CnGGfFi/bbqtJZZ0P/NQY20xdG3E0LALJaLUEoKwPLwl6PPPfbeiCqMVQnhoFRAxjJj4RpBRJzDmUgsex2tSg==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-popper@1.2.0':
    resolution: {integrity: sha512-ZnRMshKF43aBxVWPWvbj21+7TQCvhuULWJ4gNIKYpRlQt5xGRhLx66tMp8pya2UkGHTSlhpXwmjqltDYHhw7Vg==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-portal@1.0.3':
    resolution: {integrity: sha512-xLYZeHrWoPmA5mEKEfZZevoVRK/Q43GfzRXkWV6qawIWWK8t6ifIiLQdd7rmQ4Vk1bmI21XhqF9BN3jWf+phpA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-portal@1.1.1':
    resolution: {integrity: sha512-A3UtLk85UtqhzFqtoC8Q0KvR2GbXF3mtPgACSazajqq6A41mEQgo53iPzY4i6BwDxlIFqWIhiQ2G729n+2aw/g==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-presence@1.1.0':
    resolution: {integrity: sha512-Gq6wuRN/asf9H/E/VzdKoUtT8GC9PQc9z40/vEr0VCJ4u5XvvhWIrSsCB6vD2/cH7ugTdSfYq9fLJCcM00acrQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-primitive@1.0.3':
    resolution: {integrity: sha512-yi58uVyoAcK/Nq1inRY56ZSjKypBNKTa/1mcL8qdl6oJeEaDbOldlzrGn7P6Q3Id5d+SYNGc5AJgc4vGhjs5+g==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-primitive@2.0.0':
    resolution: {integrity: sha512-ZSpFm0/uHa8zTvKBDjLFWLo8dkr4MBsiDLz0g3gMUwqgLHz9rTaRRGYDgvZPtBJgYCBKXkS9fzmoySgr8CO6Cw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-progress@1.1.0':
    resolution: {integrity: sha512-aSzvnYpP725CROcxAOEBVZZSIQVQdHgBr2QQFKySsaD14u8dNT0batuXI+AAGDdAHfXH8rbnHmjYFqVJ21KkRg==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-roving-focus@1.1.0':
    resolution: {integrity: sha512-EA6AMGeq9AEeQDeSH0aZgG198qkfHSbvWTf1HvoDmOB5bBG/qTxjYMWUKMnYiV6J/iP/J8MEFSuB2zRU2n7ODA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-scroll-area@1.1.0':
    resolution: {integrity: sha512-9ArIZ9HWhsrfqS765h+GZuLoxaRHD/j0ZWOWilsCvYTpYJp8XwCqNG7Dt9Nu/TItKOdgLGkOPCodQvDc+UMwYg==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-select@1.2.2':
    resolution: {integrity: sha512-zI7McXr8fNaSrUY9mZe4x/HC0jTLY9fWNhO1oLWYMQGDXuV4UCivIGTxwioSzO0ZCYX9iSLyWmAh/1TOmX3Cnw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-slot@1.0.2':
    resolution: {integrity: sha512-YeTpuq4deV+6DusvVUW4ivBgnkHwECUu0BiN43L5UCDFgdhsRUWAghhTF5MbvNTPzmiFOx90asDSUjWuCNapwg==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-slot@1.1.0':
    resolution: {integrity: sha512-FUCf5XMfmW4dtYl69pdS4DbxKy8nj4M7SafBgPllysxmdachynNflAdp/gCsnYWNDnge6tI9onzMp5ARYc1KNw==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-callback-ref@1.0.1':
    resolution: {integrity: sha512-D94LjX4Sp0xJFVaoQOd3OO9k7tpBYNOXdVhkltUbGv2Qb9OXdrg/CpsjlZv7ia14Sylv398LswWBVVu5nqKzAQ==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-callback-ref@1.1.0':
    resolution: {integrity: sha512-CasTfvsy+frcFkbXtSJ2Zu9JHpN8TYKxkgJGWbjiZhFivxaeW7rMeZt7QELGVLaYVfFMsKHjb7Ak0nMEe+2Vfw==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-controllable-state@1.0.1':
    resolution: {integrity: sha512-Svl5GY5FQeN758fWKrjM6Qb7asvXeiZltlT4U2gVfl8Gx5UAv2sMR0LWo8yhsIZh2oQ0eFdZ59aoOOMV7b47VA==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-controllable-state@1.1.0':
    resolution: {integrity: sha512-MtfMVJiSr2NjzS0Aa90NPTnvTSg6C/JLCV7ma0W6+OMV78vd8OyRpID+Ng9LxzsPbLeuBnWBA1Nq30AtBIDChw==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-escape-keydown@1.0.3':
    resolution: {integrity: sha512-vyL82j40hcFicA+M4Ex7hVkB9vHgSse1ZWomAqV2Je3RleKGO5iM8KMOEtfoSB0PnIelMd2lATjTGMYqN5ylTg==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-escape-keydown@1.1.0':
    resolution: {integrity: sha512-L7vwWlR1kTTQ3oh7g1O0CBF3YCyyTj8NmhLR+phShpyA50HCfBFKVJTpshm9PzLiKmehsrQzTYTpX9HvmC9rhw==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-layout-effect@1.0.1':
    resolution: {integrity: sha512-v/5RegiJWYdoCvMnITBkNNx6bCj20fiaJnWtRkU18yITptraXjffz5Qbn05uOiQnOvi+dbkznkoaMltz1GnszQ==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-layout-effect@1.1.0':
    resolution: {integrity: sha512-+FPE0rOdziWSrH9athwI1R0HDVbWlEhd+FR+aSDk4uWGmSJ9Z54sdZVDQPZAinJhJXwfT+qnj969mCsT2gfm5w==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-previous@1.0.1':
    resolution: {integrity: sha512-cV5La9DPwiQ7S0gf/0qiD6YgNqM5Fk97Kdrlc5yBcrF3jyEZQwm7vYFqMo4IfeHgJXsRaMvLABFtd0OVEmZhDw==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-rect@1.0.1':
    resolution: {integrity: sha512-Cq5DLuSiuYVKNU8orzJMbl15TXilTnJKUCltMVQg53BQOF1/C5toAaGrowkgksdBQ9H+SRL23g0HDmg9tvmxXw==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-rect@1.1.0':
    resolution: {integrity: sha512-0Fmkebhr6PiseyZlYAOtLS+nb7jLmpqTrJyv61Pe68MKYW6OWdRE2kI70TaYY27u7H0lajqM3hSMMLFq18Z7nQ==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-size@1.0.1':
    resolution: {integrity: sha512-ibay+VqrgcaI6veAojjofPATwledXiSmX+C0KrBk/xgpX9rBzPV3OsfwlhQdUOFbh+LKQorLYT+xTXW9V8yd0g==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-size@1.1.0':
    resolution: {integrity: sha512-XW3/vWuIXHa+2Uwcc2ABSfcCledmXhhQPlGbfcRXbiUQI5Icjcg19BGCZVKKInYbvUCut/ufbbLLPFC5cbb1hw==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-visually-hidden@1.0.3':
    resolution: {integrity: sha512-D4w41yN5YRKtu464TLnByKzMDG/JlMPHtfZgQAu9v6mNakUqGUI9vUrfQKz8NK41VMm/xbZbh76NUTVtIYqOMA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/rect@1.0.1':
    resolution: {integrity: sha512-fyrgCaedtvMg9NK3en0pnOYJdtfwxUcNolezkNPUsoX57X8oQk+NkqcvzHXD2uKNij6GXmWU9NDru2IWjrO4BQ==}

  '@radix-ui/rect@1.1.0':
    resolution: {integrity: sha512-A9+lCBZoaMJlVKcRBz2YByCG+Cp2t6nAnMnNba+XiWxnj6r4JUFqfsgwocMBZU9LPtdxC6wB56ySYpc7LQIoJg==}

  '@reactflow/background@11.3.14':
    resolution: {integrity: sha512-Gewd7blEVT5Lh6jqrvOgd4G6Qk17eGKQfsDXgyRSqM+CTwDqRldG2LsWN4sNeno6sbqVIC2fZ+rAUBFA9ZEUDA==}
    peerDependencies:
      react: '>=17'
      react-dom: '>=17'

  '@reactflow/controls@11.2.14':
    resolution: {integrity: sha512-MiJp5VldFD7FrqaBNIrQ85dxChrG6ivuZ+dcFhPQUwOK3HfYgX2RHdBua+gx+40p5Vw5It3dVNp/my4Z3jF0dw==}
    peerDependencies:
      react: '>=17'
      react-dom: '>=17'

  '@reactflow/core@11.11.4':
    resolution: {integrity: sha512-H4vODklsjAq3AMq6Np4LE12i1I4Ta9PrDHuBR9GmL8uzTt2l2jh4CiQbEMpvMDcp7xi4be0hgXj+Ysodde/i7Q==}
    peerDependencies:
      react: '>=17'
      react-dom: '>=17'

  '@reactflow/minimap@11.7.14':
    resolution: {integrity: sha512-mpwLKKrEAofgFJdkhwR5UQ1JYWlcAAL/ZU/bctBkuNTT1yqV+y0buoNVImsRehVYhJwffSWeSHaBR5/GJjlCSQ==}
    peerDependencies:
      react: '>=17'
      react-dom: '>=17'

  '@reactflow/node-resizer@2.2.14':
    resolution: {integrity: sha512-fwqnks83jUlYr6OHcdFEedumWKChTHRGw/kbCxj0oqBd+ekfs+SIp4ddyNU0pdx96JIm5iNFS0oNrmEiJbbSaA==}
    peerDependencies:
      react: '>=17'
      react-dom: '>=17'

  '@reactflow/node-toolbar@1.3.14':
    resolution: {integrity: sha512-rbynXQnH/xFNu4P9H+hVqlEUafDCkEoCy0Dg9mG22Sg+rY/0ck6KkrAQrYrTgXusd+cEJOMK0uOOFCK2/5rSGQ==}
    peerDependencies:
      react: '>=17'
      react-dom: '>=17'

  '@rtsao/scc@1.1.0':
    resolution: {integrity: sha512-zt6OdqaDoOnJ1ZYsCYGt9YmWzDXl4vQdKTyJev62gFhRGKdx7mcT54V9KIjg+d2wi9EXsPvAPKe7i7WjfVWB8g==}

  '@rushstack/eslint-patch@1.10.4':
    resolution: {integrity: sha512-WJgX9nzTqknM393q1QJDJmoW28kUfEnybeTfVNcNAPnIx210RXm2DiXiHzfNPJNIUUb1tJnz/l4QGtJ30PgWmA==}

  '@smithy/abort-controller@3.1.4':
    resolution: {integrity: sha512-VupaALAQlXViW3/enTf/f5l5JZYSAxoJL7f0nanhNNKnww6DGCg1oYIuNP78KDugnkwthBO6iEcym16HhWV8RQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/chunked-blob-reader-native@3.0.0':
    resolution: {integrity: sha512-VDkpCYW+peSuM4zJip5WDfqvg2Mo/e8yxOv3VF1m11y7B8KKMKVFtmZWDe36Fvk8rGuWrPZHHXZ7rR7uM5yWyg==}

  '@smithy/chunked-blob-reader@3.0.0':
    resolution: {integrity: sha512-sbnURCwjF0gSToGlsBiAmd1lRCmSn72nu9axfJu5lIx6RUEgHu6GwTMbqCdhQSi0Pumcm5vFxsi9XWXb2mTaoA==}

  '@smithy/config-resolver@3.0.8':
    resolution: {integrity: sha512-Tv1obAC18XOd2OnDAjSWmmthzx6Pdeh63FbLin8MlPiuJ2ATpKkq0NcNOJFr0dO+JmZXnwu8FQxKJ3TKJ3Hulw==}
    engines: {node: '>=16.0.0'}

  '@smithy/core@2.4.3':
    resolution: {integrity: sha512-4LTusLqFMRVQUfC3RNuTg6IzYTeJNpydRdTKq7J5wdEyIRQSu3rGIa3s80mgG2hhe6WOZl9IqTSo1pgbn6EHhA==}
    engines: {node: '>=16.0.0'}

  '@smithy/credential-provider-imds@3.2.3':
    resolution: {integrity: sha512-VoxMzSzdvkkjMJNE38yQgx4CfnmT+Z+5EUXkg4x7yag93eQkVQgZvN3XBSHC/ylfBbLbAtdu7flTCChX9I+mVg==}
    engines: {node: '>=16.0.0'}

  '@smithy/eventstream-codec@3.1.5':
    resolution: {integrity: sha512-6pu+PT2r+5ZnWEV3vLV1DzyrpJ0TmehQlniIDCSpZg6+Ji2SfOI38EqUyQ+O8lotVElCrfVc9chKtSMe9cmCZQ==}

  '@smithy/eventstream-serde-browser@3.0.9':
    resolution: {integrity: sha512-PiQLo6OQmZAotJweIcObL1H44gkvuJACKMNqpBBe5Rf2Ax1DOcGi/28+feZI7yTe1ERHlQQaGnm8sSkyDUgsMg==}
    engines: {node: '>=16.0.0'}

  '@smithy/eventstream-serde-config-resolver@3.0.6':
    resolution: {integrity: sha512-iew15It+c7WfnVowWkt2a7cdPp533LFJnpjDQgfZQcxv2QiOcyEcea31mnrk5PVbgo0nNH3VbYGq7myw2q/F6A==}
    engines: {node: '>=16.0.0'}

  '@smithy/eventstream-serde-node@3.0.8':
    resolution: {integrity: sha512-6m+wI+fT0na+6oao6UqALVA38fsScCpoG5UO/A8ZSyGLnPM2i4MS1cFUhpuALgvLMxfYoTCh7qSeJa0aG4IWpQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/eventstream-serde-universal@3.0.8':
    resolution: {integrity: sha512-09tqzIQ6e+7jLqGvRji1yJoDbL/zob0OFhq75edgStWErGLf16+yI5hRc/o9/YAybOhUZs/swpW2SPn892G5Gg==}
    engines: {node: '>=16.0.0'}

  '@smithy/fetch-http-handler@3.2.7':
    resolution: {integrity: sha512-Ra6IPI1spYLO+t62/3jQbodjOwAbto9wlpJdHZwkycm0Kit+GVpzHW/NMmSgY4rK1bjJ4qLAmCnaBzePO5Nkkg==}

  '@smithy/hash-blob-browser@3.1.5':
    resolution: {integrity: sha512-Vi3eoNCmao4iKglS80ktYnBOIqZhjbDDwa1IIbF/VaJ8PsHnZTQ5wSicicPrU7nTI4JPFn92/txzWkh4GlK18Q==}

  '@smithy/hash-node@3.0.6':
    resolution: {integrity: sha512-c/FHEdKK/7DU2z6ZE91L36ahyXWayR3B+FzELjnYq7wH5YqIseM24V+pWCS9kFn1Ln8OFGTf+pyYPiHZuX0s/Q==}
    engines: {node: '>=16.0.0'}

  '@smithy/hash-stream-node@3.1.5':
    resolution: {integrity: sha512-61CyFCzqN3VBfcnGX7mof/rkzLb8oHjm4Lr6ZwBIRpBssBb8d09ChrZAqinP2rUrA915BRNkq9NpJz18N7+3hQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/invalid-dependency@3.0.6':
    resolution: {integrity: sha512-czM7Ioq3s8pIXht7oD+vmgy4Wfb4XavU/k/irO8NdXFFOx7YAlsCCcKOh/lJD1mJSYQqiR7NmpZ9JviryD/7AQ==}

  '@smithy/is-array-buffer@2.2.0':
    resolution: {integrity: sha512-GGP3O9QFD24uGeAXYUjwSTXARoqpZykHadOmA8G5vfJPK0/DC67qa//0qvqrJzL1xc8WQWX7/yc7fwudjPHPhA==}
    engines: {node: '>=14.0.0'}

  '@smithy/is-array-buffer@3.0.0':
    resolution: {integrity: sha512-+Fsu6Q6C4RSJiy81Y8eApjEB5gVtM+oFKTffg+jSuwtvomJJrhUJBu2zS8wjXSgH/g1MKEWrzyChTBe6clb5FQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/md5-js@3.0.6':
    resolution: {integrity: sha512-Ze690T8O3M5SVbb70WormwrKzVf9QQRtIuxtJDgpUQDkmt+PtdYDetBbyCbF9ryupxLw6tgzWKgwffAShhVIXQ==}

  '@smithy/middleware-content-length@3.0.8':
    resolution: {integrity: sha512-VuyszlSO49WKh3H9/kIO2kf07VUwGV80QRiaDxUfP8P8UKlokz381ETJvwLhwuypBYhLymCYyNhB3fLAGBX2og==}
    engines: {node: '>=16.0.0'}

  '@smithy/middleware-endpoint@3.1.3':
    resolution: {integrity: sha512-KeM/OrK8MVFUsoJsmCN0MZMVPjKKLudn13xpgwIMpGTYpA8QZB2Xq5tJ+RE6iu3A6NhOI4VajDTwBsm8pwwrhg==}
    engines: {node: '>=16.0.0'}

  '@smithy/middleware-retry@3.0.18':
    resolution: {integrity: sha512-YU1o/vYob6vlqZdd97MN8cSXRToknLXhFBL3r+c9CZcnxkO/rgNZ++CfgX2vsmnEKvlqdi26+SRtSzlVp5z6Mg==}
    engines: {node: '>=16.0.0'}

  '@smithy/middleware-serde@3.0.6':
    resolution: {integrity: sha512-KKTUSl1MzOM0MAjGbudeaVNtIDo+PpekTBkCNwvfZlKndodrnvRo+00USatiyLOc0ujjO9UydMRu3O9dYML7ag==}
    engines: {node: '>=16.0.0'}

  '@smithy/middleware-stack@3.0.6':
    resolution: {integrity: sha512-2c0eSYhTQ8xQqHMcRxLMpadFbTXg6Zla5l0mwNftFCZMQmuhI7EbAJMx6R5eqfuV3YbJ3QGyS3d5uSmrHV8Khg==}
    engines: {node: '>=16.0.0'}

  '@smithy/node-config-provider@3.1.7':
    resolution: {integrity: sha512-g3mfnC3Oo8pOI0dYuPXLtdW1WGVb3bR2tkV21GNkm0ZvQjLTtamXAwCWt/FCb0HGvKt3gHHmF1XerG0ICfalOg==}
    engines: {node: '>=16.0.0'}

  '@smithy/node-http-handler@3.2.2':
    resolution: {integrity: sha512-42Cy4/oT2O+00aiG1iQ7Kd7rE6q8j7vI0gFfnMlUiATvyo8vefJkhb7O10qZY0jAqo5WZdUzfl9IV6wQ3iMBCg==}
    engines: {node: '>=16.0.0'}

  '@smithy/property-provider@3.1.6':
    resolution: {integrity: sha512-NK3y/T7Q/Bw+Z8vsVs9MYIQ5v7gOX7clyrXcwhhIBQhbPgRl6JDrZbusO9qWDhcEus75Tg+VCxtIRfo3H76fpw==}
    engines: {node: '>=16.0.0'}

  '@smithy/protocol-http@4.1.3':
    resolution: {integrity: sha512-GcbMmOYpH9iRqtC05RbRnc/0FssxSTHlmaNhYBTgSgNCYpdR3Kt88u5GAZTBmouzv+Zlj/VRv92J9ruuDeJuEw==}
    engines: {node: '>=16.0.0'}

  '@smithy/querystring-builder@3.0.6':
    resolution: {integrity: sha512-sQe08RunoObe+Usujn9+R2zrLuQERi3CWvRO3BvnoWSYUaIrLKuAIeY7cMeDax6xGyfIP3x/yFWbEKSXvOnvVg==}
    engines: {node: '>=16.0.0'}

  '@smithy/querystring-parser@3.0.6':
    resolution: {integrity: sha512-UJKw4LlEkytzz2Wq+uIdHf6qOtFfee/o7ruH0jF5I6UAuU+19r9QV7nU3P/uI0l6+oElRHmG/5cBBcGJrD7Ozg==}
    engines: {node: '>=16.0.0'}

  '@smithy/service-error-classification@3.0.6':
    resolution: {integrity: sha512-53SpchU3+DUZrN7J6sBx9tBiCVGzsib2e4sc512Q7K9fpC5zkJKs6Z9s+qbMxSYrkEkle6hnMtrts7XNkMJJMg==}
    engines: {node: '>=16.0.0'}

  '@smithy/shared-ini-file-loader@3.1.7':
    resolution: {integrity: sha512-IA4K2qTJYXkF5OfVN4vsY1hfnUZjaslEE8Fsr/gGFza4TAC2A9NfnZuSY2srQIbt9bwtjHiAayrRVgKse4Q7fA==}
    engines: {node: '>=16.0.0'}

  '@smithy/signature-v4@4.1.3':
    resolution: {integrity: sha512-YD2KYSCEEeFHcWZ1E3mLdAaHl8T/TANh6XwmocQ6nPcTdBfh4N5fusgnblnWDlnlU1/cUqEq3PiGi22GmT2Lkg==}
    engines: {node: '>=16.0.0'}

  '@smithy/smithy-client@3.3.2':
    resolution: {integrity: sha512-RKDfhF2MTwXl7jan5d7QfS9eCC6XJbO3H+EZAvLQN8A5in4ib2Ml4zoeLo57w9QrqFekBPcsoC2hW3Ekw4vQ9Q==}
    engines: {node: '>=16.0.0'}

  '@smithy/types@3.4.2':
    resolution: {integrity: sha512-tHiFcfcVedVBHpmHUEUHOCCih8iZbIAYn9NvPsNzaPm/237I3imdDdZoOC8c87H5HBAVEa06tTgb+OcSWV9g5w==}
    engines: {node: '>=16.0.0'}

  '@smithy/url-parser@3.0.6':
    resolution: {integrity: sha512-47Op/NU8Opt49KyGpHtVdnmmJMsp2hEwBdyjuFB9M2V5QVOwA7pBhhxKN5z6ztKGrMw76gd8MlbPuzzvaAncuQ==}

  '@smithy/util-base64@3.0.0':
    resolution: {integrity: sha512-Kxvoh5Qtt0CDsfajiZOCpJxgtPHXOKwmM+Zy4waD43UoEMA+qPxxa98aE/7ZhdnBFZFXMOiBR5xbcaMhLtznQQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-body-length-browser@3.0.0':
    resolution: {integrity: sha512-cbjJs2A1mLYmqmyVl80uoLTJhAcfzMOyPgjwAYusWKMdLeNtzmMz9YxNl3/jRLoxSS3wkqkf0jwNdtXWtyEBaQ==}

  '@smithy/util-body-length-node@3.0.0':
    resolution: {integrity: sha512-Tj7pZ4bUloNUP6PzwhN7K386tmSmEET9QtQg0TgdNOnxhZvCssHji+oZTUIuzxECRfG8rdm2PMw2WCFs6eIYkA==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-buffer-from@2.2.0':
    resolution: {integrity: sha512-IJdWBbTcMQ6DA0gdNhh/BwrLkDR+ADW5Kr1aZmd4k3DIF6ezMV4R2NIAmT08wQJ3yUK82thHWmC/TnK/wpMMIA==}
    engines: {node: '>=14.0.0'}

  '@smithy/util-buffer-from@3.0.0':
    resolution: {integrity: sha512-aEOHCgq5RWFbP+UDPvPot26EJHjOC+bRgse5A8V3FSShqd5E5UN4qc7zkwsvJPPAVsf73QwYcHN1/gt/rtLwQA==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-config-provider@3.0.0':
    resolution: {integrity: sha512-pbjk4s0fwq3Di/ANL+rCvJMKM5bzAQdE5S/6RL5NXgMExFAi6UgQMPOm5yPaIWPpr+EOXKXRonJ3FoxKf4mCJQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-defaults-mode-browser@3.0.18':
    resolution: {integrity: sha512-/eveCzU6Z6Yw8dlYQLA4rcK30XY0E4L3lD3QFHm59mzDaWYelrXE1rlynuT3J6qxv+5yNy3a1JuzhG5hk5hcmw==}
    engines: {node: '>= 10.0.0'}

  '@smithy/util-defaults-mode-node@3.0.18':
    resolution: {integrity: sha512-9cfzRjArtOFPlTYRREJk00suUxVXTgbrzVncOyMRTUeMKnecG/YentLF3cORa+R6mUOMSrMSnT18jos1PKqK6Q==}
    engines: {node: '>= 10.0.0'}

  '@smithy/util-endpoints@2.1.2':
    resolution: {integrity: sha512-FEISzffb4H8DLzGq1g4MuDpcv6CIG15fXoQzDH9SjpRJv6h7J++1STFWWinilG0tQh9H1v2UKWG19Jjr2B16zQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-hex-encoding@3.0.0':
    resolution: {integrity: sha512-eFndh1WEK5YMUYvy3lPlVmYY/fZcQE1D8oSf41Id2vCeIkKJXPcYDCZD+4+xViI6b1XSd7tE+s5AmXzz5ilabQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-middleware@3.0.6':
    resolution: {integrity: sha512-BxbX4aBhI1O9p87/xM+zWy0GzT3CEVcXFPBRDoHAM+pV0eSW156pR+PSYEz0DQHDMYDsYAflC2bQNz2uaDBUZQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-retry@3.0.6':
    resolution: {integrity: sha512-BRZiuF7IwDntAbevqMco67an0Sr9oLQJqqRCsSPZZHYRnehS0LHDAkJk/pSmI7Z8c/1Vet294H7fY2fWUgB+Rg==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-stream@3.1.6':
    resolution: {integrity: sha512-lQEUfTx1ht5CRdvIjdAN/gUL6vQt2wSARGGLaBHNe+iJSkRHlWzY+DOn0mFTmTgyU3jcI5n9DkT5gTzYuSOo6A==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-uri-escape@3.0.0':
    resolution: {integrity: sha512-LqR7qYLgZTD7nWLBecUi4aqolw8Mhza9ArpNEQ881MJJIU2sE5iHCK6TdyqqzcDLy0OPe10IY4T8ctVdtynubg==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-utf8@2.3.0':
    resolution: {integrity: sha512-R8Rdn8Hy72KKcebgLiv8jQcQkXoLMOGGv5uI1/k0l+snqkOzQ1R0ChUBCxWMlBsFMekWjq0wRudIweFs7sKT5A==}
    engines: {node: '>=14.0.0'}

  '@smithy/util-utf8@3.0.0':
    resolution: {integrity: sha512-rUeT12bxFnplYDe815GXbq/oixEGHfRFFtcTF3YdDi/JaENIM6aSYYLJydG83UNzLXeRI5K8abYd/8Sp/QM0kA==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-waiter@3.1.5':
    resolution: {integrity: sha512-jYOSvM3H6sZe3CHjzD2VQNCjWBJs+4DbtwBMvUp9y5EnnwNa7NQxTeYeQw0CKCAdGGZ3QvVkyJmvbvs5M/B10A==}
    engines: {node: '>=16.0.0'}

  '@stitches/react@1.2.8':
    resolution: {integrity: sha512-9g9dWI4gsSVe8bNLlb+lMkBYsnIKCZTmvqvDG+Avnn69XfmHZKiaMrx7cgTaddq7aTPPmXiTsbFcUy0xgI4+wA==}
    peerDependencies:
      react: '>= 16.3.0'

  '@stripe/react-stripe-js@2.8.0':
    resolution: {integrity: sha512-Vf1gNEuBxA9EtxiLghm2ZWmgbADNMJw4HW6eolUu0DON/6mZvWZgk0KHolN0sozNJwYp0i/8hBsDBcBUWcvnbw==}
    peerDependencies:
      '@stripe/stripe-js': ^1.44.1 || ^2.0.0 || ^3.0.0 || ^4.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
      react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0

  '@stripe/stripe-js@1.54.2':
    resolution: {integrity: sha512-R1PwtDvUfs99cAjfuQ/WpwJ3c92+DAMy9xGApjqlWQMj0FKQabUAys2swfTRNzuYAYJh7NqK2dzcYVNkKLEKUg==}

  '@swc/helpers@0.5.15':
    resolution: {integrity: sha512-JQ5TuMi45Owi4/BIMAJBoSQoOJu12oOk/gADqlcUL9JEdHB8vyjUSsxqeNXnmXHjYKMi2WcYtezGEEhqUI/E2g==}

  '@tailwindcss/aspect-ratio@0.4.2':
    resolution: {integrity: sha512-8QPrypskfBa7QIMuKHg2TA7BqES6vhBrDLOv8Unb6FcFyd3TjKbc6lcmb9UPQHxfl24sXoJ41ux/H7qQQvfaSQ==}
    peerDependencies:
      tailwindcss: '>=2.0.0 || >=3.0.0 || >=3.0.0-alpha.1'

  '@tailwindcss/forms@0.5.9':
    resolution: {integrity: sha512-tM4XVr2+UVTxXJzey9Twx48c1gcxFStqn1pQz0tRsX8o3DvxhN5oY5pvyAbUx7VTaZxpej4Zzvc6h+1RJBzpIg==}
    peerDependencies:
      tailwindcss: '>=3.0.0 || >= 3.0.0-alpha.1 || >= 4.0.0-alpha.20'

  '@tanstack/react-table@8.20.5':
    resolution: {integrity: sha512-WEHopKw3znbUZ61s9i0+i9g8drmDo6asTWbrQh8Us63DAk/M0FkmIqERew6P71HI75ksZ2Pxyuf4vvKh9rAkiA==}
    engines: {node: '>=12'}
    peerDependencies:
      react: '>=16.8'
      react-dom: '>=16.8'

  '@tanstack/react-virtual@3.10.7':
    resolution: {integrity: sha512-yeP+M0G8D+15ZFPivpuQ5hoM4Fa/PzERBx8P8EGcfEsXX3JOb9G9UUrqc47ZXAxvK+YqzM9T5qlJUYUFOwCZJw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
      react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0

  '@tanstack/table-core@8.20.5':
    resolution: {integrity: sha512-P9dF7XbibHph2PFRz8gfBKEXEY/HJPOhym8CHmjF8y3q5mWpKx9xtZapXQUWCgkqvsK0R46Azuz+VaxD4Xl+Tg==}
    engines: {node: '>=12'}

  '@tanstack/virtual-core@3.10.7':
    resolution: {integrity: sha512-ND5dfsU0n9F4gROzwNNDJmg6y8n9pI8YWxtgbfJ5UcNn7Hx+MxEXtXcQ189tS7sh8pmCObgz2qSiyRKTZxT4dg==}

  '@tremor/react@3.18.1':
    resolution: {integrity: sha512-Tyt6pDsPUyVr6cIDZIVgO2gIdLMG7R8oqhA/i/yBnAAAsljfIAIV6azP/jIVIqbvar6mczm5wwbSX8eOfc0yVQ==}
    peerDependencies:
      react: ^18.0.0
      react-dom: '>=16.6.0'

  '@types/body-parser@1.19.5':
    resolution: {integrity: sha512-fB3Zu92ucau0iQ0JMCFQE7b/dv8Ot07NI3KaZIkIUNXq82k4eBAqUaneXfleGY9JWskeS9y+u0nXMyspcuQrCg==}

  '@types/connect@3.4.38':
    resolution: {integrity: sha512-K6uROf1LD88uDQqJCktA4yzL1YYAK6NgfsI0v/mTgyPKWsX1CnJ0XPSDhViejru1GcRkLWb8RlzFYJRqGUbaug==}

  '@types/cookies@0.7.7':
    resolution: {integrity: sha512-h7BcvPUogWbKCzBR2lY4oqaZbO3jXZksexYJVFvkrFeLgbZjQkU4x8pRq6eg2MHXQhY0McQdqmmsxRWlVAHooA==}

  '@types/d3-array@3.2.1':
    resolution: {integrity: sha512-Y2Jn2idRrLzUfAKV2LyRImR+y4oa2AntrgID95SHJxuMUrkNXmanDSed71sRNZysveJVt1hLLemQZIady0FpEg==}

  '@types/d3-axis@3.0.6':
    resolution: {integrity: sha512-pYeijfZuBd87T0hGn0FO1vQ/cgLk6E1ALJjfkC0oJ8cbwkZl3TpgS8bVBLZN+2jjGgg38epgxb2zmoGtSfvgMw==}

  '@types/d3-brush@3.0.6':
    resolution: {integrity: sha512-nH60IZNNxEcrh6L1ZSMNA28rj27ut/2ZmI3r96Zd+1jrZD++zD3LsMIjWlvg4AYrHn/Pqz4CF3veCxGjtbqt7A==}

  '@types/d3-chord@3.0.6':
    resolution: {integrity: sha512-LFYWWd8nwfwEmTZG9PfQxd17HbNPksHBiJHaKuY1XeqscXacsS2tyoo6OdRsjf+NQYeB6XrNL3a25E3gH69lcg==}

  '@types/d3-color@3.1.3':
    resolution: {integrity: sha512-iO90scth9WAbmgv7ogoq57O9YpKmFBbmoEoCHDB2xMBY0+/KVrqAaCDyCE16dUspeOvIxFFRI+0sEtqDqy2b4A==}

  '@types/d3-contour@3.0.6':
    resolution: {integrity: sha512-BjzLgXGnCWjUSYGfH1cpdo41/hgdWETu4YxpezoztawmqsvCeep+8QGfiY6YbDvfgHz/DkjeIkkZVJavB4a3rg==}

  '@types/d3-delaunay@6.0.4':
    resolution: {integrity: sha512-ZMaSKu4THYCU6sV64Lhg6qjf1orxBthaC161plr5KuPHo3CNm8DTHiLw/5Eq2b6TsNP0W0iJrUOFscY6Q450Hw==}

  '@types/d3-dispatch@3.0.6':
    resolution: {integrity: sha512-4fvZhzMeeuBJYZXRXrRIQnvUYfyXwYmLsdiN7XXmVNQKKw1cM8a5WdID0g1hVFZDqT9ZqZEY5pD44p24VS7iZQ==}

  '@types/d3-drag@3.0.7':
    resolution: {integrity: sha512-HE3jVKlzU9AaMazNufooRJ5ZpWmLIoc90A37WU2JMmeq28w1FQqCZswHZ3xR+SuxYftzHq6WU6KJHvqxKzTxxQ==}

  '@types/d3-dsv@3.0.7':
    resolution: {integrity: sha512-n6QBF9/+XASqcKK6waudgL0pf/S5XHPPI8APyMLLUHd8NqouBGLsU8MgtO7NINGtPBtk9Kko/W4ea0oAspwh9g==}

  '@types/d3-ease@3.0.2':
    resolution: {integrity: sha512-NcV1JjO5oDzoK26oMzbILE6HW7uVXOHLQvHshBUW4UMdZGfiY6v5BeQwh9a9tCzv+CeefZQHJt5SRgK154RtiA==}

  '@types/d3-fetch@3.0.7':
    resolution: {integrity: sha512-fTAfNmxSb9SOWNB9IoG5c8Hg6R+AzUHDRlsXsDZsNp6sxAEOP0tkP3gKkNSO/qmHPoBFTxNrjDprVHDQDvo5aA==}

  '@types/d3-force@3.0.10':
    resolution: {integrity: sha512-ZYeSaCF3p73RdOKcjj+swRlZfnYpK1EbaDiYICEEp5Q6sUiqFaFQ9qgoshp5CzIyyb/yD09kD9o2zEltCexlgw==}

  '@types/d3-format@3.0.4':
    resolution: {integrity: sha512-fALi2aI6shfg7vM5KiR1wNJnZ7r6UuggVqtDA+xiEdPZQwy/trcQaHnwShLuLdta2rTymCNpxYTiMZX/e09F4g==}

  '@types/d3-geo@3.1.0':
    resolution: {integrity: sha512-856sckF0oP/diXtS4jNsiQw/UuK5fQG8l/a9VVLeSouf1/PPbBE1i1W852zVwKwYCBkFJJB7nCFTbk6UMEXBOQ==}

  '@types/d3-hierarchy@3.1.7':
    resolution: {integrity: sha512-tJFtNoYBtRtkNysX1Xq4sxtjK8YgoWUNpIiUee0/jHGRwqvzYxkq0hGVbbOGSz+JgFxxRu4K8nb3YpG3CMARtg==}

  '@types/d3-interpolate@3.0.4':
    resolution: {integrity: sha512-mgLPETlrpVV1YRJIglr4Ez47g7Yxjl1lj7YKsiMCb27VJH9W8NVM6Bb9d8kkpG/uAQS5AmbA48q2IAolKKo1MA==}

  '@types/d3-path@3.1.0':
    resolution: {integrity: sha512-P2dlU/q51fkOc/Gfl3Ul9kicV7l+ra934qBFXCFhrZMOL6du1TM0pm1ThYvENukyOn5h9v+yMJ9Fn5JK4QozrQ==}

  '@types/d3-polygon@3.0.2':
    resolution: {integrity: sha512-ZuWOtMaHCkN9xoeEMr1ubW2nGWsp4nIql+OPQRstu4ypeZ+zk3YKqQT0CXVe/PYqrKpZAi+J9mTs05TKwjXSRA==}

  '@types/d3-quadtree@3.0.6':
    resolution: {integrity: sha512-oUzyO1/Zm6rsxKRHA1vH0NEDG58HrT5icx/azi9MF1TWdtttWl0UIUsjEQBBh+SIkrpd21ZjEv7ptxWys1ncsg==}

  '@types/d3-random@3.0.3':
    resolution: {integrity: sha512-Imagg1vJ3y76Y2ea0871wpabqp613+8/r0mCLEBfdtqC7xMSfj9idOnmBYyMoULfHePJyxMAw3nWhJxzc+LFwQ==}

  '@types/d3-scale-chromatic@3.0.3':
    resolution: {integrity: sha512-laXM4+1o5ImZv3RpFAsTRn3TEkzqkytiOY0Dz0sq5cnd1dtNlk6sHLon4OvqaiJb28T0S/TdsBI3Sjsy+keJrw==}

  '@types/d3-scale@4.0.8':
    resolution: {integrity: sha512-gkK1VVTr5iNiYJ7vWDI+yUFFlszhNMtVeneJ6lUTKPjprsvLLI9/tgEGiXJOnlINJA8FyA88gfnQsHbybVZrYQ==}

  '@types/d3-selection@3.0.10':
    resolution: {integrity: sha512-cuHoUgS/V3hLdjJOLTT691+G2QoqAjCVLmr4kJXR4ha56w1Zdu8UUQ5TxLRqudgNjwXeQxKMq4j+lyf9sWuslg==}

  '@types/d3-shape@3.1.6':
    resolution: {integrity: sha512-5KKk5aKGu2I+O6SONMYSNflgiP0WfZIQvVUMan50wHsLG1G94JlxEVnCpQARfTtzytuY0p/9PXXZb3I7giofIA==}

  '@types/d3-time-format@4.0.3':
    resolution: {integrity: sha512-5xg9rC+wWL8kdDj153qZcsJ0FWiFt0J5RB6LYUNZjwSnesfblqrI/bJ1wBdJ8OQfncgbJG5+2F+qfqnqyzYxyg==}

  '@types/d3-time@3.0.3':
    resolution: {integrity: sha512-2p6olUZ4w3s+07q3Tm2dbiMZy5pCDfYwtLXXHUnVzXgQlZ/OyPtUz6OL382BkOuGlLXqfT+wqv8Fw2v8/0geBw==}

  '@types/d3-timer@3.0.2':
    resolution: {integrity: sha512-Ps3T8E8dZDam6fUyNiMkekK3XUsaUEik+idO9/YjPtfj2qruF8tFBXS7XhtE4iIXBLxhmLjP3SXpLhVf21I9Lw==}

  '@types/d3-transition@3.0.8':
    resolution: {integrity: sha512-ew63aJfQ/ms7QQ4X7pk5NxQ9fZH/z+i24ZfJ6tJSfqxJMrYLiK01EAs2/Rtw/JreGUsS3pLPNV644qXFGnoZNQ==}

  '@types/d3-zoom@3.0.8':
    resolution: {integrity: sha512-iqMC4/YlFCSlO8+2Ii1GGGliCAY4XdeG748w5vQUbevlbDu0zSjH/+jojorQVBK/se0j6DUFNPBGSqD3YWYnDw==}

  '@types/d3@7.4.3':
    resolution: {integrity: sha512-lZXZ9ckh5R8uiFVt8ogUNf+pIrK4EsWrx2Np75WvF/eTpJ0FMHNhjXk8CKEx/+gpHbNQyJWehbFaTvqmHWB3ww==}

  '@types/express-serve-static-core@4.19.5':
    resolution: {integrity: sha512-y6W03tvrACO72aijJ5uF02FRq5cgDR9lUxddQ8vyF+GvmjJQqbzDcJngEjURc+ZsG31VI3hODNZJ2URj86pzmg==}

  '@types/express@4.17.14':
    resolution: {integrity: sha512-TEbt+vaPFQ+xpxFLFssxUDXj5cWCxZJjIcB7Yg0k0GMHGtgtQgpvx/MUQUeAkNbA9AAGrwkAsoeItdTgS7FMyg==}

  '@types/geojson@7946.0.14':
    resolution: {integrity: sha512-WCfD5Ht3ZesJUsONdhvm84dmzWOiOzOAqOncN0++w0lBw1o8OuDNJF2McvvCef/yBqb/HYRahp1BYtODFQ8bRg==}

  '@types/hast@2.3.10':
    resolution: {integrity: sha512-McWspRw8xx8J9HurkVBfYj0xKoE25tOFlHGdx4MJ5xORQrMGZNqJhVQWaIbm6Oyla5kYOXtDiopzKRJzEOkwJw==}

  '@types/http-errors@2.0.4':
    resolution: {integrity: sha512-D0CFMMtydbJAegzOyHjtiKPLlvnm3iTZyZRSZoLq2mRhDdmLfIWOCYPfQJ4cu2erKghU++QvjcUjp/5h7hESpA==}

  '@types/json-schema@7.0.15':
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==}

  '@types/json5@0.0.29':
    resolution: {integrity: sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ==}

  '@types/keygrip@1.0.6':
    resolution: {integrity: sha512-lZuNAY9xeJt7Bx4t4dx0rYCDqGPW8RXhQZK1td7d4H6E9zYbLoOtjBvfwdTKpsyxQI/2jv+armjX/RW+ZNpXOQ==}

  '@types/lodash.mergewith@4.6.7':
    resolution: {integrity: sha512-3m+lkO5CLRRYU0fhGRp7zbsGi6+BZj0uTVSwvcKU+nSlhjA9/QRNfuSGnD2mX6hQA7ZbmcCkzk5h4ZYGOtk14A==}

  '@types/lodash@4.17.7':
    resolution: {integrity: sha512-8wTvZawATi/lsmNu10/j2hk1KEP0IvjubqPE3cu1Xz7xfXXt5oCq3SNUz4fMIP4XGF9Ky+Ue2tBA3hcS7LSBlA==}

  '@types/mime@1.3.5':
    resolution: {integrity: sha512-/pyBZWSLD2n0dcHE3hq8s8ZvcETHtEuF+3E7XVt0Ig2nvsVQXdghHVcEkIWjy9A0wKfTn97a/PSDYohKIlnP/w==}

  '@types/node-fetch@2.6.11':
    resolution: {integrity: sha512-24xFj9R5+rfQJLRyM56qh+wnVSYhyXC2tkoBndtY0U+vubqNsYXGjufB2nn8Q6gt0LrARwL6UBtMCSVCwl4B1g==}

  '@types/node-fetch@2.6.2':
    resolution: {integrity: sha512-DHqhlq5jeESLy19TYhLakJ07kNumXWjcDdxXsLUMJZ6ue8VZJj4kLPQVE/2mdHh3xZziNF1xppu5lwmS53HR+A==}

  '@types/node@16.18.6':
    resolution: {integrity: sha512-vmYJF0REqDyyU0gviezF/KHq/fYaUbFhkcNbQCuPGFQj6VTbXuHZoxs/Y7mutWe73C8AC6l9fFu8mSYiBAqkGA==}

  '@types/node@18.19.50':
    resolution: {integrity: sha512-xonK+NRrMBRtkL1hVCc3G+uXtjh1Al4opBLjqVmipe5ZAaBYWW6cNAiBVZ1BvmkBhep698rP3UM3aRAdSALuhg==}

  '@types/parse-json@4.0.2':
    resolution: {integrity: sha512-dISoDXWWQwUquiKsyZ4Ng+HX2KsPL7LyHKHQwgGFEA3IaKac4Obd+h2a/a6waisAoepJlBcx9paWqjA8/HVjCw==}

  '@types/pg@8.12.0':
    resolution: {integrity: sha512-a9Z11ecnpNPFu2iT4Qo9SSYgM2r1l4UqLIQ454zhCDRzxqOh/vsi57FFovbc64oBGPBotXw5cRhUQtJEHCb/OA==}

  '@types/prop-types@15.7.12':
    resolution: {integrity: sha512-5zvhXYtRNRluoE/jAp4GVsSduVUzNWKkOZrCDBWYtE7biZywwdC2AcEzg+cSMLFRfVgeAFqpfNabiPjxFddV1Q==}

  '@types/qs@6.9.16':
    resolution: {integrity: sha512-7i+zxXdPD0T4cKDuxCUXJ4wHcsJLwENa6Z3dCu8cfCK743OGy5Nu1RmAGqDPsoTDINVEcdXKRvR/zre+P2Ku1A==}

  '@types/range-parser@1.2.7':
    resolution: {integrity: sha512-hKormJbkJqzQGhziax5PItDUTMAM9uE2XXQmM37dyd4hVM+5aVl7oVxMVUiVQn2oCQFN/LKCZdvSM0pFRqbSmQ==}

  '@types/react-dom@18.3.0':
    resolution: {integrity: sha512-EhwApuTmMBmXuFOikhQLIBUn6uFg81SwLMOAUgodJF14SOBOCMdU04gDoYi0WOJJHD144TL32z4yDqCW3dnkQg==}

  '@types/react-reconciler@0.28.8':
    resolution: {integrity: sha512-SN9c4kxXZonFhbX4hJrZy37yw9e7EIxcpHCxQv5JUS18wDE5ovkQKlqQEkufdJCCMfuI9BnjUJvhYeJ9x5Ra7g==}

  '@types/react-transition-group@4.4.11':
    resolution: {integrity: sha512-RM05tAniPZ5DZPzzNFP+DmrcOdD0efDUxMy3145oljWSl3x9ZV5vhme98gTxFrj2lhXvmGNnUiuDyJgY9IKkNA==}

  '@types/react@18.3.5':
    resolution: {integrity: sha512-WeqMfGJLGuLCqHGYRGHxnKrXcTitc6L/nBUWfWPcTarG3t9PsquqUMuVeXZeca+mglY4Vo5GZjCi0A3Or2lnxA==}

  '@types/semver@7.5.8':
    resolution: {integrity: sha512-I8EUhyrgfLrcTkzV3TSsGyl1tSuPrEDzr0yd5m90UgNxQkyDXULk3b6MlQqTCpZpNtWe1K0hzclnZkTcLBe2UQ==}

  '@types/send@0.17.4':
    resolution: {integrity: sha512-x2EM6TJOybec7c52BX0ZspPodMsQUd5L6PRwOunVyVUhXiBSKf3AezDL8Dgvgt5o0UfKNfuA0eMLr2wLT4AiBA==}

  '@types/serve-static@1.15.7':
    resolution: {integrity: sha512-W8Ym+h8nhuRwaKPaDw34QUkwsGi6Rc4yYqvKFo5rm2FUEhCFbzVWrxXUxuKK8TASjWsysJY0nsmNCGhCOIsrOw==}

  '@types/stylis@4.2.5':
    resolution: {integrity: sha512-1Xve+NMN7FWjY14vLoY5tL3BVEQ/n42YLwaqJIPYhotZ9uBHt87VceMwWQpzmdEt2TNXIorIFG+YeCUUW7RInw==}

  '@types/unist@2.0.11':
    resolution: {integrity: sha512-CmBKiL6NNo/OqgmMn95Fk9Whlp2mtvIv+KNpQKN2F4SjvrEesubTRWGYSg+BnWZOnlCaSTU1sMpsBOzgbYhnsA==}

  '@typescript-eslint/eslint-plugin@5.62.0':
    resolution: {integrity: sha512-TiZzBSJja/LbhNPvk6yc0JrX9XqhQ0hdh6M2svYfsHGejaKFIAGd9MQ+ERIMzLGlN/kZoYIgdxFV0PuljTKXag==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      '@typescript-eslint/parser': ^5.0.0
      eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/parser@5.62.0':
    resolution: {integrity: sha512-VlJEV0fOQ7BExOsHYAGrgbEiZoi8D+Bl2+f6V2RrXerRSylnp+ZBHmPvaIa8cz0Ajx7WO7Z5RqfgYg7ED1nRhA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/scope-manager@5.62.0':
    resolution: {integrity: sha512-VXuvVvZeQCQb5Zgf4HAxc04q5j+WrNAtNh9OwCsCgpKqESMTu3tF/jhZ3xG6T4NZwWl65Bg8KuS2uEvhSfLl0w==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@typescript-eslint/type-utils@5.62.0':
    resolution: {integrity: sha512-xsSQreu+VnfbqQpW5vnCJdq1Z3Q0U31qiWmRhr98ONQmcp/yhiPJFPq8MXiJVLiksmOKSjIldZzkebzHuCGzew==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: '*'
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/types@5.62.0':
    resolution: {integrity: sha512-87NVngcbVXUahrRTqIK27gD2t5Cu1yuCXxbLcFtCzZGlfyVWWh8mLHkoxzjsB6DDNnvdL+fW8MiwPEJyGJQDgQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@typescript-eslint/typescript-estree@5.62.0':
    resolution: {integrity: sha512-CmcQ6uY7b9y694lKdRB8FEel7JbU/40iSAPomu++SjLMntB+2Leay2LO6i8VnJk58MtE9/nQSFIH6jpyRWyYzA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/utils@5.62.0':
    resolution: {integrity: sha512-n8oxjeb5aIbPFEtmQxQYOLI0i9n5ySBEY/ZEHHZqKQSFnxio1rv6dthascc9dLuwrL0RC5mPCxB7vnAVGAYWAQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || ^8.0.0

  '@typescript-eslint/visitor-keys@5.62.0':
    resolution: {integrity: sha512-07ny+LHRzQXepkGg6w0mFY41fVUNBrL2Roj/++7V1txKugfjm/Ci/qSND03r2RhlJhJYMcTn9AhhSSqQp0Ysyw==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@ungap/structured-clone@1.2.0':
    resolution: {integrity: sha512-zuVdFrMJiuCDQUMCzQaD6KL28MjnqqN8XnAqiEq9PNm/hCPTSGfrXCOfwj1ow4LFb/tNymJPwsNbVePc1xFqrQ==}

  '@xata.io/client@0.29.5':
    resolution: {integrity: sha512-b55dmPVNVFOE5nj2F2G6t9l/d5yYBhIu5X5w3rznhhsriGHkrzn93tqJexIZPS77E7f/yDXcFz06KbvR3bHK5w==}
    peerDependencies:
      typescript: '>=4.5'

  '@zag-js/dom-query@0.16.0':
    resolution: {integrity: sha512-Oqhd6+biWyKnhKwFFuZrrf6lxBz2tX2pRQe6grUnYwO6HJ8BcbqZomy2lpOdr+3itlaUqx+Ywj5E5ZZDr/LBfQ==}

  '@zag-js/element-size@0.10.5':
    resolution: {integrity: sha512-uQre5IidULANvVkNOBQ1tfgwTQcGl4hliPSe69Fct1VfYb2Fd0jdAcGzqQgPhfrXFpR62MxLPB7erxJ/ngtL8w==}

  '@zag-js/focus-visible@0.16.0':
    resolution: {integrity: sha512-a7U/HSopvQbrDU4GLerpqiMcHKEkQkNPeDZJWz38cw/6Upunh41GjHetq5TB84hxyCaDzJ6q2nEdNoBQfC0FKA==}

  abort-controller@3.0.0:
    resolution: {integrity: sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==}
    engines: {node: '>=6.5'}

  acorn-jsx@5.3.2:
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn@8.12.1:
    resolution: {integrity: sha512-tcpGyI9zbizT9JbV6oYE477V6mTlXvvi0T0G3SNIYE2apm/G5huBa1+K89VGeovbg+jycCrfhl3ADxErOuO6Jg==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  agentkeepalive@4.5.0:
    resolution: {integrity: sha512-5GG/5IbQQpC9FpkRGsSvZI5QYeSCzlJHdpBQntCsuTOxhKD8lqKhrleg2Yi7yvMIf82Ycmmqln9U8V9qwEiJew==}
    engines: {node: '>= 8.0.0'}

  ajv@6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}

  ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}

  ansi-regex@6.1.0:
    resolution: {integrity: sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==}
    engines: {node: '>=12'}

  ansi-styles@3.2.1:
    resolution: {integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==}
    engines: {node: '>=4'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}

  ansi-styles@6.2.1:
    resolution: {integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==}
    engines: {node: '>=12'}

  any-promise@1.3.0:
    resolution: {integrity: sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==}

  anymatch@3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    engines: {node: '>= 8'}

  arg@5.0.2:
    resolution: {integrity: sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==}

  argparse@2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}

  aria-hidden@1.2.4:
    resolution: {integrity: sha512-y+CcFFwelSXpLZk/7fMB2mUbGtX9lKycf1MWJ7CaTIERyitVlyQx6C+sxcROU2BAJ24OiZyK+8wj2i8AlBoS3A==}
    engines: {node: '>=10'}

  aria-query@5.1.3:
    resolution: {integrity: sha512-R5iJ5lkuHybztUfuOAznmboyjWq8O6sqNqtK7CLOqdydi54VNbORp49mb14KbWgG1QD3JFO9hJdZ+y4KutfdOQ==}

  array-buffer-byte-length@1.0.1:
    resolution: {integrity: sha512-ahC5W1xgou+KTXix4sAO8Ki12Q+jf4i0+tmk3sC+zgcynshkHxzpXdImBehiUYKKKDwvfFiJl1tZt6ewscS1Mg==}
    engines: {node: '>= 0.4'}

  array-includes@3.1.8:
    resolution: {integrity: sha512-itaWrbYbqpGXkGhZPGUulwnhVf5Hpy1xiCFsGqyIGglbBxmG5vSjxQen3/WGOjPpNEv1RtBLKxbmVXm8HpJStQ==}
    engines: {node: '>= 0.4'}

  array-union@2.1.0:
    resolution: {integrity: sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==}
    engines: {node: '>=8'}

  array.prototype.findlast@1.2.5:
    resolution: {integrity: sha512-CVvd6FHg1Z3POpBLxO6E6zr+rSKEQ9L6rZHAaY7lLfhKsWYUBBOuMs0e9o24oopj6H+geRCX0YJ+TJLBK2eHyQ==}
    engines: {node: '>= 0.4'}

  array.prototype.findlastindex@1.2.5:
    resolution: {integrity: sha512-zfETvRFA8o7EiNn++N5f/kaCw221hrpGsDmcpndVupkPzEc1Wuf3VgC0qby1BbHs7f5DVYjgtEU2LLh5bqeGfQ==}
    engines: {node: '>= 0.4'}

  array.prototype.flat@1.3.2:
    resolution: {integrity: sha512-djYB+Zx2vLewY8RWlNCUdHjDXs2XOgm602S9E7P/UpHgfeHL00cRiIF+IN/G/aUJ7kGPb6yO/ErDI5V2s8iycA==}
    engines: {node: '>= 0.4'}

  array.prototype.flatmap@1.3.2:
    resolution: {integrity: sha512-Ewyx0c9PmpcsByhSW4r+9zDU7sGjFc86qf/kKtuSCRdhfbk0SNLLkaT5qvcHnRGgc5NP/ly/y+qkXkqONX54CQ==}
    engines: {node: '>= 0.4'}

  array.prototype.tosorted@1.1.4:
    resolution: {integrity: sha512-p6Fx8B7b7ZhL/gmUsAy0D15WhvDccw3mnGNbZpi3pmeJdxtWsj2jEaI4Y6oo3XiHfzuSgPwKc04MYt6KgvC/wA==}
    engines: {node: '>= 0.4'}

  arraybuffer.prototype.slice@1.0.3:
    resolution: {integrity: sha512-bMxMKAjg13EBSVscxTaYA4mRc5t1UAXa2kXiGTNfZ079HIWXEkKmkgFrh/nJqamaLSrXO5H4WFFkPEaLJWbs3A==}
    engines: {node: '>= 0.4'}

  asn1js@3.0.5:
    resolution: {integrity: sha512-FVnvrKJwpt9LP2lAMl8qZswRNm3T4q9CON+bxldk2iwk3FFpuwhx2FfinyitizWHsVYyaY+y5JzDR0rCMV5yTQ==}
    engines: {node: '>=12.0.0'}

  ast-types-flow@0.0.8:
    resolution: {integrity: sha512-OH/2E5Fg20h2aPrbe+QL8JZQFko0YZaF+j4mnQ7BGhfavO7OpSLa8a0y9sBwomHdSbkhTS8TQNayBfnW5DwbvQ==}

  asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}

  available-typed-arrays@1.0.7:
    resolution: {integrity: sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==}
    engines: {node: '>= 0.4'}

  axe-core@4.10.0:
    resolution: {integrity: sha512-Mr2ZakwQ7XUAjp7pAwQWRhhK8mQQ6JAaNWSjmjxil0R8BPioMtQsTLOolGYkji1rcL++3dCqZA3zWqpT+9Ew6g==}
    engines: {node: '>=4'}

  axios@1.7.7:
    resolution: {integrity: sha512-S4kL7XrjgBmvdGut0sN3yJxqYzrDOnivkBiN0OFs6hLiUam3UPvswUo0kqGyhqUZGEOytHyumEdXsAkgCOUf3Q==}

  axobject-query@4.1.0:
    resolution: {integrity: sha512-qIj0G9wZbMGNLjLmg1PT6v2mE9AH2zlnADJD/2tC6E00hgmhUOfEB6greHPAfLRSufHqROIUTkw6E+M3lH0PTQ==}
    engines: {node: '>= 0.4'}

  babel-plugin-macros@3.1.0:
    resolution: {integrity: sha512-Cg7TFGpIr01vOQNODXOOaGz2NpCU5gl8x1qJFbb6hbZxR7XrcE2vtbAsTAbJ7/xwJtUuJEw8K8Zr/AE0LHlesg==}
    engines: {node: '>=10', npm: '>=6'}

  balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  base-64@1.0.0:
    resolution: {integrity: sha512-kwDPIFCGx0NZHog36dj+tHiwP4QMzsZ3AgMViUBKI0+V5n4U0ufTCUMhnQ04diaRI8EX/QcPfql7zlhZ7j4zgg==}

  binary-extensions@2.3.0:
    resolution: {integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==}
    engines: {node: '>=8'}

  bowser@2.11.0:
    resolution: {integrity: sha512-AlcaJBi/pqqJBIQ8U9Mcpc9i8Aqxn88Skv5d+xBX006BY5u8N3mGLHa5Lgppa7L/HfwgwLgZ6NYs+Ag6uUmJRA==}

  brace-expansion@1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==}

  brace-expansion@2.0.1:
    resolution: {integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==}

  braces@3.0.3:
    resolution: {integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==}
    engines: {node: '>=8'}

  call-bind@1.0.7:
    resolution: {integrity: sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==}
    engines: {node: '>= 0.4'}

  callsites@3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}

  camelcase-css@2.0.1:
    resolution: {integrity: sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==}
    engines: {node: '>= 6'}

  camelcase-keys@6.2.2:
    resolution: {integrity: sha512-YrwaA0vEKazPBkn0ipTiMpSajYDSe+KjQfrjhcBMxJt/znbvlHd8Pw/Vamaz5EB4Wfhs3SUR3Z9mwRu/P3s3Yg==}
    engines: {node: '>=8'}

  camelcase@5.3.1:
    resolution: {integrity: sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==}
    engines: {node: '>=6'}

  camelize@1.0.1:
    resolution: {integrity: sha512-dU+Tx2fsypxTgtLoE36npi3UqcjSSMNYfkqgmoEhtZrraP5VWq0K7FkWVTYa8eMPtnU/G2txVsfdCJTn9uzpuQ==}

  caniuse-lite@1.0.30001660:
    resolution: {integrity: sha512-GacvNTTuATm26qC74pt+ad1fW15mlQ/zuTzzY1ZoIzECTP8HURDfF43kNxPgf7H1jmelCBQTTbBNxdSXOA7Bqg==}

  canvas-confetti@1.9.3:
    resolution: {integrity: sha512-rFfTURMvmVEX1gyXFgn5QMn81bYk70qa0HLzcIOSVEyl57n6o9ItHeBtUSWdvKAPY0xlvBHno4/v3QPrT83q9g==}

  chalk@2.4.2:
    resolution: {integrity: sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==}
    engines: {node: '>=4'}

  chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}

  character-entities-legacy@1.1.4:
    resolution: {integrity: sha512-3Xnr+7ZFS1uxeiUDvV02wQ+QDbc55o97tIV5zHScSPJpcLm/r0DFPcoY3tYRp+VZukxuMeKgXYmsXQHO05zQeA==}

  character-entities@1.2.4:
    resolution: {integrity: sha512-iBMyeEHxfVnIakwOuDXpVkc54HijNgCyQB2w0VfGQThle6NXn50zU6V/u+LDhxHcDUPojn6Kpga3PTAD8W1bQw==}

  character-reference-invalid@1.1.4:
    resolution: {integrity: sha512-mKKUkUbhPpQlCOfIuZkvSEgktjPFIsZKRRbC6KWVEMvlzblj3i3asQv5ODsrwt0N3pHAEvjP8KTQPHkp0+6jOg==}

  chart.js@4.4.4:
    resolution: {integrity: sha512-emICKGBABnxhMjUjlYRR12PmOXhJ2eJjEHL2/dZlWjxRAZT1D8xplLFq5M0tMQK8ja+wBS/tuVEJB5C6r7VxJA==}
    engines: {pnpm: '>=8'}

  chartjs-adapter-date-fns@3.0.0:
    resolution: {integrity: sha512-Rs3iEB3Q5pJ973J93OBTpnP7qoGwvq3nUnoMdtxO+9aoJof7UFcRbWcIDteXuYd1fgAvct/32T9qaLyLuZVwCg==}
    peerDependencies:
      chart.js: '>=2.8.0'
      date-fns: '>=2.0.0'

  chartjs-adapter-luxon@1.3.1:
    resolution: {integrity: sha512-yxHov3X8y+reIibl1o+j18xzrcdddCLqsXhriV2+aQ4hCR66IYFchlRXUvrJVoxglJ380pgytU7YWtoqdIgqhg==}
    peerDependencies:
      chart.js: '>=3.0.0'
      luxon: '>=1.0.0'

  chartjs-plugin-streaming@2.0.0:
    resolution: {integrity: sha512-oBwHRmovGbPDS1h8PhpttnlKjI6eiJO13v4h+xz+mfbHhIc+iNF9xuMobCIFMKFH7rlVWi5TJAOIxoXyGElYYg==}
    peerDependencies:
      chart.js: ^3.0.0

  chartjs-plugin-zoom@2.0.1:
    resolution: {integrity: sha512-ogOmLu6e+Q7E1XWOCOz9YwybMslz9qNfGV2a+qjfmqJYpsw5ZMoRHZBUyW+NGhkpQ5PwwPA/+rikHpBZb7PZuA==}
    peerDependencies:
      chart.js: '>=3.2.0'

  chokidar@3.6.0:
    resolution: {integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==}
    engines: {node: '>= 8.10.0'}

  class-variance-authority@0.7.0:
    resolution: {integrity: sha512-jFI8IQw4hczaL4ALINxqLEXQbWcNjoSkloa4IaufXCJr6QawJyw7tuRysRsrE8w2p/4gGaxKIt/hX3qz/IbD1A==}

  classcat@5.0.5:
    resolution: {integrity: sha512-JhZUT7JFcQy/EzW605k/ktHtncoo9vnyW/2GspNYwFlN1C/WmjuV/xtS04e9SOkL2sTdw0VAZ2UGCcQ9lR6p6w==}

  client-only@0.0.1:
    resolution: {integrity: sha512-IV3Ou0jSMzZrd3pZ48nLkT9DA7Ag1pnPzaiQhpW7c3RbcqqzvzzVu+L8gfqMp/8IM2MQtSiqaCxrrcfu8I8rMA==}

  clsx@1.2.1:
    resolution: {integrity: sha512-EcR6r5a8bj6pu3ycsa/E/cKVGuTgZJZdsyUYHOksG/UHIiKfjxzRxYJpyVBwYaQeOvghal9fcc4PidlgzugAQg==}
    engines: {node: '>=6'}

  clsx@2.0.0:
    resolution: {integrity: sha512-rQ1+kcj+ttHG0MKVGBUXwayCCF1oh39BF5COIpRzuCEv8Mwjv0XucrI2ExNTOn9IlLifGClWQcU9BrZORvtw6Q==}
    engines: {node: '>=6'}

  clsx@2.1.1:
    resolution: {integrity: sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==}
    engines: {node: '>=6'}

  color-convert@1.9.3:
    resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==}

  color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}

  color-name@1.1.3:
    resolution: {integrity: sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==}

  color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  color-string@1.9.1:
    resolution: {integrity: sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==}

  color2k@2.0.3:
    resolution: {integrity: sha512-zW190nQTIoXcGCaU08DvVNFTmQhUpnJfVuAKfWqUQkflXKpaDdpaYoM0iluLS9lgJNHyBF58KKA2FBEwkD7wog==}

  color@4.2.3:
    resolution: {integrity: sha512-1rXeuUUiGGrykh+CeBdu5Ie7OJwinCgQY0bc7GCRxy5xVHy+moaqkpL/jqQq0MtQOeYcrqEz4abc5f0KtU7W4A==}
    engines: {node: '>=12.5.0'}

  combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}

  comma-separated-tokens@1.0.8:
    resolution: {integrity: sha512-GHuDRO12Sypu2cV70d1dkA2EUmXHgntrzbpvOB+Qy+49ypNfGgFQIC2fhhXbnyrJRynDCAARsT7Ou0M6hirpfw==}

  commander@4.1.1:
    resolution: {integrity: sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==}
    engines: {node: '>= 6'}

  compute-scroll-into-view@3.0.3:
    resolution: {integrity: sha512-nadqwNxghAGTamwIqQSG433W6OADZx2vCo3UXHNrzTRHK/htu+7+L0zhjEoaeaQVNAi3YgqWDv8+tzf0hRfR+A==}

  concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}

  convert-source-map@1.9.0:
    resolution: {integrity: sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==}

  cookie@0.5.0:
    resolution: {integrity: sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw==}
    engines: {node: '>= 0.6'}

  copy-to-clipboard@3.3.3:
    resolution: {integrity: sha512-2KV8NhB5JqC3ky0r9PMCAZKbUHSwtEo4CwCs0KXgruG43gX5PMqDEBbVU4OUzw2MuAWUfsuFmWvEKG5QRfSnJA==}

  cosmiconfig@7.1.0:
    resolution: {integrity: sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA==}
    engines: {node: '>=10'}

  cross-spawn@7.0.3:
    resolution: {integrity: sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==}
    engines: {node: '>= 8'}

  css-box-model@1.2.1:
    resolution: {integrity: sha512-a7Vr4Q/kd/aw96bnJG332W9V9LkJO69JRcaCYDUqjp6/z0w6VcZjgAcTbgFxEPfBgdnAwlh3iwu+hLopa+flJw==}

  css-color-keywords@1.0.0:
    resolution: {integrity: sha512-FyyrDHZKEjXDpNJYvVsV960FiqQyXc/LlYmsxl2BcdMb2WPx0OGRVgTg55rPSyLSNMqP52R9r8geSp7apN3Ofg==}
    engines: {node: '>=4'}

  css-to-react-native@3.2.0:
    resolution: {integrity: sha512-e8RKaLXMOFii+02mOlqwjbD00KSEKqblnpO9e++1aXS1fPQOpS1YoqdVHBqPjHNoxeF2mimzVqawm2KCbEdtHQ==}

  cssesc@3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==}
    engines: {node: '>=4'}
    hasBin: true

  csstype@3.1.1:
    resolution: {integrity: sha512-DJR/VvkAvSZW9bTouZue2sSxDwdTN92uHjqeKVm+0dAqdfNykRzQ95tay8aXMBAAPpUiq4Qcug2L7neoRh2Egw==}

  csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}

  d3-array@3.2.4:
    resolution: {integrity: sha512-tdQAmyA18i4J7wprpYq8ClcxZy3SC31QMeByyCFyRt7BVHdREQZ5lpzoe5mFEYZUWe+oq8HBvk9JjpibyEV4Jg==}
    engines: {node: '>=12'}

  d3-color@3.1.0:
    resolution: {integrity: sha512-zg/chbXyeBtMQ1LbD/WSoW2DpC3I0mpmPdW+ynRTj/x2DAWYrIY7qeZIHidozwV24m4iavr15lNwIwLxRmOxhA==}
    engines: {node: '>=12'}

  d3-dispatch@3.0.1:
    resolution: {integrity: sha512-rzUyPU/S7rwUflMyLc1ETDeBj0NRuHKKAcvukozwhshr6g6c5d8zh4c2gQjY2bZ0dXeGLWc1PF174P2tVvKhfg==}
    engines: {node: '>=12'}

  d3-drag@3.0.0:
    resolution: {integrity: sha512-pWbUJLdETVA8lQNJecMxoXfH6x+mO2UQo8rSmZ+QqxcbyA3hfeprFgIT//HW2nlHChWeIIMwS2Fq+gEARkhTkg==}
    engines: {node: '>=12'}

  d3-ease@3.0.1:
    resolution: {integrity: sha512-wR/XK3D3XcLIZwpbvQwQ5fK+8Ykds1ip7A2Txe0yxncXSdq1L9skcG7blcedkOX+ZcgxGAmLX1FrRGbADwzi0w==}
    engines: {node: '>=12'}

  d3-format@3.1.0:
    resolution: {integrity: sha512-YyUI6AEuY/Wpt8KWLgZHsIU86atmikuoOmCfommt0LYHiQSPjvX2AcFc38PX0CBpr2RCyZhjex+NS/LPOv6YqA==}
    engines: {node: '>=12'}

  d3-interpolate@3.0.1:
    resolution: {integrity: sha512-3bYs1rOD33uo8aqJfKP3JWPAibgw8Zm2+L9vBKEHJ2Rg+viTR7o5Mmv5mZcieN+FRYaAOWX5SJATX6k1PWz72g==}
    engines: {node: '>=12'}

  d3-path@3.1.0:
    resolution: {integrity: sha512-p3KP5HCf/bvjBSSKuXid6Zqijx7wIfNW+J/maPs+iwR35at5JCbLUT0LzF1cnjbCHWhqzQTIN2Jpe8pRebIEFQ==}
    engines: {node: '>=12'}

  d3-scale@4.0.2:
    resolution: {integrity: sha512-GZW464g1SH7ag3Y7hXjf8RoUuAFIqklOAq3MRl4OaWabTFJY9PN/E1YklhXLh+OQ3fM9yS2nOkCoS+WLZ6kvxQ==}
    engines: {node: '>=12'}

  d3-selection@3.0.0:
    resolution: {integrity: sha512-fmTRWbNMmsmWq6xJV8D19U/gw/bwrHfNXxrIN+HfZgnzqTHp9jOmKMhsTUjXOJnZOdZY9Q28y4yebKzqDKlxlQ==}
    engines: {node: '>=12'}

  d3-shape@3.2.0:
    resolution: {integrity: sha512-SaLBuwGm3MOViRq2ABk3eLoxwZELpH6zhl3FbAoJ7Vm1gofKx6El1Ib5z23NUEhF9AsGl7y+dzLe5Cw2AArGTA==}
    engines: {node: '>=12'}

  d3-time-format@4.1.0:
    resolution: {integrity: sha512-dJxPBlzC7NugB2PDLwo9Q8JiTR3M3e4/XANkreKSUxF8vvXKqm1Yfq4Q5dl8budlunRVlUUaDUgFt7eA8D6NLg==}
    engines: {node: '>=12'}

  d3-time@3.1.0:
    resolution: {integrity: sha512-VqKjzBLejbSMT4IgbmVgDjpkYrNWUYJnbCGo874u7MMKIWsILRX+OpX/gTk8MqjpT1A/c6HY2dCA77ZN0lkQ2Q==}
    engines: {node: '>=12'}

  d3-timer@3.0.1:
    resolution: {integrity: sha512-ndfJ/JxxMd3nw31uyKoY2naivF+r29V+Lc0svZxe1JvvIRmi8hUsrMvdOwgS1o6uBHmiz91geQ0ylPP0aj1VUA==}
    engines: {node: '>=12'}

  d3-transition@3.0.1:
    resolution: {integrity: sha512-ApKvfjsSR6tg06xrL434C0WydLr7JewBB3V+/39RMHsaXTOG0zmt/OAXeng5M5LBm0ojmxJrpomQVZ1aPvBL4w==}
    engines: {node: '>=12'}
    peerDependencies:
      d3-selection: 2 - 3

  d3-zoom@3.0.0:
    resolution: {integrity: sha512-b8AmV3kfQaqWAuacbPuNbL6vahnOJflOhexLzMMNLga62+/nh0JzvJ0aO/5a5MVgUFGS7Hu1P9P03o3fJkDCyw==}
    engines: {node: '>=12'}

  damerau-levenshtein@1.0.8:
    resolution: {integrity: sha512-sdQSFB7+llfUcQHUQO3+B8ERRj0Oa4w9POWMI/puGtuf7gFywGmkaLCElnudfTiKZV+NvHqL0ifzdrI8Ro7ESA==}

  data-view-buffer@1.0.1:
    resolution: {integrity: sha512-0lht7OugA5x3iJLOWFhWK/5ehONdprk0ISXqVFn/NFrDu+cuc8iADFrGQz5BnRK7LLU3JmkbXSxaqX+/mXYtUA==}
    engines: {node: '>= 0.4'}

  data-view-byte-length@1.0.1:
    resolution: {integrity: sha512-4J7wRJD3ABAzr8wP+OcIcqq2dlUKp4DVflx++hs5h5ZKydWMI6/D/fAot+yh6g2tHh8fLFTvNOaVN357NvSrOQ==}
    engines: {node: '>= 0.4'}

  data-view-byte-offset@1.0.0:
    resolution: {integrity: sha512-t/Ygsytq+R995EJ5PZlD4Cu56sWa8InXySaViRzw9apusqsOO2bQP+SbYzAhR0pFKoB+43lYy8rWban9JSuXnA==}
    engines: {node: '>= 0.4'}

  date-fns@3.6.0:
    resolution: {integrity: sha512-fRHTG8g/Gif+kSh50gaGEdToemgfj74aRX3swtiouboip5JDLAyDE9F11nHMIcvOaXeOC6D7SpNhi7uFyB7Uww==}

  dayjs@1.11.13:
    resolution: {integrity: sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==}

  debug@3.2.7:
    resolution: {integrity: sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.3.7:
    resolution: {integrity: sha512-Er2nc/H7RrMXZBFCEim6TCmMk02Z8vLC2Rbi1KEBggpo0fS6l0S1nnapwmIi3yW/+GOJap1Krg4w0Hg80oCqgQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decimal.js-light@2.5.1:
    resolution: {integrity: sha512-qIMFpTMZmny+MMIitAB6D7iVPEorVw6YQRWkvarTkT4tBeSLLiHzcwj6q0MmYSFCiVpiqPJTJEYIrpcPzVEIvg==}

  deep-equal@2.2.3:
    resolution: {integrity: sha512-ZIwpnevOurS8bpT4192sqAowWM76JDKSHYzMLty3BZGSswgq6pBaH3DhCSW5xVAZICZyKdOBPjwww5wfgT/6PA==}
    engines: {node: '>= 0.4'}

  deep-is@0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==}

  deepmerge@4.2.2:
    resolution: {integrity: sha512-FJ3UgI4gIl+PHZm53knsuSFpE+nESMr7M4v9QcgB7S63Kj/6WqMiFQJpBBYz1Pt+66bZpP3Q7Lye0Oo9MPKEdg==}
    engines: {node: '>=0.10.0'}

  define-data-property@1.1.4:
    resolution: {integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==}
    engines: {node: '>= 0.4'}

  define-properties@1.2.1:
    resolution: {integrity: sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==}
    engines: {node: '>= 0.4'}

  delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    engines: {node: '>=0.4.0'}

  detect-libc@2.0.4:
    resolution: {integrity: sha512-3UDv+G9CsCKO1WKMGw9fwq/SWJYbI0c5Y7LU1AXYoDdbhE2AHQ6N6Nb34sG8Fj7T5APy8qXDCKuuIHd1BR0tVA==}
    engines: {node: '>=8'}

  detect-node-es@1.1.0:
    resolution: {integrity: sha512-ypdmJU/TbBby2Dxibuv7ZLW3Bs1QEmM7nHjEANfohJLvE0XVujisn1qPJcZxg+qDucsr+bP6fLD1rPS3AhJ7EQ==}

  didyoumean@1.2.2:
    resolution: {integrity: sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==}

  dir-glob@3.0.1:
    resolution: {integrity: sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==}
    engines: {node: '>=8'}

  dlv@1.1.3:
    resolution: {integrity: sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==}

  doctrine@2.1.0:
    resolution: {integrity: sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==}
    engines: {node: '>=0.10.0'}

  doctrine@3.0.0:
    resolution: {integrity: sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==}
    engines: {node: '>=6.0.0'}

  dom-helpers@5.2.1:
    resolution: {integrity: sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==}

  dot-case@3.0.4:
    resolution: {integrity: sha512-Kv5nKlh6yRrdrGvxeJ2e5y2eRUpkUosIW4A2AS38zwSz27zu7ufDwQPi5Jhs3XAlGNetl3bmnGhQsMtkKJnj3w==}

  eastasianwidth@0.2.0:
    resolution: {integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==}

  emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}

  emoji-regex@9.2.2:
    resolution: {integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==}

  enhanced-resolve@5.17.1:
    resolution: {integrity: sha512-LMHl3dXhTcfv8gM4kEzIUeTQ+7fpdA0l2tUf34BddXPkz2A5xJ5L/Pchd5BL6rdccM9QGvu0sWZzK1Z1t4wwyg==}
    engines: {node: '>=10.13.0'}

  error-ex@1.3.2:
    resolution: {integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==}

  es-abstract@1.23.3:
    resolution: {integrity: sha512-e+HfNH61Bj1X9/jLc5v1owaLYuHdeHHSQlkhCBiTK8rBvKaULl/beGMxwrMXjpYrv4pz22BlY570vVePA2ho4A==}
    engines: {node: '>= 0.4'}

  es-define-property@1.0.0:
    resolution: {integrity: sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}

  es-get-iterator@1.1.3:
    resolution: {integrity: sha512-sPZmqHBe6JIiTfN5q2pEi//TwxmAFHwj/XEuYjTuse78i8KxaqMTTzxPoFKuzRpDpTJ+0NAbpfenkmH2rePtuw==}

  es-iterator-helpers@1.0.19:
    resolution: {integrity: sha512-zoMwbCcH5hwUkKJkT8kDIBZSz9I6mVG//+lDCinLCGov4+r7NIy0ld8o03M0cJxl2spVf6ESYVS6/gpIfq1FFw==}
    engines: {node: '>= 0.4'}

  es-object-atoms@1.0.0:
    resolution: {integrity: sha512-MZ4iQ6JwHOBQjahnjwaC1ZtIBH+2ohjamzAO3oaHcXYup7qxjF2fixyH+Q71voWHeOkI2q/TnJao/KfXYIZWbw==}
    engines: {node: '>= 0.4'}

  es-set-tostringtag@2.0.3:
    resolution: {integrity: sha512-3T8uNMC3OQTHkFUsFq8r/BwAXLHvU/9O9mE0fBc/MY5iq/8H7ncvO947LmYA6ldWw9Uh8Yhf25zu6n7nML5QWQ==}
    engines: {node: '>= 0.4'}

  es-shim-unscopables@1.0.2:
    resolution: {integrity: sha512-J3yBRXCzDu4ULnQwxyToo/OjdMx6akgVC7K6few0a7F/0wLtmKKN7I73AH5T2836UuXRqN7Qg+IIUw/+YJksRw==}

  es-to-primitive@1.2.1:
    resolution: {integrity: sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==}
    engines: {node: '>= 0.4'}

  escape-string-regexp@1.0.5:
    resolution: {integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==}
    engines: {node: '>=0.8.0'}

  escape-string-regexp@4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==}
    engines: {node: '>=10'}

  eslint-config-next@14.2.11:
    resolution: {integrity: sha512-gGIoBoHCJuLn6vaV1Ke8UurVvgb7JjQv6oRlWmI6RAAxz7KwJOYxxm2blctavA0a3eofbE9TdgKvvTb2G55OHQ==}
    peerDependencies:
      eslint: ^7.23.0 || ^8.0.0
      typescript: '>=3.3.1'
    peerDependenciesMeta:
      typescript:
        optional: true

  eslint-config-prettier@8.10.0:
    resolution: {integrity: sha512-SM8AMJdeQqRYT9O9zguiruQZaN7+z+E4eAP9oiLNGKMtomwaB1E9dcgUD6ZAn/eQAb52USbvezbiljfZUhbJcg==}
    hasBin: true
    peerDependencies:
      eslint: '>=7.0.0'

  eslint-import-resolver-node@0.3.9:
    resolution: {integrity: sha512-WFj2isz22JahUv+B788TlO3N6zL3nNJGU8CcZbPZvVEkBPaJdCV4vy5wyghty5ROFbCRnm132v8BScu5/1BQ8g==}

  eslint-import-resolver-typescript@3.6.3:
    resolution: {integrity: sha512-ud9aw4szY9cCT1EWWdGv1L1XR6hh2PaRWif0j2QjQ0pgTY/69iw+W0Z4qZv5wHahOl8isEr+k/JnyAqNQkLkIA==}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      eslint: '*'
      eslint-plugin-import: '*'
      eslint-plugin-import-x: '*'
    peerDependenciesMeta:
      eslint-plugin-import:
        optional: true
      eslint-plugin-import-x:
        optional: true

  eslint-module-utils@2.11.0:
    resolution: {integrity: sha512-gbBE5Hitek/oG6MUVj6sFuzEjA/ClzNflVrLovHi/JgLdC7fiN5gLAY1WIPW1a0V5I999MnsrvVrCOGmmVqDBQ==}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: '*'
      eslint-import-resolver-node: '*'
      eslint-import-resolver-typescript: '*'
      eslint-import-resolver-webpack: '*'
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true
      eslint:
        optional: true
      eslint-import-resolver-node:
        optional: true
      eslint-import-resolver-typescript:
        optional: true
      eslint-import-resolver-webpack:
        optional: true

  eslint-plugin-emotion@10.0.27:
    resolution: {integrity: sha512-0IG9KWmyQTAWZNM4WoGjFbdre1Xq6uMp2jYOSHvh3ZNcDfOjOLXeH3ky1MuWZlbWIHxz/Ed5DMGlJAeKnd26VA==}
    engines: {node: '>=6'}

  eslint-plugin-import@2.30.0:
    resolution: {integrity: sha512-/mHNE9jINJfiD2EKkg1BKyPyUk4zdnT54YgbOgfjSakWT5oyX/qQLVNTkehyfpcMxZXMy1zyonZ2v7hZTX43Yw==}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true

  eslint-plugin-jsx-a11y@6.10.0:
    resolution: {integrity: sha512-ySOHvXX8eSN6zz8Bywacm7CvGNhUtdjvqfQDVe6020TUK34Cywkw7m0KsCCk1Qtm9G1FayfTN1/7mMYnYO2Bhg==}
    engines: {node: '>=4.0'}
    peerDependencies:
      eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9

  eslint-plugin-react-hooks@4.6.2:
    resolution: {integrity: sha512-QzliNJq4GinDBcD8gPB5v0wh6g8q3SUi6EFF0x8N/BL9PoVs0atuGc47ozMRyOWAKdwaZ5OnbOEa3WR+dSGKuQ==}
    engines: {node: '>=10'}
    peerDependencies:
      eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0

  eslint-plugin-react@7.36.1:
    resolution: {integrity: sha512-/qwbqNXZoq+VP30s1d4Nc1C5GTxjJQjk4Jzs4Wq2qzxFM7dSmuG2UkIjg2USMLh3A/aVcUNrK7v0J5U1XEGGwA==}
    engines: {node: '>=4'}
    peerDependencies:
      eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7

  eslint-scope@5.1.1:
    resolution: {integrity: sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==}
    engines: {node: '>=8.0.0'}

  eslint-scope@7.2.2:
    resolution: {integrity: sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint-visitor-keys@3.4.3:
    resolution: {integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint@8.57.0:
    resolution: {integrity: sha512-dZ6+mexnaTIbSBZWgou51U6OmzIhYM2VcNdtiTtI7qPNZm35Akpr0f6vtw3w1Kmn5PYo+tZVfh13WrhpS6oLqQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    hasBin: true

  espree@9.6.1:
    resolution: {integrity: sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  esquery@1.6.0:
    resolution: {integrity: sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==}
    engines: {node: '>=0.10'}

  esrecurse@4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    engines: {node: '>=4.0'}

  estraverse@4.3.0:
    resolution: {integrity: sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==}
    engines: {node: '>=4.0'}

  estraverse@5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    engines: {node: '>=4.0'}

  esutils@2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}

  event-target-shim@5.0.1:
    resolution: {integrity: sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==}
    engines: {node: '>=6'}

  eventemitter3@4.0.7:
    resolution: {integrity: sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}

  fast-equals@5.0.1:
    resolution: {integrity: sha512-WF1Wi8PwwSY7/6Kx0vKXtw8RwuSGoM1bvDaJbu7MxDlR1vovZjIAKrnzyrThgAjm6JDTu0fVgWXDlMGspodfoQ==}
    engines: {node: '>=6.0.0'}

  fast-glob@3.3.2:
    resolution: {integrity: sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==}
    engines: {node: '>=8.6.0'}

  fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}

  fast-levenshtein@2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==}

  fast-xml-parser@4.4.1:
    resolution: {integrity: sha512-xkjOecfnKGkSsOwtZ5Pz7Us/T6mrbPQrq0nh+aCO5V9nk5NLWmasAHumTKjiPJPWANe+kAZ84Jc8ooJkzZ88Sw==}
    hasBin: true

  fastq@1.17.1:
    resolution: {integrity: sha512-sRVD3lWVIXWg6By68ZN7vho9a1pQcN/WBFaAAsDDFzlJjvoGx0P8z7V1t72grFJfJhu3YPZBuu25f7Kaw2jN1w==}

  fault@1.0.4:
    resolution: {integrity: sha512-CJ0HCB5tL5fYTEA7ToAq5+kTwd++Borf1/bifxd9iT70QcXr4MRrO3Llf8Ifs70q+SJcGHFtnIE/Nw6giCtECA==}

  file-entry-cache@6.0.1:
    resolution: {integrity: sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==}
    engines: {node: ^10.12.0 || >=12.0.0}

  fill-range@7.1.1:
    resolution: {integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==}
    engines: {node: '>=8'}

  find-root@1.1.0:
    resolution: {integrity: sha512-NKfW6bec6GfKc0SGx1e07QZY9PE99u0Bft/0rzSD5k3sO/vwkVUpDUKVm5Gpp5Ue3YfShPFTX2070tDs5kB9Ng==}

  find-up@5.0.0:
    resolution: {integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==}
    engines: {node: '>=10'}

  flat-cache@3.2.0:
    resolution: {integrity: sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw==}
    engines: {node: ^10.12.0 || >=12.0.0}

  flatted@3.3.1:
    resolution: {integrity: sha512-X8cqMLLie7KsNUDSdzeN8FYK9rEt4Dt67OsG/DNGnYTSDBG4uFAJFBnUeiV+zCVAvwFy56IjM9sH51jVaEhNxw==}

  focus-lock@1.3.5:
    resolution: {integrity: sha512-QFaHbhv9WPUeLYBDe/PAuLKJ4Dd9OPvKs9xZBr3yLXnUrDNaVXKu2baDBXe3naPY30hgHYSsf2JW4jzas2mDEQ==}
    engines: {node: '>=10'}

  follow-redirects@1.15.9:
    resolution: {integrity: sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  for-each@0.3.3:
    resolution: {integrity: sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==}

  foreground-child@3.3.0:
    resolution: {integrity: sha512-Ld2g8rrAyMYFXBhEqMz8ZAHBi4J4uS1i/CxGMDnjyFWddMXLVcDp051DZfu+t7+ab7Wv6SMqpWmyFIj5UbfFvg==}
    engines: {node: '>=14'}

  form-data-encoder@1.7.2:
    resolution: {integrity: sha512-qfqtYan3rxrnCk1VYaA4H+Ms9xdpPqvLZa6xmMgFvhO32x7/3J/ExcTd6qpxM0vH2GdMI+poehyBZvqfMTto8A==}

  form-data@3.0.1:
    resolution: {integrity: sha512-RHkBKtLWUVwd7SqRIvCZMEvAMoGUp0XU+seQiZejj0COz3RI3hWP4sCv3gZWWLjJTd7rGwcsF5eKZGii0r/hbg==}
    engines: {node: '>= 6'}

  form-data@4.0.0:
    resolution: {integrity: sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==}
    engines: {node: '>= 6'}

  format@0.2.2:
    resolution: {integrity: sha512-wzsgA6WOq+09wrU1tsJ09udeR/YZRaeArL9e1wPbFg3GG2yDnC2ldKpxs4xunpFF9DgqCqOIra3bc1HWrJ37Ww==}
    engines: {node: '>=0.4.x'}

  formdata-node@4.4.1:
    resolution: {integrity: sha512-0iirZp3uVDjVGt9p49aTaqjk84TrglENEDuqfdlZQ1roC9CWlPk6Avf8EEnZNcAqPonwkG35x4n3ww/1THYAeQ==}
    engines: {node: '>= 12.20'}

  framer-motion@10.18.0:
    resolution: {integrity: sha512-oGlDh1Q1XqYPksuTD/usb0I70hq95OUzmL9+6Zd+Hs4XV0oaISBa/UUMSjYiq6m8EUF32132mOJ8xVZS+I0S6w==}
    peerDependencies:
      react: ^18.0.0
      react-dom: ^18.0.0
    peerDependenciesMeta:
      react:
        optional: true
      react-dom:
        optional: true

  framesync@6.1.2:
    resolution: {integrity: sha512-jBTqhX6KaQVDyus8muwZbBeGGP0XgujBRbQ7gM7BRdS3CadCZIHiawyzYLnafYcvZIh5j8WE7cxZKFn7dXhu9g==}

  fs.realpath@1.0.0:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==}

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  function.prototype.name@1.1.6:
    resolution: {integrity: sha512-Z5kx79swU5P27WEayXM1tBi5Ze/lbIyiNgU3qyXUOf9b2rgXYyF9Dy9Cx+IQv/Lc8WCG6L82zwUPpSS9hGehIg==}
    engines: {node: '>= 0.4'}

  functions-have-names@1.2.3:
    resolution: {integrity: sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==}

  get-intrinsic@1.2.4:
    resolution: {integrity: sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==}
    engines: {node: '>= 0.4'}

  get-nonce@1.0.1:
    resolution: {integrity: sha512-FJhYRoDaiatfEkUK8HKlicmu/3SGFD51q3itKDGoSTysQJBnfOcxU5GxnhE1E6soB76MbT0MBtnKJuXyAx+96Q==}
    engines: {node: '>=6'}

  get-symbol-description@1.0.2:
    resolution: {integrity: sha512-g0QYk1dZBxGwk+Ngc+ltRH2IBp2f7zBkBMBJZCDerh6EhlhSR6+9irMCuT/09zD6qkarHUSn529sK/yL4S27mg==}
    engines: {node: '>= 0.4'}

  get-tsconfig@4.8.1:
    resolution: {integrity: sha512-k9PN+cFBmaLWtVz29SkUoqU5O0slLuHJXt/2P+tMVFT+phsSGXGkp9t3rQIqdz0e+06EHNGs3oM6ZX1s2zHxRg==}

  glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}

  glob-parent@6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==}
    engines: {node: '>=10.13.0'}

  glob-to-regexp@0.4.1:
    resolution: {integrity: sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==}

  glob@10.3.10:
    resolution: {integrity: sha512-fa46+tv1Ak0UPK1TOy/pZrIybNNt4HCv7SDzwyfiOZkvZLEbjsZkJBPtDHVshZjbecAoAGSC20MjLDG/qr679g==}
    engines: {node: '>=16 || 14 >=14.17'}
    hasBin: true

  glob@10.4.5:
    resolution: {integrity: sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==}
    hasBin: true

  glob@7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==}
    deprecated: Glob versions prior to v9 are no longer supported

  globals@11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==}
    engines: {node: '>=4'}

  globals@13.24.0:
    resolution: {integrity: sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==}
    engines: {node: '>=8'}

  globalthis@1.0.4:
    resolution: {integrity: sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==}
    engines: {node: '>= 0.4'}

  globby@11.1.0:
    resolution: {integrity: sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==}
    engines: {node: '>=10'}

  gopd@1.0.1:
    resolution: {integrity: sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==}

  graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}

  graphemer@1.4.0:
    resolution: {integrity: sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==}

  hammerjs@2.0.8:
    resolution: {integrity: sha512-tSQXBXS/MWQOn/RKckawJ61vvsDpCom87JgxiYdGwHdOa0ht0vzUWDlfioofFCRU0L+6NGDt6XzbgoJvZkMeRQ==}
    engines: {node: '>=0.8.0'}

  has-bigints@1.0.2:
    resolution: {integrity: sha512-tSvCKtBr9lkF0Ex0aQiP9N+OpV4zi2r/Nee5VkRDbaqv35RLYMzbwQfFSZZH0kR+Rd6302UJZ2p/bJCEoR3VoQ==}

  has-flag@3.0.0:
    resolution: {integrity: sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==}
    engines: {node: '>=4'}

  has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}

  has-property-descriptors@1.0.2:
    resolution: {integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==}

  has-proto@1.0.3:
    resolution: {integrity: sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q==}
    engines: {node: '>= 0.4'}

  has-symbols@1.0.3:
    resolution: {integrity: sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==}
    engines: {node: '>= 0.4'}

  has-tostringtag@1.0.2:
    resolution: {integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==}
    engines: {node: '>= 0.4'}

  hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}

  hast-util-parse-selector@2.2.5:
    resolution: {integrity: sha512-7j6mrk/qqkSehsM92wQjdIgWM2/BW61u/53G6xmC8i1OmEdKLHbk419QKQUjz6LglWsfqoiHmyMRkP1BGjecNQ==}

  hastscript@6.0.0:
    resolution: {integrity: sha512-nDM6bvd7lIqDUiYEiu5Sl/+6ReP0BMk/2f4U/Rooccxkj0P5nm+acM5PrGJ/t5I8qPGiqZSE6hVAwZEdZIvP4w==}

  highlight.js@10.7.3:
    resolution: {integrity: sha512-tzcUFauisWKNHaRkN4Wjl/ZA07gENAjFl3J/c480dprkGTg5EQstgaNFqBfUqCq54kZRIEcreTsAgF/m2quD7A==}

  hoist-non-react-statics@3.3.2:
    resolution: {integrity: sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==}

  humanize-ms@1.2.1:
    resolution: {integrity: sha512-Fl70vYtsAFb/C06PTS9dZBo7ihau+Tu/DNCk/OyHhea07S+aeMWpFFkUaXRa8fI+ScZbEI8dfSxwY7gxZ9SAVQ==}

  ignore@5.3.2:
    resolution: {integrity: sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==}
    engines: {node: '>= 4'}

  immutable@4.3.7:
    resolution: {integrity: sha512-1hqclzwYwjRDFLjcFxOM5AYkkG0rpFPpr1RLPMEuGczoS7YA8gLhy8SWXYRAA/XwfEHpfo3cw5JGioS32fnMRw==}

  import-fresh@3.3.0:
    resolution: {integrity: sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==}
    engines: {node: '>=6'}

  imurmurhash@0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==}
    engines: {node: '>=0.8.19'}

  inflight@1.0.6:
    resolution: {integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==}
    deprecated: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.

  inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  internal-slot@1.0.7:
    resolution: {integrity: sha512-NGnrKwXzSms2qUUih/ILZ5JBqNTSa1+ZmP6flaIp6KmSElgE9qdndzS3cqjrDovwFdmwsGsLdeFgB6suw+1e9g==}
    engines: {node: '>= 0.4'}

  internmap@2.0.3:
    resolution: {integrity: sha512-5Hh7Y1wQbvY5ooGgPbDaL5iYLAPzMTUrjMulskHLH6wnv/A+1q5rgEaiuqEjB+oxGXIVZs1FF+R/KPN3ZSQYYg==}
    engines: {node: '>=12'}

  invariant@2.2.4:
    resolution: {integrity: sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==}

  is-alphabetical@1.0.4:
    resolution: {integrity: sha512-DwzsA04LQ10FHTZuL0/grVDk4rFoVH1pjAToYwBrHSxcrBIGQuXrQMtD5U1b0U2XVgKZCTLLP8u2Qxqhy3l2Vg==}

  is-alphanumerical@1.0.4:
    resolution: {integrity: sha512-UzoZUr+XfVz3t3v4KyGEniVL9BDRoQtY7tOyrRybkVNjDFWyo1yhXNGrrBTQxp3ib9BLAWs7k2YKBQsFRkZG9A==}

  is-arguments@1.1.1:
    resolution: {integrity: sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA==}
    engines: {node: '>= 0.4'}

  is-array-buffer@3.0.4:
    resolution: {integrity: sha512-wcjaerHw0ydZwfhiKbXJWLDY8A7yV7KhjQOpb83hGgGfId/aQa4TOvwyzn2PuswW2gPCYEL/nEAiSVpdOj1lXw==}
    engines: {node: '>= 0.4'}

  is-arrayish@0.2.1:
    resolution: {integrity: sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==}

  is-arrayish@0.3.2:
    resolution: {integrity: sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==}

  is-async-function@2.0.0:
    resolution: {integrity: sha512-Y1JXKrfykRJGdlDwdKlLpLyMIiWqWvuSd17TvZk68PLAOGOoF4Xyav1z0Xhoi+gCYjZVeC5SI+hYFOfvXmGRCA==}
    engines: {node: '>= 0.4'}

  is-bigint@1.0.4:
    resolution: {integrity: sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg==}

  is-binary-path@2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}

  is-boolean-object@1.1.2:
    resolution: {integrity: sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA==}
    engines: {node: '>= 0.4'}

  is-bun-module@1.2.1:
    resolution: {integrity: sha512-AmidtEM6D6NmUiLOvvU7+IePxjEjOzra2h0pSrsfSAcXwl/83zLLXDByafUJy9k/rKK0pvXMLdwKwGHlX2Ke6Q==}

  is-callable@1.2.7:
    resolution: {integrity: sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==}
    engines: {node: '>= 0.4'}

  is-core-module@2.15.1:
    resolution: {integrity: sha512-z0vtXSwucUJtANQWldhbtbt7BnL0vxiFjIdDLAatwhDYty2bad6s+rijD6Ri4YuYJubLzIJLUidCh09e1djEVQ==}
    engines: {node: '>= 0.4'}

  is-data-view@1.0.1:
    resolution: {integrity: sha512-AHkaJrsUVW6wq6JS8y3JnM/GJF/9cf+k20+iDzlSaJrinEo5+7vRiteOSwBhHRiAyQATN1AmY4hwzxJKPmYf+w==}
    engines: {node: '>= 0.4'}

  is-date-object@1.0.5:
    resolution: {integrity: sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ==}
    engines: {node: '>= 0.4'}

  is-decimal@1.0.4:
    resolution: {integrity: sha512-RGdriMmQQvZ2aqaQq3awNA6dCGtKpiDFcOzrTWrDAT2MiWrKQVPmxLGHl7Y2nNu6led0kEyoX0enY0qXYsv9zw==}

  is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}

  is-finalizationregistry@1.0.2:
    resolution: {integrity: sha512-0by5vtUJs8iFQb5TYUHHPudOR+qXYIMKtiUzvLIZITZUjknFmziyBJuLhVRc+Ds0dREFlskDNJKYIdIzu/9pfw==}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}

  is-generator-function@1.0.10:
    resolution: {integrity: sha512-jsEjy9l3yiXEQ+PsXdmBwEPcOxaXWLspKdplFUVI9vq1iZgIekeC0L167qeu86czQaxed3q/Uzuw0swL0irL8A==}
    engines: {node: '>= 0.4'}

  is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}

  is-hexadecimal@1.0.4:
    resolution: {integrity: sha512-gyPJuv83bHMpocVYoqof5VDiZveEoGoFL8m3BXNb2VW8Xs+rz9kqO8LOQ5DH6EsuvilT1ApazU0pyl+ytbPtlw==}

  is-map@2.0.3:
    resolution: {integrity: sha512-1Qed0/Hr2m+YqxnM09CjA2d/i6YZNfF6R2oRAOj36eUdS6qIV/huPJNSEpKbupewFs+ZsJlxsjjPbc0/afW6Lw==}
    engines: {node: '>= 0.4'}

  is-negative-zero@2.0.3:
    resolution: {integrity: sha512-5KoIu2Ngpyek75jXodFvnafB6DJgr3u8uuK0LEZJjrU19DrMD3EVERaR8sjz8CCGgpZvxPl9SuE1GMVPFHx1mw==}
    engines: {node: '>= 0.4'}

  is-number-object@1.0.7:
    resolution: {integrity: sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ==}
    engines: {node: '>= 0.4'}

  is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  is-path-inside@3.0.3:
    resolution: {integrity: sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==}
    engines: {node: '>=8'}

  is-regex@1.1.4:
    resolution: {integrity: sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==}
    engines: {node: '>= 0.4'}

  is-set@2.0.3:
    resolution: {integrity: sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg==}
    engines: {node: '>= 0.4'}

  is-shared-array-buffer@1.0.3:
    resolution: {integrity: sha512-nA2hv5XIhLR3uVzDDfCIknerhx8XUKnstuOERPNNIinXG7v9u+ohXF67vxm4TPTEPU6lm61ZkwP3c9PCB97rhg==}
    engines: {node: '>= 0.4'}

  is-string@1.0.7:
    resolution: {integrity: sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg==}
    engines: {node: '>= 0.4'}

  is-symbol@1.0.4:
    resolution: {integrity: sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg==}
    engines: {node: '>= 0.4'}

  is-typed-array@1.1.13:
    resolution: {integrity: sha512-uZ25/bUAlUY5fR4OKT4rZQEBrzQWYV9ZJYGGsUmEJ6thodVJ1HX64ePQ6Z0qPWP+m+Uq6e9UugrE38jeYsDSMw==}
    engines: {node: '>= 0.4'}

  is-weakmap@2.0.2:
    resolution: {integrity: sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w==}
    engines: {node: '>= 0.4'}

  is-weakref@1.0.2:
    resolution: {integrity: sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ==}

  is-weakset@2.0.3:
    resolution: {integrity: sha512-LvIm3/KWzS9oRFHugab7d+M/GcBXuXX5xZkzPmN+NxihdQlZUQ4dWuSV1xR/sq6upL1TJEDrfBgRepHFdBtSNQ==}
    engines: {node: '>= 0.4'}

  isarray@2.0.5:
    resolution: {integrity: sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==}

  isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}

  iterator.prototype@1.1.2:
    resolution: {integrity: sha512-DR33HMMr8EzwuRL8Y9D3u2BMj8+RqSE850jfGu59kS7tbmPLzGkZmVSfyCFSDxuZiEY6Rzt3T2NA/qU+NwVj1w==}

  its-fine@1.2.5:
    resolution: {integrity: sha512-fXtDA0X0t0eBYAGLVM5YsgJGsJ5jEmqZEPrGbzdf5awjv0xE7nqv3TVnvtUF060Tkes15DbDAKW/I48vsb6SyA==}
    peerDependencies:
      react: '>=18.0'

  jackspeak@2.3.6:
    resolution: {integrity: sha512-N3yCS/NegsOBokc8GAdM8UcmfsKiSS8cipheD/nivzr700H+nsMOxJjQnvwOcRYVuFkdH0wGUvW2WbXGmrZGbQ==}
    engines: {node: '>=14'}

  jackspeak@3.4.3:
    resolution: {integrity: sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==}

  jiti@1.21.6:
    resolution: {integrity: sha512-2yTgeWTWzMWkHu6Jp9NKgePDaYHbntiwvYuuJLbbN9vl7DC9DvXKOB2BC3ZZ92D3cvV/aflH0osDfwpHepQ53w==}
    hasBin: true

  js-cookie@3.0.1:
    resolution: {integrity: sha512-+0rgsUXZu4ncpPxRL+lNEptWMOWl9etvPHc/koSRp6MPwpRYAhmk0dUG00J4bxVV3r9uUzfo24wW0knS07SKSw==}
    engines: {node: '>=12'}

  js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  js-yaml@4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==}
    hasBin: true

  jsesc@2.5.2:
    resolution: {integrity: sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==}
    engines: {node: '>=4'}
    hasBin: true

  json-buffer@3.0.1:
    resolution: {integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==}

  json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==}

  json-schema-traverse@0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}

  json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==}

  json5@1.0.2:
    resolution: {integrity: sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==}
    hasBin: true

  jsx-ast-utils@3.3.5:
    resolution: {integrity: sha512-ZZow9HBI5O6EPgSJLUb8n2NKgmVWTwCvHGwFuJlMjvLFqlGG6pjirPhtdsseaLZjSibD8eegzmYpUZwoIlj2cQ==}
    engines: {node: '>=4.0'}

  keyv@4.5.4:
    resolution: {integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==}

  konva@8.4.3:
    resolution: {integrity: sha512-ARqdgAbdNIougRlOKvkQwHlGhXPRBV4KvhCP+qoPpGoVQwwiJe4Hkdu4HHdRPb9rGUp04jDTAxBzEwBsE272pg==}

  language-subtag-registry@0.3.23:
    resolution: {integrity: sha512-0K65Lea881pHotoGEa5gDlMxt3pctLi2RplBb7Ezh4rRdLEOtgi7n4EwK9lamnUCkKBqaeKRVebTq6BAxSkpXQ==}

  language-tags@1.0.9:
    resolution: {integrity: sha512-MbjN408fEndfiQXbFQ1vnd+1NoLDsnQW41410oQBXiyXDMYH5z505juWa4KUE1LqxRC7DgOgZDbKLxHIwm27hA==}
    engines: {node: '>=0.10'}

  levn@0.4.1:
    resolution: {integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==}
    engines: {node: '>= 0.8.0'}

  lilconfig@2.1.0:
    resolution: {integrity: sha512-utWOt/GHzuUxnLKxB6dk81RoOeoNeHgbrXiuGk4yyF5qlRz+iIVWu56E2fqGHFrXz0QNUhLB/8nKqvRH66JKGQ==}
    engines: {node: '>=10'}

  lilconfig@3.1.2:
    resolution: {integrity: sha512-eop+wDAvpItUys0FWkHIKeC9ybYrTGbU41U5K7+bttZZeohvnY7M9dZ5kB21GNWiFT2q1OoPTvncPCgSOVO5ow==}
    engines: {node: '>=14'}

  lines-and-columns@1.2.4:
    resolution: {integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==}

  locate-path@6.0.0:
    resolution: {integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==}
    engines: {node: '>=10'}

  lodash.merge@4.6.2:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==}

  lodash.mergewith@4.6.2:
    resolution: {integrity: sha512-GK3g5RPZWTRSeLSpgP8Xhra+pnjBC56q9FZYe1d5RN3TJ35dbkGy3YqBSMbyCrlbi+CM9Z3Jk5yTL7RCsqboyQ==}

  lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}

  logrocket@8.1.3:
    resolution: {integrity: sha512-lWuFhkWYMtARJ2BZUrxP54Dtn3TskpLZ0kDhp14BkrapFQihcN0GB8pvy2vP1OdRFFVh3pAG7AWo/BUbQ9SIbg==}

  loose-envify@1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==}
    hasBin: true

  lower-case@2.0.2:
    resolution: {integrity: sha512-7fm3l3NAF9WfN6W3JOmf5drwpVqX78JtoGJ3A6W0a6ZnldM41w2fV5D490psKFTpMds8TJse/eHLFFsNHHjHgg==}

  lowlight@1.20.0:
    resolution: {integrity: sha512-8Ktj+prEb1RoCPkEOrPMYUN/nCggB7qAWe3a7OpMjWQkh3l2RD5wKRQ+o8Q8YuI9RG/xs95waaI/E6ym/7NsTw==}

  lru-cache@10.4.3:
    resolution: {integrity: sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==}

  lucide-react@0.358.0:
    resolution: {integrity: sha512-rBSptRjZTMBm24zsFhR6pK/NgbT18JegZGKcH4+1H3+UigMSRpeoWLtR/fAwMYwYnlJOZB+y8WpeHne9D6X6Kg==}
    peerDependencies:
      react: ^16.5.1 || ^17.0.0 || ^18.0.0

  luxon@3.5.0:
    resolution: {integrity: sha512-rh+Zjr6DNfUYR3bPwJEnuwDdqMbxZW7LOQfUN4B54+Cl+0o5zaU9RJ6bcidfDtC1cWCZXQ+nvX8bf6bAji37QQ==}
    engines: {node: '>=12'}

  mailgun.js@10.2.3:
    resolution: {integrity: sha512-7Mcw5IFtzN21i+qFQoWI+aQFDpLYSMUIWvDUXKLlpGFVVGfYVL8GIiveS+LIXpEJTQcF1hoNhOhDwenFqNSKmw==}
    engines: {node: '>=18.0.0'}

  map-obj@4.3.0:
    resolution: {integrity: sha512-hdN1wVrZbb29eBGiGjJbeP8JbKjq1urkHJ/LIP/NY48MZ1QVXUsQBV1G1zvYFHn1XE06cwjBsOI2K3Ulnj1YXQ==}
    engines: {node: '>=8'}

  merge2@1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}

  micromatch@4.0.8:
    resolution: {integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==}
    engines: {node: '>=8.6'}

  mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}

  mini-svg-data-uri@1.4.4:
    resolution: {integrity: sha512-r9deDe9p5FJUPZAk3A59wGH7Ii9YrjjWw0jmw/liSbHl2CHiyXj6FcDXDu2K3TjVAXqiJdaw3xxwlZZr9E6nHg==}
    hasBin: true

  minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}

  minimatch@9.0.5:
    resolution: {integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==}
    engines: {node: '>=16 || 14 >=14.17'}

  minimist@1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==}

  minipass@7.1.2:
    resolution: {integrity: sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==}
    engines: {node: '>=16 || 14 >=14.17'}

  moment@2.30.1:
    resolution: {integrity: sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==}

  ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}

  mz@2.7.0:
    resolution: {integrity: sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==}

  nanoid@3.3.7:
    resolution: {integrity: sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  nanoid@5.0.7:
    resolution: {integrity: sha512-oLxFY2gd2IqnjcYyOXD8XGCftpGtZP2AbHbOkthDkvRywH5ayNtPVy9YlOPcHckXzbLTCHpkb7FB+yuxKV13pQ==}
    engines: {node: ^18 || >=20}
    hasBin: true

  natural-compare-lite@1.4.0:
    resolution: {integrity: sha512-Tj+HTDSJJKaZnfiuw+iaF9skdPpTo2GtEly5JHnWV/hfv2Qj/9RKsGISQtLh2ox3l5EAGw487hnBee0sIJ6v2g==}

  natural-compare@1.4.0:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==}

  next@15.4.0-canary.61:
    resolution: {integrity: sha512-EMSeQxbnfy7iR1lFK9CQNMdff9FYlFhWwiY1WfhXk7nYBSwF2n7+VM5q1drxVMZrrJpFmXvlWQ/A7imGVgVS9g==}
    engines: {node: ^18.18.0 || ^19.8.0 || >= 20.0.0}
    hasBin: true
    peerDependencies:
      '@opentelemetry/api': ^1.1.0
      '@playwright/test': ^1.41.2
      babel-plugin-react-compiler: '*'
      react: ^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0
      react-dom: ^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0
      sass: ^1.3.0
    peerDependenciesMeta:
      '@opentelemetry/api':
        optional: true
      '@playwright/test':
        optional: true
      babel-plugin-react-compiler:
        optional: true
      sass:
        optional: true

  no-case@3.0.4:
    resolution: {integrity: sha512-fgAN3jGAh+RoxUGZHTSOLJIqUc2wmoBwGR4tbpNAKmmovFoWq0OdRkb0VkldReO2a2iBT/OEulG9XSUc10r3zg==}

  node-domexception@1.0.0:
    resolution: {integrity: sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==}
    engines: {node: '>=10.5.0'}

  node-fetch-native@1.0.1:
    resolution: {integrity: sha512-VzW+TAk2wE4X9maiKMlT+GsPU4OMmR1U9CrHSmd3DFLn2IcZ9VJ6M6BBugGfYUnPCLSYxXdZy17M0BEJyhUTwg==}

  node-fetch@2.7.0:
    resolution: {integrity: sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==}
    engines: {node: 4.x || >=6.0.0}
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true

  normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}

  object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}

  object-hash@3.0.0:
    resolution: {integrity: sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==}
    engines: {node: '>= 6'}

  object-inspect@1.13.2:
    resolution: {integrity: sha512-IRZSRuzJiynemAXPYtPe5BoI/RESNYR7TYm50MC5Mqbd3Jmw5y790sErYw3V6SryFJD64b74qQQs9wn5Bg/k3g==}
    engines: {node: '>= 0.4'}

  object-is@1.1.6:
    resolution: {integrity: sha512-F8cZ+KfGlSGi09lJT7/Nd6KJZ9ygtvYC0/UYYLI9nmQKLMnydpB9yvbv9K1uSkEu7FU9vYPmVwLg328tX+ot3Q==}
    engines: {node: '>= 0.4'}

  object-keys@1.1.1:
    resolution: {integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==}
    engines: {node: '>= 0.4'}

  object.assign@4.1.5:
    resolution: {integrity: sha512-byy+U7gp+FVwmyzKPYhW2h5l3crpmGsxl7X2s8y43IgxvG4g3QZ6CffDtsNQy1WsmZpQbO+ybo0AlW7TY6DcBQ==}
    engines: {node: '>= 0.4'}

  object.entries@1.1.8:
    resolution: {integrity: sha512-cmopxi8VwRIAw/fkijJohSfpef5PdN0pMQJN6VC/ZKvn0LIknWD8KtgY6KlQdEc4tIjcQ3HxSMmnvtzIscdaYQ==}
    engines: {node: '>= 0.4'}

  object.fromentries@2.0.8:
    resolution: {integrity: sha512-k6E21FzySsSK5a21KRADBd/NGneRegFO5pLHfdQLpRDETUNJueLXs3WCzyQ3tFRDYgbq3KHGXfTbi2bs8WQ6rQ==}
    engines: {node: '>= 0.4'}

  object.groupby@1.0.3:
    resolution: {integrity: sha512-+Lhy3TQTuzXI5hevh8sBGqbmurHbbIjAi0Z4S63nthVLmLxfbj4T54a4CfZrXIrt9iP4mVAPYMo/v99taj3wjQ==}
    engines: {node: '>= 0.4'}

  object.values@1.2.0:
    resolution: {integrity: sha512-yBYjY9QX2hnRmZHAjG/f13MzmBzxzYgQhFrke06TTyKY5zSTEqkOeukBzIdVA3j3ulu8Qa3MbVFShV7T2RmGtQ==}
    engines: {node: '>= 0.4'}

  obuf@1.1.2:
    resolution: {integrity: sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg==}

  once@1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==}

  openai@4.61.0:
    resolution: {integrity: sha512-xkygRBRLIUumxzKGb1ug05pWmJROQsHkGuj/N6Jiw2dj0dI19JvbFpErSZKmJ/DA+0IvpcugZqCAyk8iLpyM6Q==}
    hasBin: true
    peerDependencies:
      zod: ^3.23.8
    peerDependenciesMeta:
      zod:
        optional: true

  optionator@0.9.4:
    resolution: {integrity: sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==}
    engines: {node: '>= 0.8.0'}

  p-limit@3.1.0:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==}
    engines: {node: '>=10'}

  p-locate@5.0.0:
    resolution: {integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==}
    engines: {node: '>=10'}

  package-json-from-dist@1.0.0:
    resolution: {integrity: sha512-dATvCeZN/8wQsGywez1mzHtTlP22H8OEfPrVMLNr4/eGa+ijtLn/6M5f0dY8UKNrC2O9UCU6SSoG3qRKnt7STw==}

  parent-module@1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}

  parse-entities@2.0.0:
    resolution: {integrity: sha512-kkywGpCcRYhqQIchaWqZ875wzpS/bMKhz5HnN3p7wveJTkTtyAB/AlnS0f8DFSqYW1T82t6yEAkEcB+A1I3MbQ==}

  parse-json@5.2.0:
    resolution: {integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==}
    engines: {node: '>=8'}

  path-exists@4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}

  path-is-absolute@1.0.1:
    resolution: {integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==}
    engines: {node: '>=0.10.0'}

  path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}

  path-parse@1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}

  path-scurry@1.11.1:
    resolution: {integrity: sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==}
    engines: {node: '>=16 || 14 >=14.18'}

  path-type@4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==}
    engines: {node: '>=8'}

  pg-cloudflare@1.2.5:
    resolution: {integrity: sha512-OOX22Vt0vOSRrdoUPKJ8Wi2OpE/o/h9T8X1s4qSkCedbNah9ei2W2765be8iMVxQUsvgT7zIAT2eIa9fs5+vtg==}

  pg-connection-string@2.8.5:
    resolution: {integrity: sha512-Ni8FuZ8yAF+sWZzojvtLE2b03cqjO5jNULcHFfM9ZZ0/JXrgom5pBREbtnAw7oxsxJqHw9Nz/XWORUEL3/IFow==}

  pg-int8@1.0.1:
    resolution: {integrity: sha512-WCtabS6t3c8SkpDBUlb1kjOs7l66xsGdKpIPZsg4wR+B3+u9UAum2odSsF9tnvxg80h4ZxLWMy4pRjOsFIqQpw==}
    engines: {node: '>=4.0.0'}

  pg-numeric@1.0.2:
    resolution: {integrity: sha512-BM/Thnrw5jm2kKLE5uJkXqqExRUY/toLHda65XgFTBTFYZyopbKjBe29Ii3RbkvlsMoFwD+tHeGaCjjv0gHlyw==}
    engines: {node: '>=4'}

  pg-pool@3.9.6:
    resolution: {integrity: sha512-rFen0G7adh1YmgvrmE5IPIqbb+IgEzENUm+tzm6MLLDSlPRoZVhzU1WdML9PV2W5GOdRA9qBKURlbt1OsXOsPw==}
    peerDependencies:
      pg: '>=8.0'

  pg-protocol@1.9.5:
    resolution: {integrity: sha512-DYTWtWpfd5FOro3UnAfwvhD8jh59r2ig8bPtc9H8Ds7MscE/9NYruUQWFAOuraRl29jwcT2kyMFQ3MxeaVjUhg==}

  pg-types@2.2.0:
    resolution: {integrity: sha512-qTAAlrEsl8s4OiEQY69wDvcMIdQN6wdz5ojQiOy6YRMuynxenON0O5oCpJI6lshc6scgAY8qvJ2On/p+CXY0GA==}
    engines: {node: '>=4'}

  pg-types@4.0.2:
    resolution: {integrity: sha512-cRL3JpS3lKMGsKaWndugWQoLOCoP+Cic8oseVcbr0qhPzYD5DWXK+RZ9LY9wxRf7RQia4SCwQlXk0q6FCPrVng==}
    engines: {node: '>=10'}

  pg@8.15.6:
    resolution: {integrity: sha512-yvao7YI3GdmmrslNVsZgx9PfntfWrnXwtR+K/DjI0I/sTKif4Z623um+sjVZ1hk5670B+ODjvHDAckKdjmPTsg==}
    engines: {node: '>= 8.0.0'}
    peerDependencies:
      pg-native: '>=3.0.1'
    peerDependenciesMeta:
      pg-native:
        optional: true

  pgpass@1.0.5:
    resolution: {integrity: sha512-FdW9r/jQZhSeohs1Z3sI1yxFQNFvMcnmfuj4WBMUTxOrAyLMaTcE1aAMBiTlbMNaXvBCQuVi0R7hd8udDSP7ug==}

  picocolors@1.1.0:
    resolution: {integrity: sha512-TQ92mBOW0l3LeMeyLV6mzy/kWr8lkd/hp3mTg7wYK7zJhuBStmGMBG0BdeDZS/dZx1IukaX6Bk11zcln25o1Aw==}

  picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  pify@2.3.0:
    resolution: {integrity: sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==}
    engines: {node: '>=0.10.0'}

  pirates@4.0.6:
    resolution: {integrity: sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==}
    engines: {node: '>= 6'}

  possible-typed-array-names@1.0.0:
    resolution: {integrity: sha512-d7Uw+eZoloe0EHDIYoe+bQ5WXnGMOpmiZFTuMWCwpjzzkL2nTjcKiAk4hh8TjnGye2TwWOk3UXucZ+3rbmBa8Q==}
    engines: {node: '>= 0.4'}

  postcss-import@15.1.0:
    resolution: {integrity: sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      postcss: ^8.0.0

  postcss-js@4.0.1:
    resolution: {integrity: sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==}
    engines: {node: ^12 || ^14 || >= 16}
    peerDependencies:
      postcss: ^8.4.21

  postcss-load-config@4.0.2:
    resolution: {integrity: sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==}
    engines: {node: '>= 14'}
    peerDependencies:
      postcss: '>=8.0.9'
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      postcss:
        optional: true
      ts-node:
        optional: true

  postcss-nested@6.2.0:
    resolution: {integrity: sha512-HQbt28KulC5AJzG+cZtj9kvKB93CFCdLvog1WFLf1D+xmMvPGlBstkpTEZfK5+AN9hfJocyBFCNiqyS48bpgzQ==}
    engines: {node: '>=12.0'}
    peerDependencies:
      postcss: ^8.2.14

  postcss-selector-parser@6.1.2:
    resolution: {integrity: sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==}
    engines: {node: '>=4'}

  postcss-value-parser@4.2.0:
    resolution: {integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==}

  postcss@8.4.31:
    resolution: {integrity: sha512-PS08Iboia9mts/2ygV3eLpY5ghnUcfLV/EXTOW1E2qYxJKGGBUtNjN76FYHnMs36RmARn41bC0AZmn+rR0OVpQ==}
    engines: {node: ^10 || ^12 || >=14}

  postcss@8.4.38:
    resolution: {integrity: sha512-Wglpdk03BSfXkHoQa3b/oulrotAkwrlLDRSOb9D0bN86FdRyE9lppSp33aHNPgBa0JKCoB+drFLZkQoRRYae5A==}
    engines: {node: ^10 || ^12 || >=14}

  postcss@8.4.47:
    resolution: {integrity: sha512-56rxCq7G/XfB4EkXq9Egn5GCqugWvDFjafDOThIdMBsI15iqPqR5r15TfSr1YPYeEI19YeaXMCbY6u88Y76GLQ==}
    engines: {node: ^10 || ^12 || >=14}

  postgres-array@2.0.0:
    resolution: {integrity: sha512-VpZrUqU5A69eQyW2c5CA1jtLecCsN2U/bD6VilrFDWq5+5UIEVO7nazS3TEcHf1zuPYO/sqGvUvW62g86RXZuA==}
    engines: {node: '>=4'}

  postgres-array@3.0.4:
    resolution: {integrity: sha512-nAUSGfSDGOaOAEGwqsRY27GPOea7CNipJPOA7lPbdEpx5Kg3qzdP0AaWC5MlhTWV9s4hFX39nomVZ+C4tnGOJQ==}
    engines: {node: '>=12'}

  postgres-bytea@1.0.0:
    resolution: {integrity: sha512-xy3pmLuQqRBZBXDULy7KbaitYqLcmxigw14Q5sj8QBVLqEwXfeybIKVWiqAXTlcvdvb0+xkOtDbfQMOf4lST1w==}
    engines: {node: '>=0.10.0'}

  postgres-bytea@3.0.0:
    resolution: {integrity: sha512-CNd4jim9RFPkObHSjVHlVrxoVQXz7quwNFpz7RY1okNNme49+sVyiTvTRobiLV548Hx/hb1BG+iE7h9493WzFw==}
    engines: {node: '>= 6'}

  postgres-date@1.0.7:
    resolution: {integrity: sha512-suDmjLVQg78nMK2UZ454hAG+OAW+HQPZ6n++TNDUX+L0+uUlLywnoxJKDou51Zm+zTCjrCl0Nq6J9C5hP9vK/Q==}
    engines: {node: '>=0.10.0'}

  postgres-date@2.1.0:
    resolution: {integrity: sha512-K7Juri8gtgXVcDfZttFKVmhglp7epKb1K4pgrkLxehjqkrgPhfG6OO8LHLkfaqkbpjNRnra018XwAr1yQFWGcA==}
    engines: {node: '>=12'}

  postgres-interval@1.2.0:
    resolution: {integrity: sha512-9ZhXKM/rw350N1ovuWHbGxnGh/SNJ4cnxHiM0rxE4VN41wsg8P8zWn9hv/buK00RP4WvlOyr/RBDiptyxVbkZQ==}
    engines: {node: '>=0.10.0'}

  postgres-interval@3.0.0:
    resolution: {integrity: sha512-BSNDnbyZCXSxgA+1f5UU2GmwhoI0aU5yMxRGO8CdFEcY2BQF9xm/7MqKnYoM1nJDk8nONNWDk9WeSmePFhQdlw==}
    engines: {node: '>=12'}

  postgres-range@1.1.4:
    resolution: {integrity: sha512-i/hbxIE9803Alj/6ytL7UHQxRvZkI9O4Sy+J3HGc4F4oo/2eQAjTSNJ0bfxyse3bH0nuVesCk+3IRLaMtG3H6w==}

  prelude-ls@1.2.1:
    resolution: {integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==}
    engines: {node: '>= 0.8.0'}

  prettier@2.8.8:
    resolution: {integrity: sha512-tdN8qQGvNjw4CHbY+XXk0JgCXn9QiF21a55rBe5LJAU+kDyC4WQn4+awm2Xfk2lQMk5fKup9XgzTZtGkjBdP9Q==}
    engines: {node: '>=10.13.0'}
    hasBin: true

  prismjs@1.27.0:
    resolution: {integrity: sha512-t13BGPUlFDR7wRB5kQDG4jjl7XeuH6jbJGt11JHPL96qwsEHNX2+68tFXqc1/k+/jALsbSWJKUOT/hcYAZ5LkA==}
    engines: {node: '>=6'}

  prismjs@1.29.0:
    resolution: {integrity: sha512-Kx/1w86q/epKcmte75LNrEoT+lX8pBpavuAbvJWRXar7Hz8jrtF+e3vY751p0R8H9HdArwaCTNDDzHg/ScJK1Q==}
    engines: {node: '>=6'}

  prop-types@15.8.1:
    resolution: {integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==}

  property-information@5.6.0:
    resolution: {integrity: sha512-YUHSPk+A30YPv+0Qf8i9Mbfe/C0hdPXk1s1jPVToV8pk8BQtpw10ct89Eo7OWkutrwqvT0eicAxlOg3dOAu8JA==}

  proxy-from-env@1.1.0:
    resolution: {integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==}

  punycode@2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==}
    engines: {node: '>=6'}

  pvtsutils@1.3.5:
    resolution: {integrity: sha512-ARvb14YB9Nm2Xi6nBq1ZX6dAM0FsJnuk+31aUp4TrcZEdKUlSqOqsxJHUPJDNE3qiIp+iUPEIeR6Je/tgV7zsA==}

  pvutils@1.1.3:
    resolution: {integrity: sha512-pMpnA0qRdFp32b1sJl1wOJNxZLQ2cbQx+k6tjNtZ8CpvVhNqEPRgivZ2WOUev2YMajecdH7ctUPDvEe87nariQ==}
    engines: {node: '>=6.0.0'}

  qs@6.13.0:
    resolution: {integrity: sha512-+38qI9SOr8tfZ4QmJNplMUxqjbe7LKvvZgWdExBOmd+egZTtjLB67Gu0HRX3u/XOq7UU2Nx6nsjvS16Z9uwfpg==}
    engines: {node: '>=0.6'}

  queue-microtask@1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}

  quick-lru@4.0.1:
    resolution: {integrity: sha512-ARhCpm70fzdcvNQfPoy49IaanKkTlRWF2JMzqhcJbhSFRZv7nPTvZJdcY7301IPmvW+/p0RgIWnQDLJxifsQ7g==}
    engines: {node: '>=8'}

  react-clientside-effect@1.2.6:
    resolution: {integrity: sha512-XGGGRQAKY+q25Lz9a/4EPqom7WRjz3z9R2k4jhVKA/puQFH/5Nt27vFZYql4m4NVNdUvX8PS3O7r/Zzm7cjUlg==}
    peerDependencies:
      react: ^15.3.0 || ^16.0.0 || ^17.0.0 || ^18.0.0

  react-day-picker@8.10.1:
    resolution: {integrity: sha512-TMx7fNbhLk15eqcMt+7Z7S2KF7mfTId/XJDjKE8f+IUcFn0l08/kI4FiYTL/0yuOLmEcbR4Fwe3GJf/NiiMnPA==}
    peerDependencies:
      date-fns: ^2.28.0 || ^3.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0

  react-dom@18.3.1:
    resolution: {integrity: sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==}
    peerDependencies:
      react: ^18.3.1

  react-fast-compare@3.2.2:
    resolution: {integrity: sha512-nsO+KSNgo1SbJqJEYRE9ERzo7YtYbou/OqjSQKxV7jcKox7+usiUVZOAC+XnDOABXggQTno0Y1CpVnuWEc1boQ==}

  react-focus-lock@2.13.2:
    resolution: {integrity: sha512-T/7bsofxYqnod2xadvuwjGKHOoL5GH7/EIPI5UyEvaU/c2CcphvGI371opFtuY/SYdbMsNiuF4HsHQ50nA/TKQ==}
    peerDependencies:
      '@types/react': ^16.8.0 || ^17.0.0 || ^18.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-hook-form@7.53.0:
    resolution: {integrity: sha512-M1n3HhqCww6S2hxLxciEXy2oISPnAzxY7gvwVPrtlczTM/1dDadXgUxDpHMrMTblDOcm/AXtXxHwZ3jpg1mqKQ==}
    engines: {node: '>=18.0.0'}
    peerDependencies:
      react: ^16.8.0 || ^17 || ^18 || ^19

  react-is@16.13.1:
    resolution: {integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==}

  react-is@18.3.1:
    resolution: {integrity: sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==}

  react-konva-utils@0.3.2:
    resolution: {integrity: sha512-BocSYCPd58rCumFAL9sUs5aum+eRD53Amknd78Mm2BQln/XMZZS8zHq35HPbiZkSMcj5Q6fZ4foHa9aMLcPRIQ==}
    peerDependencies:
      konva: ^8.3.5
      react: ^18.2.0
      react-dom: ^18.2.0

  react-konva@18.2.10:
    resolution: {integrity: sha512-ohcX1BJINL43m4ynjZ24MxFI1syjBdrXhqVxYVDw2rKgr3yuS0x/6m1Y2Z4sl4T/gKhfreBx8KHisd0XC6OT1g==}
    peerDependencies:
      konva: ^8.0.1 || ^7.2.5 || ^9.0.0
      react: '>=18.0.0'
      react-dom: '>=18.0.0'

  react-reconciler@0.29.2:
    resolution: {integrity: sha512-zZQqIiYgDCTP/f1N/mAR10nJGrPD2ZR+jDSEsKWJHYC7Cm2wodlwbR3upZRdC3cjIjSlTLNVyO7Iu0Yy7t2AYg==}
    engines: {node: '>=0.10.0'}
    peerDependencies:
      react: ^18.3.1

  react-remove-scroll-bar@2.3.6:
    resolution: {integrity: sha512-DtSYaao4mBmX+HDo5YWYdBWQwYIQQshUV/dVxFxK+KM26Wjwp1gZ6rv6OC3oujI6Bfu6Xyg3TwK533AQutsn/g==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': ^16.8.0 || ^17.0.0 || ^18.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-remove-scroll@2.5.5:
    resolution: {integrity: sha512-ImKhrzJJsyXJfBZ4bzu8Bwpka14c/fQt0k+cyFp/PBhTfyDnU5hjOtM4AG/0AMyy8oKzOTR0lDgJIM7pYXI0kw==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': ^16.8.0 || ^17.0.0 || ^18.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-remove-scroll@2.5.7:
    resolution: {integrity: sha512-FnrTWO4L7/Bhhf3CYBNArEG/yROV0tKmTv7/3h9QCFvH6sndeFf1wPqOcbFVu5VAulS5dV1wGT3GZZ/1GawqiA==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': ^16.8.0 || ^17.0.0 || ^18.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-remove-scroll@2.6.0:
    resolution: {integrity: sha512-I2U4JVEsQenxDAKaVa3VZ/JeJZe0/2DxPWL8Tj8yLKctQJQiZM52pn/GWFpSp8dftjM3pSAHVJZscAnC/y+ySQ==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': ^16.8.0 || ^17.0.0 || ^18.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-smooth@4.0.1:
    resolution: {integrity: sha512-OE4hm7XqR0jNOq3Qmk9mFLyd6p2+j6bvbPJ7qlB7+oo0eNcL2l7WQzG6MBnT3EXY6xzkLMUBec3AfewJdA0J8w==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
      react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0

  react-style-singleton@2.2.1:
    resolution: {integrity: sha512-ZWj0fHEMyWkHzKYUr2Bs/4zU6XLmq9HsgBURm7g5pAVfyn49DgUiNgY2d4lXRlYSiCif9YBGpQleewkcqddc7g==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': ^16.8.0 || ^17.0.0 || ^18.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-syntax-highlighter@15.5.0:
    resolution: {integrity: sha512-+zq2myprEnQmH5yw6Gqc8lD55QHnpKaU8TOcFeC/Lg/MQSs8UknEA0JC4nTZGFAXC2J2Hyj/ijJ7NlabyPi2gg==}
    peerDependencies:
      react: '>= 0.14.0'

  react-transition-group@4.4.5:
    resolution: {integrity: sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==}
    peerDependencies:
      react: '>=16.6.0'
      react-dom: '>=16.6.0'

  react-transition-state@2.1.2:
    resolution: {integrity: sha512-RkDYBkj1V1ZqBA5AwQPrMt2Uagwsx6b//GVJdRDhs/t0o66w2nhQiyHyFGQEI60mgtbaIdLm8yhBRCvhA+FxEg==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'

  react@18.3.1:
    resolution: {integrity: sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==}
    engines: {node: '>=0.10.0'}

  reactflow@11.11.4:
    resolution: {integrity: sha512-70FOtJkUWH3BAOsN+LU9lCrKoKbtOPnz2uq0CV2PLdNSwxTXOhCbsZr50GmZ+Rtw3jx8Uv7/vBFtCGixLfd4Og==}
    peerDependencies:
      react: '>=17'
      react-dom: '>=17'

  read-cache@1.0.0:
    resolution: {integrity: sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==}

  readdirp@3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}

  recharts-scale@0.4.5:
    resolution: {integrity: sha512-kivNFO+0OcUNu7jQquLXAxz1FIwZj8nrj+YkOKc5694NbjCvcT6aSZiIzNzd2Kul4o4rTto8QVR9lMNtxD4G1w==}

  recharts@2.12.7:
    resolution: {integrity: sha512-hlLJMhPQfv4/3NBSAyq3gzGg4h2v69RJh6KU7b3pXYNNAELs9kEoXOjbkxdXpALqKBoVmVptGfLpxdaVYqjmXQ==}
    engines: {node: '>=14'}
    peerDependencies:
      react: ^16.0.0 || ^17.0.0 || ^18.0.0
      react-dom: ^16.0.0 || ^17.0.0 || ^18.0.0

  reflect.getprototypeof@1.0.6:
    resolution: {integrity: sha512-fmfw4XgoDke3kdI6h4xcUz1dG8uaiv5q9gcEwLS4Pnth2kxT+GZ7YehS1JTMGBQmtV7Y4GFGbs2re2NqhdozUg==}
    engines: {node: '>= 0.4'}

  refractor@3.6.0:
    resolution: {integrity: sha512-MY9W41IOWxxk31o+YvFCNyNzdkc9M20NoZK5vq6jkv4I/uh2zkWcfudj0Q1fovjUQJrNewS9NMzeTtqPf+n5EA==}

  regenerator-runtime@0.14.1:
    resolution: {integrity: sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==}

  regexp.prototype.flags@1.5.2:
    resolution: {integrity: sha512-NcDiDkTLuPR+++OCKB0nWafEmhg/Da8aUPLPMQbK+bxKKCm1/S5he+AqYa4PlMCVBalb4/yxIRub6qkEx5yJbw==}
    engines: {node: '>= 0.4'}

  resolve-from@4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}

  resolve-pkg-maps@1.0.0:
    resolution: {integrity: sha512-seS2Tj26TBVOC2NIc2rOe2y2ZO7efxITtLZcGSOnHHNOQ7CkiUBfw0Iw2ck6xkIhPwLhKNLS8BO+hEpngQlqzw==}

  resolve@1.22.8:
    resolution: {integrity: sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==}
    hasBin: true

  resolve@2.0.0-next.5:
    resolution: {integrity: sha512-U7WjGVG9sH8tvjW5SmGbQuui75FiyjAX72HX15DwBBwF9dNiQZRQAg9nnPhYy+TUnE0+VcrttuvNI8oSxZcocA==}
    hasBin: true

  reusify@1.0.4:
    resolution: {integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  rimraf@3.0.2:
    resolution: {integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==}
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true

  run-parallel@1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}

  safe-array-concat@1.1.2:
    resolution: {integrity: sha512-vj6RsCsWBCf19jIeHEfkRMw8DPiBb+DMXklQ/1SGDHOMlHdPUkZXFQ2YdplS23zESTijAcurb1aSgJA3AgMu1Q==}
    engines: {node: '>=0.4'}

  safe-regex-test@1.0.3:
    resolution: {integrity: sha512-CdASjNJPvRa7roO6Ra/gLYBTzYzzPyyBXxIMdGW3USQLyjWEls2RgW5UBTXaQVp+OrpeCK3bLem8smtmheoRuw==}
    engines: {node: '>= 0.4'}

  sass@1.78.0:
    resolution: {integrity: sha512-AaIqGSrjo5lA2Yg7RvFZrlXDBCp3nV4XP73GrLGvdRWWwk+8H3l0SDvq/5bA4eF+0RFPLuWUk3E+P1U/YqnpsQ==}
    engines: {node: '>=14.0.0'}
    hasBin: true

  scheduler@0.23.2:
    resolution: {integrity: sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==}

  semver@6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==}
    hasBin: true

  semver@7.6.3:
    resolution: {integrity: sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==}
    engines: {node: '>=10'}
    hasBin: true

  semver@7.7.2:
    resolution: {integrity: sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==}
    engines: {node: '>=10'}
    hasBin: true

  set-function-length@1.2.2:
    resolution: {integrity: sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==}
    engines: {node: '>= 0.4'}

  set-function-name@2.0.2:
    resolution: {integrity: sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==}
    engines: {node: '>= 0.4'}

  shallowequal@1.1.0:
    resolution: {integrity: sha512-y0m1JoUZSlPAjXVtPPW70aZWfIL/dSP7AFkRnniLCrK/8MDKog3TySTBmckD+RObVxH0v4Tox67+F14PdED2oQ==}

  sharp@0.34.2:
    resolution: {integrity: sha512-lszvBmB9QURERtyKT2bNmsgxXK0ShJrL/fvqlonCo7e6xBF8nT8xU6pW+PMIbLsz0RxQk3rgH9kd8UmvOzlMJg==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}

  shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}

  side-channel@1.0.6:
    resolution: {integrity: sha512-fDW/EZ6Q9RiO8eFG8Hj+7u/oW+XrPTIChwCOM2+th2A6OblDtYYIpve9m+KvI9Z4C9qSEXlaGR6bTEYHReuglA==}
    engines: {node: '>= 0.4'}

  signal-exit@4.1.0:
    resolution: {integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==}
    engines: {node: '>=14'}

  simple-swizzle@0.2.2:
    resolution: {integrity: sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==}

  slash@3.0.0:
    resolution: {integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==}
    engines: {node: '>=8'}

  snake-case@3.0.4:
    resolution: {integrity: sha512-LAOh4z89bGQvl9pFfNF8V146i7o7/CqFPbqzYgP+yYzDIDeS9HaNFtXABamRW+AQzEVODcvE79ljJ+8a9YSdMg==}

  snakecase-keys@3.2.1:
    resolution: {integrity: sha512-CjU5pyRfwOtaOITYv5C8DzpZ8XA/ieRsDpr93HI2r6e3YInC6moZpSQbmUtg8cTk58tq2x3jcG2gv+p1IZGmMA==}
    engines: {node: '>=8'}

  snakecase-keys@5.4.4:
    resolution: {integrity: sha512-YTywJG93yxwHLgrYLZjlC75moVEX04LZM4FHfihjHe1FCXm+QaLOFfSf535aXOAd0ArVQMWUAe8ZPm4VtWyXaA==}
    engines: {node: '>=12'}

  source-map-js@1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==}
    engines: {node: '>=0.10.0'}

  source-map@0.5.7:
    resolution: {integrity: sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==}
    engines: {node: '>=0.10.0'}

  space-separated-tokens@1.1.5:
    resolution: {integrity: sha512-q/JSVd1Lptzhf5bkYm4ob4iWPjx0KiRe3sRFBNrVqbJkFaBm5vbbowy1mymoPNLRa52+oadOhJ+K49wsSeSjTA==}

  split2@4.2.0:
    resolution: {integrity: sha512-UcjcJOWknrNkF6PLX83qcHM6KHgVKNkV62Y8a5uYDVv9ydGQVwAHMKqHdJje1VTWpljG0WYpCDhrCdAOYH4TWg==}
    engines: {node: '>= 10.x'}

  stop-iteration-iterator@1.0.0:
    resolution: {integrity: sha512-iCGQj+0l0HOdZ2AEeBADlsRC+vsnDsZsbdSiH1yNSjcfKM7fdpCMfqAL/dwF5BLiw/XhRft/Wax6zQbhq2BcjQ==}
    engines: {node: '>= 0.4'}

  string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}

  string-width@5.1.2:
    resolution: {integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==}
    engines: {node: '>=12'}

  string.prototype.includes@2.0.0:
    resolution: {integrity: sha512-E34CkBgyeqNDcrbU76cDjL5JLcVrtSdYq0MEh/B10r17pRP4ciHLwTgnuLV8Ay6cgEMLkcBkFCKyFZ43YldYzg==}

  string.prototype.matchall@4.0.11:
    resolution: {integrity: sha512-NUdh0aDavY2og7IbBPenWqR9exH+E26Sv8e0/eTe1tltDGZL+GtBkDAnnyBtmekfK6/Dq3MkcGtzXFEd1LQrtg==}
    engines: {node: '>= 0.4'}

  string.prototype.repeat@1.0.0:
    resolution: {integrity: sha512-0u/TldDbKD8bFCQ/4f5+mNRrXwZ8hg2w7ZR8wa16e8z9XpePWl3eGEcUD0OXpEH/VJH/2G3gjUtR3ZOiBe2S/w==}

  string.prototype.trim@1.2.9:
    resolution: {integrity: sha512-klHuCNxiMZ8MlsOihJhJEBJAiMVqU3Z2nEXWfWnIqjN0gEFS9J9+IxKozWWtQGcgoa1WUZzLjKPTr4ZHNFTFxw==}
    engines: {node: '>= 0.4'}

  string.prototype.trimend@1.0.8:
    resolution: {integrity: sha512-p73uL5VCHCO2BZZ6krwwQE3kCzM7NKmis8S//xEC6fQonchbum4eP6kR4DLEjQFO3Wnj3Fuo8NM0kOSjVdHjZQ==}

  string.prototype.trimstart@1.0.8:
    resolution: {integrity: sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg==}
    engines: {node: '>= 0.4'}

  strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}

  strip-ansi@7.1.0:
    resolution: {integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==}
    engines: {node: '>=12'}

  strip-bom@3.0.0:
    resolution: {integrity: sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==}
    engines: {node: '>=4'}

  strip-json-comments@3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==}
    engines: {node: '>=8'}

  stripe@12.18.0:
    resolution: {integrity: sha512-cYjgBM2SY/dTm8Lr6eMyyONaHTZHA/QjHxFUIW5WH8FevSRIGAVtXEmBkUXF1fsqe7QvvRgQSGSJZmjDacegGg==}
    engines: {node: '>=12.*'}

  strnum@1.0.5:
    resolution: {integrity: sha512-J8bbNyKKXl5qYcR36TIO8W3mVGVHrmmxsd5PAItGkmyzwJvybiw2IVq5nqd0i4LSNSkB/sx9VHllbfFdr9k1JA==}

  styled-components@6.1.13:
    resolution: {integrity: sha512-M0+N2xSnAtwcVAQeFEsGWFFxXDftHUD7XrKla06QbpUMmbmtFBMMTcKWvFXtWxuD5qQkB8iU5gk6QASlx2ZRMw==}
    engines: {node: '>= 16'}
    peerDependencies:
      react: '>= 16.8.0'
      react-dom: '>= 16.8.0'

  styled-jsx@5.1.6:
    resolution: {integrity: sha512-qSVyDTeMotdvQYoHWLNGwRFJHC+i+ZvdBRYosOFgC+Wg1vx4frN2/RG/NA7SYqqvKNLf39P2LSRA2pu6n0XYZA==}
    engines: {node: '>= 12.0.0'}
    peerDependencies:
      '@babel/core': '*'
      babel-plugin-macros: '*'
      react: '>= 16.8.0 || 17.x.x || ^18.0.0-0 || ^19.0.0-0'
    peerDependenciesMeta:
      '@babel/core':
        optional: true
      babel-plugin-macros:
        optional: true

  stylis@4.2.0:
    resolution: {integrity: sha512-Orov6g6BB1sDfYgzWfTHDOxamtX1bE/zo104Dh9e6fqJ3PooipYyfJ0pUmrZO2wAvO8YbEyeFrkV91XTsGMSrw==}

  stylis@4.3.2:
    resolution: {integrity: sha512-bhtUjWd/z6ltJiQwg0dUfxEJ+W+jdqQd8TbWLWyeIJHlnsqmGLRFFd8e5mA0AZi/zx90smXRlN66YMTcaSFifg==}

  sucrase@3.35.0:
    resolution: {integrity: sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==}
    engines: {node: '>=16 || 14 >=14.17'}
    hasBin: true

  supports-color@5.5.0:
    resolution: {integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==}
    engines: {node: '>=4'}

  supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}

  supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}

  swr@2.2.0:
    resolution: {integrity: sha512-AjqHOv2lAhkuUdIiBu9xbuettzAzWXmCEcLONNKJRba87WAefz8Ca9d6ds/SzrPc235n1IxWYdhJ2zF3MNUaoQ==}
    peerDependencies:
      react: ^16.11.0 || ^17.0.0 || ^18.0.0

  swr@2.2.5:
    resolution: {integrity: sha512-QtxqyclFeAsxEUeZIYmsaQ0UjimSq1RZ9Un7I68/0ClKK/U3LoyQunwkQfJZr2fc22DfIXLNDc2wFyTEikCUpg==}
    peerDependencies:
      react: ^16.11.0 || ^17.0.0 || ^18.0.0

  tabbable@6.2.0:
    resolution: {integrity: sha512-Cat63mxsVJlzYvN51JmVXIgNoUokrIaT2zLclCXjRd8boZ0004U4KCs/sToJ75C6sdlByWxpYnb5Boif1VSFew==}

  tailwind-merge@1.14.0:
    resolution: {integrity: sha512-3mFKyCo/MBcgyOTlrY8T7odzZFx+w+qKSMAmdFzRvqBfLlSigU6TZnlFHK0lkMwj9Bj8OYU+9yW9lmGuS0QEnQ==}

  tailwind-merge@2.5.2:
    resolution: {integrity: sha512-kjEBm+pvD+6eAwzJL2Bi+02/9LFLal1Gs61+QB7HvTfQQ0aXwC5LGT8PEt1gS0CWKktKe6ysPTAy3cBC5MeiIg==}

  tailwindcss-animate@1.0.7:
    resolution: {integrity: sha512-bl6mpH3T7I3UFxuvDEXLxy/VuFxBk5bbzplh7tXI68mwMokNYd1t9qPBHlnyTwfa4JGC4zP516I1hYYtQ/vspA==}
    peerDependencies:
      tailwindcss: '>=3.0.0 || insiders'

  tailwindcss@3.4.11:
    resolution: {integrity: sha512-qhEuBcLemjSJk5ajccN9xJFtM/h0AVCPaA6C92jNP+M2J8kX+eMJHI7R2HFKUvvAsMpcfLILMCFYSeDwpMmlUg==}
    engines: {node: '>=14.0.0'}
    hasBin: true

  tapable@2.2.1:
    resolution: {integrity: sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==}
    engines: {node: '>=6'}

  text-table@0.2.0:
    resolution: {integrity: sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==}

  thenify-all@1.6.0:
    resolution: {integrity: sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==}
    engines: {node: '>=0.8'}

  thenify@3.3.1:
    resolution: {integrity: sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==}

  tiny-invariant@1.3.3:
    resolution: {integrity: sha512-+FbBPE1o9QAYvviau/qC5SE3caw21q3xkvWKBtja5vgqOWIHHJ3ioaq1VPfn/Szqctz2bU/oYeKd9/z5BL+PVg==}

  to-fast-properties@2.0.0:
    resolution: {integrity: sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==}
    engines: {node: '>=4'}

  to-no-case@1.0.2:
    resolution: {integrity: sha512-Z3g735FxuZY8rodxV4gH7LxClE4H0hTIyHNIHdk+vpQxjLm0cwnKXq/OFVZ76SOQmto7txVcwSCwkU5kqp+FKg==}

  to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}

  to-snake-case@1.0.0:
    resolution: {integrity: sha512-joRpzBAk1Bhi2eGEYBjukEWHOe/IvclOkiJl3DtA91jV6NwQ3MwXA4FHYeqk8BNp/D8bmi9tcNbRu/SozP0jbQ==}

  to-space-case@1.0.0:
    resolution: {integrity: sha512-rLdvwXZ39VOn1IxGL3V6ZstoTbwLRckQmn/U8ZDLuWwIXNpuZDhQ3AiRUlhTbOXFVE9C+dR51wM0CBDhk31VcA==}

  toggle-selection@1.0.6:
    resolution: {integrity: sha512-BiZS+C1OS8g/q2RRbJmy59xpyghNBqrr6k5L/uKBGRsTfxmu3ffiRnd8mlGPUVayg8pvfi5urfnu8TU7DVOkLQ==}

  tr46@0.0.3:
    resolution: {integrity: sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==}

  ts-interface-checker@0.1.13:
    resolution: {integrity: sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==}

  tsconfig-paths@3.15.0:
    resolution: {integrity: sha512-2Ac2RgzDe/cn48GvOe3M+o82pEFewD3UPbyoUHHdKasHwJKjds4fLXWf/Ux5kATBKN20oaFGu+jbElp1pos0mg==}

  tslib@1.14.1:
    resolution: {integrity: sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==}

  tslib@2.4.0:
    resolution: {integrity: sha512-d6xOpEDfsi2CZVlPQzGeux8XMwLT9hssAsaPYExaQMuYskwb+x1x7J371tWlbBdWHroy99KnVB6qIkUbs5X3UQ==}

  tslib@2.4.1:
    resolution: {integrity: sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==}

  tslib@2.6.2:
    resolution: {integrity: sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q==}

  tslib@2.7.0:
    resolution: {integrity: sha512-gLXCKdN1/j47AiHiOkJN69hJmcbGTHI0ImLmbYLHykhgeN0jVGola9yVjFgzCUklsZQMW55o+dW7IXv3RCXDzA==}

  tslib@2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==}

  tsutils@3.21.0:
    resolution: {integrity: sha512-mHKK3iUXL+3UF6xL5k0PEhKRUBKPBCv/+RkEOpjRWxxx27KKRBmmA60A9pgOUvMi8GKhRMPEmjBRPzs2W7O1OA==}
    engines: {node: '>= 6'}
    peerDependencies:
      typescript: '>=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta'

  type-check@0.4.0:
    resolution: {integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==}
    engines: {node: '>= 0.8.0'}

  type-fest@0.20.2:
    resolution: {integrity: sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==}
    engines: {node: '>=10'}

  type-fest@2.19.0:
    resolution: {integrity: sha512-RAH822pAdBgcNMAfWnCBU3CFZcfZ/i1eZjwFU/dsLKumyuuP3niueg2UAukXYF0E2AAoc82ZSSf9J0WQBinzHA==}
    engines: {node: '>=12.20'}

  typed-array-buffer@1.0.2:
    resolution: {integrity: sha512-gEymJYKZtKXzzBzM4jqa9w6Q1Jjm7x2d+sh19AdsD4wqnMPDYyvwpsIc2Q/835kHuo3BEQ7CjelGhfTsoBb2MQ==}
    engines: {node: '>= 0.4'}

  typed-array-byte-length@1.0.1:
    resolution: {integrity: sha512-3iMJ9q0ao7WE9tWcaYKIptkNBuOIcZCCT0d4MRvuuH88fEoEH62IuQe0OtraD3ebQEoTRk8XCBoknUNc1Y67pw==}
    engines: {node: '>= 0.4'}

  typed-array-byte-offset@1.0.2:
    resolution: {integrity: sha512-Ous0vodHa56FviZucS2E63zkgtgrACj7omjwd/8lTEMEPFFyjfixMZ1ZXenpgCFBBt4EC1J2XsyVS2gkG0eTFA==}
    engines: {node: '>= 0.4'}

  typed-array-length@1.0.6:
    resolution: {integrity: sha512-/OxDN6OtAk5KBpGb28T+HZc2M+ADtvRxXrKKbUwtsLgdoxgX13hyy7ek6bFRl5+aBs2yZzB0c4CnQfAtVypW/g==}
    engines: {node: '>= 0.4'}

  typescript@5.6.2:
    resolution: {integrity: sha512-NW8ByodCSNCwZeghjN3o+JX5OFH0Ojg6sadjEKY4huZ52TqbJTJnDo5+Tw98lSy63NZvi4n+ez5m2u5d4PkZyw==}
    engines: {node: '>=14.17'}
    hasBin: true

  unbox-primitive@1.0.2:
    resolution: {integrity: sha512-61pPlCD9h51VoreyJ0BReideM3MDKMKnh6+V9L08331ipq6Q8OFXZYiqP6n/tbHx4s5I9uRhcye6BrbkizkBDw==}

  undici-types@5.26.5:
    resolution: {integrity: sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==}

  uri-js@4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}

  url-join@4.0.1:
    resolution: {integrity: sha512-jk1+QP6ZJqyOiuEI9AEWQfju/nB2Pw466kbA0LEZljHwKeMgd9WrAEgEGxjPDD2+TNbbb37rTyhEfrCXfuKXnA==}

  use-callback-ref@1.3.2:
    resolution: {integrity: sha512-elOQwe6Q8gqZgDA8mrh44qRTQqpIHDcZ3hXTLjBe1i4ph8XpNJnO+aQf3NaG+lriLopI4HMx9VjQLfPQ6vhnoA==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': ^16.8.0 || ^17.0.0 || ^18.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  use-image@1.1.1:
    resolution: {integrity: sha512-n4YO2k8AJG/BcDtxmBx8Aa+47kxY5m335dJiCQA5tTeVU4XdhrhqR6wT0WISRXwdMEOv5CSjqekDZkEMiiWaYQ==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'

  use-sidecar@1.1.2:
    resolution: {integrity: sha512-epTbsLuzZ7lPClpz2TyryBfztm7m+28DlEv2ZCQ3MDr5ssiwyOwGH/e5F9CkfWjJ1t4clvI58yF822/GUkjjhw==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': ^16.9.0 || ^17.0.0 || ^18.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  use-sync-external-store@1.2.2:
    resolution: {integrity: sha512-PElTlVMwpblvbNqQ82d2n6RjStvdSoNe9FG28kNfz3WiXilJm4DdNkEzRhCZuIDwY8U08WVihhGR5iRqAwfDiw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0

  util-deprecate@1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}

  uuid@9.0.1:
    resolution: {integrity: sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==}
    hasBin: true

  victory-vendor@36.9.2:
    resolution: {integrity: sha512-PnpQQMuxlwYdocC8fIJqVXvkeViHYzotI+NJrCuav0ZYFoq912ZHBk3mCeuj+5/VpodOjPe1z0Fk2ihgzlXqjQ==}

  web-streams-polyfill@4.0.0-beta.3:
    resolution: {integrity: sha512-QW95TCTaHmsYfHDybGMwO5IJIM93I/6vTRk+daHTWFPhwh+C8Cg7j7XyKrwrj8Ib6vYXe0ocYNrmzY4xAAN6ug==}
    engines: {node: '>= 14'}

  webcrypto-core@1.8.0:
    resolution: {integrity: sha512-kR1UQNH8MD42CYuLzvibfakG5Ew5seG85dMMoAM/1LqvckxaF6pUiidLuraIu4V+YCIFabYecUZAW0TuxAoaqw==}

  webidl-conversions@3.0.1:
    resolution: {integrity: sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==}

  whatwg-url@5.0.0:
    resolution: {integrity: sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==}

  which-boxed-primitive@1.0.2:
    resolution: {integrity: sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg==}

  which-builtin-type@1.1.4:
    resolution: {integrity: sha512-bppkmBSsHFmIMSl8BO9TbsyzsvGjVoppt8xUiGzwiu/bhDCGxnpOKCxgqj6GuyHE0mINMDecBFPlOm2hzY084w==}
    engines: {node: '>= 0.4'}

  which-collection@1.0.2:
    resolution: {integrity: sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw==}
    engines: {node: '>= 0.4'}

  which-typed-array@1.1.15:
    resolution: {integrity: sha512-oV0jmFtUky6CXfkqehVvBP/LSWJ2sy4vWMioiENyJLePrBO/yKyV9OyJySfAKosh+RYkIl5zJCNZ8/4JncrpdA==}
    engines: {node: '>= 0.4'}

  which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true

  word-wrap@1.2.5:
    resolution: {integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==}
    engines: {node: '>=0.10.0'}

  wrap-ansi@7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}

  wrap-ansi@8.1.0:
    resolution: {integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==}
    engines: {node: '>=12'}

  wrappy@1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==}

  xtend@4.0.2:
    resolution: {integrity: sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==}
    engines: {node: '>=0.4'}

  yaml@1.10.2:
    resolution: {integrity: sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==}
    engines: {node: '>= 6'}

  yaml@2.5.1:
    resolution: {integrity: sha512-bLQOjaX/ADgQ20isPJRvF0iRUHIxVhYvr53Of7wGcWlO2jvtUlH5m87DsmulFVxRpNLOnI4tB6p/oh8D7kpn9Q==}
    engines: {node: '>= 14'}
    hasBin: true

  yocto-queue@0.1.0:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==}
    engines: {node: '>=10'}

  zustand@4.5.5:
    resolution: {integrity: sha512-+0PALYNJNgK6hldkgDq2vLrw5f6g/jCInz52n9RTpropGgeAf/ioFUCdtsjCqu4gNhW9D01rUQBROoRjdzyn2Q==}
    engines: {node: '>=12.7.0'}
    peerDependencies:
      '@types/react': '>=16.8'
      immer: '>=9.0.6'
      react: '>=16.8'
    peerDependenciesMeta:
      '@types/react':
        optional: true
      immer:
        optional: true
      react:
        optional: true

snapshots:

  '@alloc/quick-lru@5.2.0': {}

  '@aws-crypto/crc32@5.2.0':
    dependencies:
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.649.0
      tslib: 2.7.0

  '@aws-crypto/crc32c@5.2.0':
    dependencies:
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.649.0
      tslib: 2.7.0

  '@aws-crypto/sha1-browser@5.2.0':
    dependencies:
      '@aws-crypto/supports-web-crypto': 5.2.0
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.649.0
      '@aws-sdk/util-locate-window': 3.568.0
      '@smithy/util-utf8': 2.3.0
      tslib: 2.7.0

  '@aws-crypto/sha256-browser@5.2.0':
    dependencies:
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-crypto/supports-web-crypto': 5.2.0
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.649.0
      '@aws-sdk/util-locate-window': 3.568.0
      '@smithy/util-utf8': 2.3.0
      tslib: 2.7.0

  '@aws-crypto/sha256-js@5.2.0':
    dependencies:
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.649.0
      tslib: 2.7.0

  '@aws-crypto/supports-web-crypto@5.2.0':
    dependencies:
      tslib: 2.7.0

  '@aws-crypto/util@5.2.0':
    dependencies:
      '@aws-sdk/types': 3.649.0
      '@smithy/util-utf8': 2.3.0
      tslib: 2.7.0

  '@aws-sdk/client-s3@3.651.1':
    dependencies:
      '@aws-crypto/sha1-browser': 5.2.0
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/client-sso-oidc': 3.651.1(@aws-sdk/client-sts@3.651.1)
      '@aws-sdk/client-sts': 3.651.1
      '@aws-sdk/core': 3.651.1
      '@aws-sdk/credential-provider-node': 3.651.1(@aws-sdk/client-sso-oidc@3.651.1(@aws-sdk/client-sts@3.651.1))(@aws-sdk/client-sts@3.651.1)
      '@aws-sdk/middleware-bucket-endpoint': 3.649.0
      '@aws-sdk/middleware-expect-continue': 3.649.0
      '@aws-sdk/middleware-flexible-checksums': 3.651.1
      '@aws-sdk/middleware-host-header': 3.649.0
      '@aws-sdk/middleware-location-constraint': 3.649.0
      '@aws-sdk/middleware-logger': 3.649.0
      '@aws-sdk/middleware-recursion-detection': 3.649.0
      '@aws-sdk/middleware-sdk-s3': 3.651.1
      '@aws-sdk/middleware-ssec': 3.649.0
      '@aws-sdk/middleware-user-agent': 3.649.0
      '@aws-sdk/region-config-resolver': 3.649.0
      '@aws-sdk/signature-v4-multi-region': 3.651.1
      '@aws-sdk/types': 3.649.0
      '@aws-sdk/util-endpoints': 3.649.0
      '@aws-sdk/util-user-agent-browser': 3.649.0
      '@aws-sdk/util-user-agent-node': 3.649.0
      '@aws-sdk/xml-builder': 3.649.0
      '@smithy/config-resolver': 3.0.8
      '@smithy/core': 2.4.3
      '@smithy/eventstream-serde-browser': 3.0.9
      '@smithy/eventstream-serde-config-resolver': 3.0.6
      '@smithy/eventstream-serde-node': 3.0.8
      '@smithy/fetch-http-handler': 3.2.7
      '@smithy/hash-blob-browser': 3.1.5
      '@smithy/hash-node': 3.0.6
      '@smithy/hash-stream-node': 3.1.5
      '@smithy/invalid-dependency': 3.0.6
      '@smithy/md5-js': 3.0.6
      '@smithy/middleware-content-length': 3.0.8
      '@smithy/middleware-endpoint': 3.1.3
      '@smithy/middleware-retry': 3.0.18
      '@smithy/middleware-serde': 3.0.6
      '@smithy/middleware-stack': 3.0.6
      '@smithy/node-config-provider': 3.1.7
      '@smithy/node-http-handler': 3.2.2
      '@smithy/protocol-http': 4.1.3
      '@smithy/smithy-client': 3.3.2
      '@smithy/types': 3.4.2
      '@smithy/url-parser': 3.0.6
      '@smithy/util-base64': 3.0.0
      '@smithy/util-body-length-browser': 3.0.0
      '@smithy/util-body-length-node': 3.0.0
      '@smithy/util-defaults-mode-browser': 3.0.18
      '@smithy/util-defaults-mode-node': 3.0.18
      '@smithy/util-endpoints': 2.1.2
      '@smithy/util-middleware': 3.0.6
      '@smithy/util-retry': 3.0.6
      '@smithy/util-stream': 3.1.6
      '@smithy/util-utf8': 3.0.0
      '@smithy/util-waiter': 3.1.5
      tslib: 2.7.0
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/client-sso-oidc@3.651.1(@aws-sdk/client-sts@3.651.1)':
    dependencies:
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/client-sts': 3.651.1
      '@aws-sdk/core': 3.651.1
      '@aws-sdk/credential-provider-node': 3.651.1(@aws-sdk/client-sso-oidc@3.651.1(@aws-sdk/client-sts@3.651.1))(@aws-sdk/client-sts@3.651.1)
      '@aws-sdk/middleware-host-header': 3.649.0
      '@aws-sdk/middleware-logger': 3.649.0
      '@aws-sdk/middleware-recursion-detection': 3.649.0
      '@aws-sdk/middleware-user-agent': 3.649.0
      '@aws-sdk/region-config-resolver': 3.649.0
      '@aws-sdk/types': 3.649.0
      '@aws-sdk/util-endpoints': 3.649.0
      '@aws-sdk/util-user-agent-browser': 3.649.0
      '@aws-sdk/util-user-agent-node': 3.649.0
      '@smithy/config-resolver': 3.0.8
      '@smithy/core': 2.4.3
      '@smithy/fetch-http-handler': 3.2.7
      '@smithy/hash-node': 3.0.6
      '@smithy/invalid-dependency': 3.0.6
      '@smithy/middleware-content-length': 3.0.8
      '@smithy/middleware-endpoint': 3.1.3
      '@smithy/middleware-retry': 3.0.18
      '@smithy/middleware-serde': 3.0.6
      '@smithy/middleware-stack': 3.0.6
      '@smithy/node-config-provider': 3.1.7
      '@smithy/node-http-handler': 3.2.2
      '@smithy/protocol-http': 4.1.3
      '@smithy/smithy-client': 3.3.2
      '@smithy/types': 3.4.2
      '@smithy/url-parser': 3.0.6
      '@smithy/util-base64': 3.0.0
      '@smithy/util-body-length-browser': 3.0.0
      '@smithy/util-body-length-node': 3.0.0
      '@smithy/util-defaults-mode-browser': 3.0.18
      '@smithy/util-defaults-mode-node': 3.0.18
      '@smithy/util-endpoints': 2.1.2
      '@smithy/util-middleware': 3.0.6
      '@smithy/util-retry': 3.0.6
      '@smithy/util-utf8': 3.0.0
      tslib: 2.7.0
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/client-sso@3.651.1':
    dependencies:
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/core': 3.651.1
      '@aws-sdk/middleware-host-header': 3.649.0
      '@aws-sdk/middleware-logger': 3.649.0
      '@aws-sdk/middleware-recursion-detection': 3.649.0
      '@aws-sdk/middleware-user-agent': 3.649.0
      '@aws-sdk/region-config-resolver': 3.649.0
      '@aws-sdk/types': 3.649.0
      '@aws-sdk/util-endpoints': 3.649.0
      '@aws-sdk/util-user-agent-browser': 3.649.0
      '@aws-sdk/util-user-agent-node': 3.649.0
      '@smithy/config-resolver': 3.0.8
      '@smithy/core': 2.4.3
      '@smithy/fetch-http-handler': 3.2.7
      '@smithy/hash-node': 3.0.6
      '@smithy/invalid-dependency': 3.0.6
      '@smithy/middleware-content-length': 3.0.8
      '@smithy/middleware-endpoint': 3.1.3
      '@smithy/middleware-retry': 3.0.18
      '@smithy/middleware-serde': 3.0.6
      '@smithy/middleware-stack': 3.0.6
      '@smithy/node-config-provider': 3.1.7
      '@smithy/node-http-handler': 3.2.2
      '@smithy/protocol-http': 4.1.3
      '@smithy/smithy-client': 3.3.2
      '@smithy/types': 3.4.2
      '@smithy/url-parser': 3.0.6
      '@smithy/util-base64': 3.0.0
      '@smithy/util-body-length-browser': 3.0.0
      '@smithy/util-body-length-node': 3.0.0
      '@smithy/util-defaults-mode-browser': 3.0.18
      '@smithy/util-defaults-mode-node': 3.0.18
      '@smithy/util-endpoints': 2.1.2
      '@smithy/util-middleware': 3.0.6
      '@smithy/util-retry': 3.0.6
      '@smithy/util-utf8': 3.0.0
      tslib: 2.7.0
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/client-sts@3.651.1':
    dependencies:
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/client-sso-oidc': 3.651.1(@aws-sdk/client-sts@3.651.1)
      '@aws-sdk/core': 3.651.1
      '@aws-sdk/credential-provider-node': 3.651.1(@aws-sdk/client-sso-oidc@3.651.1(@aws-sdk/client-sts@3.651.1))(@aws-sdk/client-sts@3.651.1)
      '@aws-sdk/middleware-host-header': 3.649.0
      '@aws-sdk/middleware-logger': 3.649.0
      '@aws-sdk/middleware-recursion-detection': 3.649.0
      '@aws-sdk/middleware-user-agent': 3.649.0
      '@aws-sdk/region-config-resolver': 3.649.0
      '@aws-sdk/types': 3.649.0
      '@aws-sdk/util-endpoints': 3.649.0
      '@aws-sdk/util-user-agent-browser': 3.649.0
      '@aws-sdk/util-user-agent-node': 3.649.0
      '@smithy/config-resolver': 3.0.8
      '@smithy/core': 2.4.3
      '@smithy/fetch-http-handler': 3.2.7
      '@smithy/hash-node': 3.0.6
      '@smithy/invalid-dependency': 3.0.6
      '@smithy/middleware-content-length': 3.0.8
      '@smithy/middleware-endpoint': 3.1.3
      '@smithy/middleware-retry': 3.0.18
      '@smithy/middleware-serde': 3.0.6
      '@smithy/middleware-stack': 3.0.6
      '@smithy/node-config-provider': 3.1.7
      '@smithy/node-http-handler': 3.2.2
      '@smithy/protocol-http': 4.1.3
      '@smithy/smithy-client': 3.3.2
      '@smithy/types': 3.4.2
      '@smithy/url-parser': 3.0.6
      '@smithy/util-base64': 3.0.0
      '@smithy/util-body-length-browser': 3.0.0
      '@smithy/util-body-length-node': 3.0.0
      '@smithy/util-defaults-mode-browser': 3.0.18
      '@smithy/util-defaults-mode-node': 3.0.18
      '@smithy/util-endpoints': 2.1.2
      '@smithy/util-middleware': 3.0.6
      '@smithy/util-retry': 3.0.6
      '@smithy/util-utf8': 3.0.0
      tslib: 2.7.0
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/core@3.651.1':
    dependencies:
      '@smithy/core': 2.4.3
      '@smithy/node-config-provider': 3.1.7
      '@smithy/property-provider': 3.1.6
      '@smithy/protocol-http': 4.1.3
      '@smithy/signature-v4': 4.1.3
      '@smithy/smithy-client': 3.3.2
      '@smithy/types': 3.4.2
      '@smithy/util-middleware': 3.0.6
      fast-xml-parser: 4.4.1
      tslib: 2.7.0

  '@aws-sdk/credential-provider-env@3.649.0':
    dependencies:
      '@aws-sdk/types': 3.649.0
      '@smithy/property-provider': 3.1.6
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@aws-sdk/credential-provider-http@3.649.0':
    dependencies:
      '@aws-sdk/types': 3.649.0
      '@smithy/fetch-http-handler': 3.2.7
      '@smithy/node-http-handler': 3.2.2
      '@smithy/property-provider': 3.1.6
      '@smithy/protocol-http': 4.1.3
      '@smithy/smithy-client': 3.3.2
      '@smithy/types': 3.4.2
      '@smithy/util-stream': 3.1.6
      tslib: 2.7.0

  '@aws-sdk/credential-provider-ini@3.651.1(@aws-sdk/client-sso-oidc@3.651.1(@aws-sdk/client-sts@3.651.1))(@aws-sdk/client-sts@3.651.1)':
    dependencies:
      '@aws-sdk/client-sts': 3.651.1
      '@aws-sdk/credential-provider-env': 3.649.0
      '@aws-sdk/credential-provider-http': 3.649.0
      '@aws-sdk/credential-provider-process': 3.649.0
      '@aws-sdk/credential-provider-sso': 3.651.1(@aws-sdk/client-sso-oidc@3.651.1(@aws-sdk/client-sts@3.651.1))
      '@aws-sdk/credential-provider-web-identity': 3.649.0(@aws-sdk/client-sts@3.651.1)
      '@aws-sdk/types': 3.649.0
      '@smithy/credential-provider-imds': 3.2.3
      '@smithy/property-provider': 3.1.6
      '@smithy/shared-ini-file-loader': 3.1.7
      '@smithy/types': 3.4.2
      tslib: 2.7.0
    transitivePeerDependencies:
      - '@aws-sdk/client-sso-oidc'
      - aws-crt

  '@aws-sdk/credential-provider-node@3.651.1(@aws-sdk/client-sso-oidc@3.651.1(@aws-sdk/client-sts@3.651.1))(@aws-sdk/client-sts@3.651.1)':
    dependencies:
      '@aws-sdk/credential-provider-env': 3.649.0
      '@aws-sdk/credential-provider-http': 3.649.0
      '@aws-sdk/credential-provider-ini': 3.651.1(@aws-sdk/client-sso-oidc@3.651.1(@aws-sdk/client-sts@3.651.1))(@aws-sdk/client-sts@3.651.1)
      '@aws-sdk/credential-provider-process': 3.649.0
      '@aws-sdk/credential-provider-sso': 3.651.1(@aws-sdk/client-sso-oidc@3.651.1(@aws-sdk/client-sts@3.651.1))
      '@aws-sdk/credential-provider-web-identity': 3.649.0(@aws-sdk/client-sts@3.651.1)
      '@aws-sdk/types': 3.649.0
      '@smithy/credential-provider-imds': 3.2.3
      '@smithy/property-provider': 3.1.6
      '@smithy/shared-ini-file-loader': 3.1.7
      '@smithy/types': 3.4.2
      tslib: 2.7.0
    transitivePeerDependencies:
      - '@aws-sdk/client-sso-oidc'
      - '@aws-sdk/client-sts'
      - aws-crt

  '@aws-sdk/credential-provider-process@3.649.0':
    dependencies:
      '@aws-sdk/types': 3.649.0
      '@smithy/property-provider': 3.1.6
      '@smithy/shared-ini-file-loader': 3.1.7
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@aws-sdk/credential-provider-sso@3.651.1(@aws-sdk/client-sso-oidc@3.651.1(@aws-sdk/client-sts@3.651.1))':
    dependencies:
      '@aws-sdk/client-sso': 3.651.1
      '@aws-sdk/token-providers': 3.649.0(@aws-sdk/client-sso-oidc@3.651.1(@aws-sdk/client-sts@3.651.1))
      '@aws-sdk/types': 3.649.0
      '@smithy/property-provider': 3.1.6
      '@smithy/shared-ini-file-loader': 3.1.7
      '@smithy/types': 3.4.2
      tslib: 2.7.0
    transitivePeerDependencies:
      - '@aws-sdk/client-sso-oidc'
      - aws-crt

  '@aws-sdk/credential-provider-web-identity@3.649.0(@aws-sdk/client-sts@3.651.1)':
    dependencies:
      '@aws-sdk/client-sts': 3.651.1
      '@aws-sdk/types': 3.649.0
      '@smithy/property-provider': 3.1.6
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@aws-sdk/middleware-bucket-endpoint@3.649.0':
    dependencies:
      '@aws-sdk/types': 3.649.0
      '@aws-sdk/util-arn-parser': 3.568.0
      '@smithy/node-config-provider': 3.1.7
      '@smithy/protocol-http': 4.1.3
      '@smithy/types': 3.4.2
      '@smithy/util-config-provider': 3.0.0
      tslib: 2.7.0

  '@aws-sdk/middleware-expect-continue@3.649.0':
    dependencies:
      '@aws-sdk/types': 3.649.0
      '@smithy/protocol-http': 4.1.3
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@aws-sdk/middleware-flexible-checksums@3.651.1':
    dependencies:
      '@aws-crypto/crc32': 5.2.0
      '@aws-crypto/crc32c': 5.2.0
      '@aws-sdk/types': 3.649.0
      '@smithy/is-array-buffer': 3.0.0
      '@smithy/node-config-provider': 3.1.7
      '@smithy/protocol-http': 4.1.3
      '@smithy/types': 3.4.2
      '@smithy/util-middleware': 3.0.6
      '@smithy/util-utf8': 3.0.0
      tslib: 2.7.0

  '@aws-sdk/middleware-host-header@3.649.0':
    dependencies:
      '@aws-sdk/types': 3.649.0
      '@smithy/protocol-http': 4.1.3
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@aws-sdk/middleware-location-constraint@3.649.0':
    dependencies:
      '@aws-sdk/types': 3.649.0
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@aws-sdk/middleware-logger@3.649.0':
    dependencies:
      '@aws-sdk/types': 3.649.0
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@aws-sdk/middleware-recursion-detection@3.649.0':
    dependencies:
      '@aws-sdk/types': 3.649.0
      '@smithy/protocol-http': 4.1.3
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@aws-sdk/middleware-sdk-s3@3.651.1':
    dependencies:
      '@aws-sdk/core': 3.651.1
      '@aws-sdk/types': 3.649.0
      '@aws-sdk/util-arn-parser': 3.568.0
      '@smithy/core': 2.4.3
      '@smithy/node-config-provider': 3.1.7
      '@smithy/protocol-http': 4.1.3
      '@smithy/signature-v4': 4.1.3
      '@smithy/smithy-client': 3.3.2
      '@smithy/types': 3.4.2
      '@smithy/util-config-provider': 3.0.0
      '@smithy/util-middleware': 3.0.6
      '@smithy/util-stream': 3.1.6
      '@smithy/util-utf8': 3.0.0
      tslib: 2.7.0

  '@aws-sdk/middleware-ssec@3.649.0':
    dependencies:
      '@aws-sdk/types': 3.649.0
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@aws-sdk/middleware-user-agent@3.649.0':
    dependencies:
      '@aws-sdk/types': 3.649.0
      '@aws-sdk/util-endpoints': 3.649.0
      '@smithy/protocol-http': 4.1.3
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@aws-sdk/region-config-resolver@3.649.0':
    dependencies:
      '@aws-sdk/types': 3.649.0
      '@smithy/node-config-provider': 3.1.7
      '@smithy/types': 3.4.2
      '@smithy/util-config-provider': 3.0.0
      '@smithy/util-middleware': 3.0.6
      tslib: 2.7.0

  '@aws-sdk/s3-request-presigner@3.651.1':
    dependencies:
      '@aws-sdk/signature-v4-multi-region': 3.651.1
      '@aws-sdk/types': 3.649.0
      '@aws-sdk/util-format-url': 3.649.0
      '@smithy/middleware-endpoint': 3.1.3
      '@smithy/protocol-http': 4.1.3
      '@smithy/smithy-client': 3.3.2
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@aws-sdk/signature-v4-multi-region@3.651.1':
    dependencies:
      '@aws-sdk/middleware-sdk-s3': 3.651.1
      '@aws-sdk/types': 3.649.0
      '@smithy/protocol-http': 4.1.3
      '@smithy/signature-v4': 4.1.3
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@aws-sdk/token-providers@3.649.0(@aws-sdk/client-sso-oidc@3.651.1(@aws-sdk/client-sts@3.651.1))':
    dependencies:
      '@aws-sdk/client-sso-oidc': 3.651.1(@aws-sdk/client-sts@3.651.1)
      '@aws-sdk/types': 3.649.0
      '@smithy/property-provider': 3.1.6
      '@smithy/shared-ini-file-loader': 3.1.7
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@aws-sdk/types@3.649.0':
    dependencies:
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@aws-sdk/util-arn-parser@3.568.0':
    dependencies:
      tslib: 2.7.0

  '@aws-sdk/util-endpoints@3.649.0':
    dependencies:
      '@aws-sdk/types': 3.649.0
      '@smithy/types': 3.4.2
      '@smithy/util-endpoints': 2.1.2
      tslib: 2.7.0

  '@aws-sdk/util-format-url@3.649.0':
    dependencies:
      '@aws-sdk/types': 3.649.0
      '@smithy/querystring-builder': 3.0.6
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@aws-sdk/util-locate-window@3.568.0':
    dependencies:
      tslib: 2.7.0

  '@aws-sdk/util-user-agent-browser@3.649.0':
    dependencies:
      '@aws-sdk/types': 3.649.0
      '@smithy/types': 3.4.2
      bowser: 2.11.0
      tslib: 2.7.0

  '@aws-sdk/util-user-agent-node@3.649.0':
    dependencies:
      '@aws-sdk/types': 3.649.0
      '@smithy/node-config-provider': 3.1.7
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@aws-sdk/xml-builder@3.649.0':
    dependencies:
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@babel/code-frame@7.24.7':
    dependencies:
      '@babel/highlight': 7.24.7
      picocolors: 1.1.0

  '@babel/generator@7.25.6':
    dependencies:
      '@babel/types': 7.25.6
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 2.5.2

  '@babel/helper-module-imports@7.24.7':
    dependencies:
      '@babel/traverse': 7.25.6
      '@babel/types': 7.25.6
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-string-parser@7.24.8': {}

  '@babel/helper-validator-identifier@7.24.7': {}

  '@babel/highlight@7.24.7':
    dependencies:
      '@babel/helper-validator-identifier': 7.24.7
      chalk: 2.4.2
      js-tokens: 4.0.0
      picocolors: 1.1.0

  '@babel/parser@7.25.6':
    dependencies:
      '@babel/types': 7.25.6

  '@babel/runtime@7.25.6':
    dependencies:
      regenerator-runtime: 0.14.1

  '@babel/template@7.25.0':
    dependencies:
      '@babel/code-frame': 7.24.7
      '@babel/parser': 7.25.6
      '@babel/types': 7.25.6

  '@babel/traverse@7.25.6':
    dependencies:
      '@babel/code-frame': 7.24.7
      '@babel/generator': 7.25.6
      '@babel/parser': 7.25.6
      '@babel/template': 7.25.0
      '@babel/types': 7.25.6
      debug: 4.3.7
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.25.6':
    dependencies:
      '@babel/helper-string-parser': 7.24.8
      '@babel/helper-validator-identifier': 7.24.7
      to-fast-properties: 2.0.0

  '@chakra-ui/accordion@2.3.1(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(framer-motion@10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/descendant': 3.1.0(react@18.3.1)
      '@chakra-ui/icon': 3.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/react-context': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-controllable-state': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-merge-refs': 2.1.0(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      '@chakra-ui/transition': 2.1.0(framer-motion@10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1)
      framer-motion: 10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/alert@2.2.2(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/icon': 3.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/react-context': 2.1.0(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/spinner': 2.1.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/anatomy@2.2.2': {}

  '@chakra-ui/avatar@2.3.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/image': 2.1.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/react-children-utils': 2.0.6(react@18.3.1)
      '@chakra-ui/react-context': 2.1.0(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/breadcrumb@2.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/react-children-utils': 2.0.6(react@18.3.1)
      '@chakra-ui/react-context': 2.1.0(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/breakpoint-utils@2.0.8':
    dependencies:
      '@chakra-ui/shared-utils': 2.0.5

  '@chakra-ui/button@2.1.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/react-context': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-merge-refs': 2.1.0(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/spinner': 2.1.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/card@2.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/checkbox@2.3.2(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/form-control': 2.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/react-context': 2.1.0(react@18.3.1)
      '@chakra-ui/react-types': 2.0.7(react@18.3.1)
      '@chakra-ui/react-use-callback-ref': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-controllable-state': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-merge-refs': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-safe-layout-effect': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-update-effect': 2.1.0(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      '@chakra-ui/visually-hidden': 2.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@zag-js/focus-visible': 0.16.0
      react: 18.3.1

  '@chakra-ui/clickable@2.1.0(react@18.3.1)':
    dependencies:
      '@chakra-ui/react-use-merge-refs': 2.1.0(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      react: 18.3.1

  '@chakra-ui/close-button@2.1.1(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/icon': 3.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/color-mode@2.2.0(react@18.3.1)':
    dependencies:
      '@chakra-ui/react-use-safe-layout-effect': 2.1.0(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/control-box@2.1.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/counter@2.1.0(react@18.3.1)':
    dependencies:
      '@chakra-ui/number-utils': 2.0.7
      '@chakra-ui/react-use-callback-ref': 2.1.0(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      react: 18.3.1

  '@chakra-ui/css-reset@2.3.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@emotion/react': 11.13.3(@types/react@18.3.5)(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/descendant@3.1.0(react@18.3.1)':
    dependencies:
      '@chakra-ui/react-context': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-merge-refs': 2.1.0(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/dom-utils@2.1.0': {}

  '@chakra-ui/editable@3.1.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/react-context': 2.1.0(react@18.3.1)
      '@chakra-ui/react-types': 2.0.7(react@18.3.1)
      '@chakra-ui/react-use-callback-ref': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-controllable-state': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-focus-on-pointer-down': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-merge-refs': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-safe-layout-effect': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-update-effect': 2.1.0(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/event-utils@2.0.8': {}

  '@chakra-ui/focus-lock@2.1.0(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      '@chakra-ui/dom-utils': 2.1.0
      react: 18.3.1
      react-focus-lock: 2.13.2(@types/react@18.3.5)(react@18.3.1)
    transitivePeerDependencies:
      - '@types/react'

  '@chakra-ui/form-control@2.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/icon': 3.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/react-context': 2.1.0(react@18.3.1)
      '@chakra-ui/react-types': 2.0.7(react@18.3.1)
      '@chakra-ui/react-use-merge-refs': 2.1.0(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/hooks@2.2.1(react@18.3.1)':
    dependencies:
      '@chakra-ui/react-utils': 2.0.12(react@18.3.1)
      '@chakra-ui/utils': 2.0.15
      compute-scroll-into-view: 3.0.3
      copy-to-clipboard: 3.3.3
      react: 18.3.1

  '@chakra-ui/icon@3.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/image@2.1.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/react-use-safe-layout-effect': 2.1.0(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/input@2.1.2(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/form-control': 2.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/object-utils': 2.1.0
      '@chakra-ui/react-children-utils': 2.0.6(react@18.3.1)
      '@chakra-ui/react-context': 2.1.0(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/layout@2.3.1(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/breakpoint-utils': 2.0.8
      '@chakra-ui/icon': 3.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/object-utils': 2.1.0
      '@chakra-ui/react-children-utils': 2.0.6(react@18.3.1)
      '@chakra-ui/react-context': 2.1.0(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/lazy-utils@2.0.5': {}

  '@chakra-ui/live-region@2.1.0(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@chakra-ui/media-query@3.3.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/breakpoint-utils': 2.0.8
      '@chakra-ui/react-env': 3.1.0(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/menu@2.2.1(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(framer-motion@10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/clickable': 2.1.0(react@18.3.1)
      '@chakra-ui/descendant': 3.1.0(react@18.3.1)
      '@chakra-ui/lazy-utils': 2.0.5
      '@chakra-ui/popper': 3.1.0(react@18.3.1)
      '@chakra-ui/react-children-utils': 2.0.6(react@18.3.1)
      '@chakra-ui/react-context': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-animation-state': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-controllable-state': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-disclosure': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-focus-effect': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-merge-refs': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-outside-click': 2.2.0(react@18.3.1)
      '@chakra-ui/react-use-update-effect': 2.1.0(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      '@chakra-ui/transition': 2.1.0(framer-motion@10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1)
      framer-motion: 10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/modal@2.3.1(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(@types/react@18.3.5)(framer-motion@10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/close-button': 2.1.1(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/focus-lock': 2.1.0(@types/react@18.3.5)(react@18.3.1)
      '@chakra-ui/portal': 2.1.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@chakra-ui/react-context': 2.1.0(react@18.3.1)
      '@chakra-ui/react-types': 2.0.7(react@18.3.1)
      '@chakra-ui/react-use-merge-refs': 2.1.0(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      '@chakra-ui/transition': 2.1.0(framer-motion@10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1)
      aria-hidden: 1.2.4
      framer-motion: 10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-remove-scroll: 2.6.0(@types/react@18.3.5)(react@18.3.1)
    transitivePeerDependencies:
      - '@types/react'

  '@chakra-ui/next-js@2.2.0(@chakra-ui/react@2.8.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(framer-motion@10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(next@15.4.0-canary.61(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(sass@1.78.0))(react@18.3.1)':
    dependencies:
      '@chakra-ui/react': 2.8.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(framer-motion@10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@emotion/cache': 11.13.1
      '@emotion/react': 11.13.3(@types/react@18.3.5)(react@18.3.1)
      next: 15.4.0-canary.61(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(sass@1.78.0)
      react: 18.3.1

  '@chakra-ui/number-input@2.1.2(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/counter': 2.1.0(react@18.3.1)
      '@chakra-ui/form-control': 2.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/icon': 3.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/react-context': 2.1.0(react@18.3.1)
      '@chakra-ui/react-types': 2.0.7(react@18.3.1)
      '@chakra-ui/react-use-callback-ref': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-event-listener': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-interval': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-merge-refs': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-safe-layout-effect': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-update-effect': 2.1.0(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/number-utils@2.0.7': {}

  '@chakra-ui/object-utils@2.1.0': {}

  '@chakra-ui/pin-input@2.1.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/descendant': 3.1.0(react@18.3.1)
      '@chakra-ui/react-children-utils': 2.0.6(react@18.3.1)
      '@chakra-ui/react-context': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-controllable-state': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-merge-refs': 2.1.0(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/popover@2.2.1(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(framer-motion@10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/close-button': 2.1.1(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/lazy-utils': 2.0.5
      '@chakra-ui/popper': 3.1.0(react@18.3.1)
      '@chakra-ui/react-context': 2.1.0(react@18.3.1)
      '@chakra-ui/react-types': 2.0.7(react@18.3.1)
      '@chakra-ui/react-use-animation-state': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-disclosure': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-focus-effect': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-focus-on-pointer-down': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-merge-refs': 2.1.0(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      framer-motion: 10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/popper@3.1.0(react@18.3.1)':
    dependencies:
      '@chakra-ui/react-types': 2.0.7(react@18.3.1)
      '@chakra-ui/react-use-merge-refs': 2.1.0(react@18.3.1)
      '@popperjs/core': 2.11.8
      react: 18.3.1

  '@chakra-ui/portal@2.1.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/react-context': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-safe-layout-effect': 2.1.0(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@chakra-ui/progress@2.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/react-context': 2.1.0(react@18.3.1)
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/provider@2.4.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/css-reset': 2.3.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      '@chakra-ui/portal': 2.1.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@chakra-ui/react-env': 3.1.0(react@18.3.1)
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      '@chakra-ui/utils': 2.0.15
      '@emotion/react': 11.13.3(@types/react@18.3.5)(react@18.3.1)
      '@emotion/styled': 11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@chakra-ui/radio@2.1.2(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/form-control': 2.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/react-context': 2.1.0(react@18.3.1)
      '@chakra-ui/react-types': 2.0.7(react@18.3.1)
      '@chakra-ui/react-use-merge-refs': 2.1.0(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      '@zag-js/focus-visible': 0.16.0
      react: 18.3.1

  '@chakra-ui/react-children-utils@2.0.6(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@chakra-ui/react-context@2.1.0(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@chakra-ui/react-env@3.1.0(react@18.3.1)':
    dependencies:
      '@chakra-ui/react-use-safe-layout-effect': 2.1.0(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/react-types@2.0.7(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@chakra-ui/react-use-animation-state@2.1.0(react@18.3.1)':
    dependencies:
      '@chakra-ui/dom-utils': 2.1.0
      '@chakra-ui/react-use-event-listener': 2.1.0(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/react-use-callback-ref@2.1.0(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@chakra-ui/react-use-controllable-state@2.1.0(react@18.3.1)':
    dependencies:
      '@chakra-ui/react-use-callback-ref': 2.1.0(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/react-use-disclosure@2.1.0(react@18.3.1)':
    dependencies:
      '@chakra-ui/react-use-callback-ref': 2.1.0(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/react-use-event-listener@2.1.0(react@18.3.1)':
    dependencies:
      '@chakra-ui/react-use-callback-ref': 2.1.0(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/react-use-focus-effect@2.1.0(react@18.3.1)':
    dependencies:
      '@chakra-ui/dom-utils': 2.1.0
      '@chakra-ui/react-use-event-listener': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-safe-layout-effect': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-update-effect': 2.1.0(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/react-use-focus-on-pointer-down@2.1.0(react@18.3.1)':
    dependencies:
      '@chakra-ui/react-use-event-listener': 2.1.0(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/react-use-interval@2.1.0(react@18.3.1)':
    dependencies:
      '@chakra-ui/react-use-callback-ref': 2.1.0(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/react-use-latest-ref@2.1.0(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@chakra-ui/react-use-merge-refs@2.1.0(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@chakra-ui/react-use-outside-click@2.2.0(react@18.3.1)':
    dependencies:
      '@chakra-ui/react-use-callback-ref': 2.1.0(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/react-use-pan-event@2.1.0(react@18.3.1)':
    dependencies:
      '@chakra-ui/event-utils': 2.0.8
      '@chakra-ui/react-use-latest-ref': 2.1.0(react@18.3.1)
      framesync: 6.1.2
      react: 18.3.1

  '@chakra-ui/react-use-previous@2.1.0(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@chakra-ui/react-use-safe-layout-effect@2.1.0(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@chakra-ui/react-use-size@2.1.0(react@18.3.1)':
    dependencies:
      '@zag-js/element-size': 0.10.5
      react: 18.3.1

  '@chakra-ui/react-use-timeout@2.1.0(react@18.3.1)':
    dependencies:
      '@chakra-ui/react-use-callback-ref': 2.1.0(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/react-use-update-effect@2.1.0(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@chakra-ui/react-utils@2.0.12(react@18.3.1)':
    dependencies:
      '@chakra-ui/utils': 2.0.15
      react: 18.3.1

  '@chakra-ui/react@2.8.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(framer-motion@10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/accordion': 2.3.1(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(framer-motion@10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/alert': 2.2.2(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/avatar': 2.3.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/breadcrumb': 2.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/button': 2.1.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/card': 2.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/checkbox': 2.3.2(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/close-button': 2.1.1(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/control-box': 2.1.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/counter': 2.1.0(react@18.3.1)
      '@chakra-ui/css-reset': 2.3.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      '@chakra-ui/editable': 3.1.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/focus-lock': 2.1.0(@types/react@18.3.5)(react@18.3.1)
      '@chakra-ui/form-control': 2.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/hooks': 2.2.1(react@18.3.1)
      '@chakra-ui/icon': 3.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/image': 2.1.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/input': 2.1.2(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/layout': 2.3.1(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/live-region': 2.1.0(react@18.3.1)
      '@chakra-ui/media-query': 3.3.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/menu': 2.2.1(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(framer-motion@10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/modal': 2.3.1(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(@types/react@18.3.5)(framer-motion@10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@chakra-ui/number-input': 2.1.2(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/pin-input': 2.1.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/popover': 2.2.1(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(framer-motion@10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/popper': 3.1.0(react@18.3.1)
      '@chakra-ui/portal': 2.1.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@chakra-ui/progress': 2.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/provider': 2.4.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@chakra-ui/radio': 2.1.2(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/react-env': 3.1.0(react@18.3.1)
      '@chakra-ui/select': 2.1.2(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/skeleton': 2.1.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/skip-nav': 2.1.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/slider': 2.1.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/spinner': 2.1.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/stat': 2.1.1(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/stepper': 2.3.1(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/styled-system': 2.9.2
      '@chakra-ui/switch': 2.1.2(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(framer-motion@10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      '@chakra-ui/table': 2.1.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/tabs': 3.0.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/tag': 3.1.1(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/textarea': 2.1.2(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/theme': 3.3.1(@chakra-ui/styled-system@2.9.2)
      '@chakra-ui/theme-utils': 2.0.21
      '@chakra-ui/toast': 7.0.2(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(framer-motion@10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@chakra-ui/tooltip': 2.3.1(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(framer-motion@10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@chakra-ui/transition': 2.1.0(framer-motion@10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/utils': 2.0.15
      '@chakra-ui/visually-hidden': 2.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@emotion/react': 11.13.3(@types/react@18.3.5)(react@18.3.1)
      '@emotion/styled': 11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1)
      framer-motion: 10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - '@types/react'

  '@chakra-ui/select@2.1.2(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/form-control': 2.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/shared-utils@2.0.5': {}

  '@chakra-ui/skeleton@2.1.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/media-query': 3.3.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/react-use-previous': 2.1.0(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/skip-nav@2.1.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/slider@2.1.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/number-utils': 2.0.7
      '@chakra-ui/react-context': 2.1.0(react@18.3.1)
      '@chakra-ui/react-types': 2.0.7(react@18.3.1)
      '@chakra-ui/react-use-callback-ref': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-controllable-state': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-latest-ref': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-merge-refs': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-pan-event': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-size': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-update-effect': 2.1.0(react@18.3.1)
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/spinner@2.1.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/stat@2.1.1(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/icon': 3.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/react-context': 2.1.0(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/stepper@2.3.1(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/icon': 3.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/react-context': 2.1.0(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/styled-system@2.9.2':
    dependencies:
      '@chakra-ui/shared-utils': 2.0.5
      csstype: 3.1.3
      lodash.mergewith: 4.6.2

  '@chakra-ui/switch@2.1.2(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(framer-motion@10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/checkbox': 2.3.2(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      framer-motion: 10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/color-mode': 2.2.0(react@18.3.1)
      '@chakra-ui/object-utils': 2.1.0
      '@chakra-ui/react-utils': 2.0.12(react@18.3.1)
      '@chakra-ui/styled-system': 2.9.2
      '@chakra-ui/theme-utils': 2.0.21
      '@chakra-ui/utils': 2.0.15
      '@emotion/react': 11.13.3(@types/react@18.3.5)(react@18.3.1)
      '@emotion/styled': 11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1)
      react: 18.3.1
      react-fast-compare: 3.2.2

  '@chakra-ui/table@2.1.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/react-context': 2.1.0(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/tabs@3.0.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/clickable': 2.1.0(react@18.3.1)
      '@chakra-ui/descendant': 3.1.0(react@18.3.1)
      '@chakra-ui/lazy-utils': 2.0.5
      '@chakra-ui/react-children-utils': 2.0.6(react@18.3.1)
      '@chakra-ui/react-context': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-controllable-state': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-merge-refs': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-safe-layout-effect': 2.1.0(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/tag@3.1.1(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/icon': 3.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/react-context': 2.1.0(react@18.3.1)
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/textarea@2.1.2(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/form-control': 2.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/theme-tools@2.1.2(@chakra-ui/styled-system@2.9.2)':
    dependencies:
      '@chakra-ui/anatomy': 2.2.2
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/styled-system': 2.9.2
      color2k: 2.0.3

  '@chakra-ui/theme-utils@2.0.21':
    dependencies:
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/styled-system': 2.9.2
      '@chakra-ui/theme': 3.3.1(@chakra-ui/styled-system@2.9.2)
      lodash.mergewith: 4.6.2

  '@chakra-ui/theme@3.3.1(@chakra-ui/styled-system@2.9.2)':
    dependencies:
      '@chakra-ui/anatomy': 2.2.2
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/styled-system': 2.9.2
      '@chakra-ui/theme-tools': 2.1.2(@chakra-ui/styled-system@2.9.2)

  '@chakra-ui/toast@7.0.2(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(framer-motion@10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/alert': 2.2.2(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/close-button': 2.1.1(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)
      '@chakra-ui/portal': 2.1.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@chakra-ui/react-context': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-timeout': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-update-effect': 2.1.0(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/styled-system': 2.9.2
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      '@chakra-ui/theme': 3.3.1(@chakra-ui/styled-system@2.9.2)
      framer-motion: 10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@chakra-ui/tooltip@2.3.1(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(framer-motion@10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/dom-utils': 2.1.0
      '@chakra-ui/popper': 3.1.0(react@18.3.1)
      '@chakra-ui/portal': 2.1.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@chakra-ui/react-types': 2.0.7(react@18.3.1)
      '@chakra-ui/react-use-disclosure': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-event-listener': 2.1.0(react@18.3.1)
      '@chakra-ui/react-use-merge-refs': 2.1.0(react@18.3.1)
      '@chakra-ui/shared-utils': 2.0.5
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      framer-motion: 10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@chakra-ui/transition@2.1.0(framer-motion@10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/shared-utils': 2.0.5
      framer-motion: 10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@chakra-ui/utils@2.0.15':
    dependencies:
      '@types/lodash.mergewith': 4.6.7
      css-box-model: 1.2.1
      framesync: 6.1.2
      lodash.mergewith: 4.6.2

  '@chakra-ui/visually-hidden@2.2.0(@chakra-ui/system@2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@chakra-ui/system': 2.6.2(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      react: 18.3.1

  '@clerk/backend@0.38.13(react@18.3.1)':
    dependencies:
      '@clerk/shared': 1.4.1(react@18.3.1)
      '@clerk/types': 3.65.3
      '@peculiar/webcrypto': 1.4.1
      '@types/node': 16.18.6
      cookie: 0.5.0
      deepmerge: 4.2.2
      node-fetch-native: 1.0.1
      snakecase-keys: 5.4.4
      tslib: 2.4.1
    transitivePeerDependencies:
      - react

  '@clerk/clerk-react@4.32.3(react@18.3.1)':
    dependencies:
      '@clerk/shared': 1.4.1(react@18.3.1)
      '@clerk/types': 3.65.3
      react: 18.3.1
      tslib: 2.4.1

  '@clerk/clerk-sdk-node@4.13.21(react@18.3.1)':
    dependencies:
      '@clerk/backend': 0.38.13(react@18.3.1)
      '@clerk/shared': 1.4.1(react@18.3.1)
      '@clerk/types': 3.65.3
      '@types/cookies': 0.7.7
      '@types/express': 4.17.14
      '@types/node-fetch': 2.6.2
      camelcase-keys: 6.2.2
      snakecase-keys: 3.2.1
      tslib: 2.4.1
    transitivePeerDependencies:
      - react

  '@clerk/nextjs@4.31.5(next@15.4.0-canary.61(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(sass@1.78.0))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@clerk/backend': 0.38.13(react@18.3.1)
      '@clerk/clerk-react': 4.32.3(react@18.3.1)
      '@clerk/clerk-sdk-node': 4.13.21(react@18.3.1)
      '@clerk/shared': 1.4.1(react@18.3.1)
      '@clerk/types': 3.65.3
      next: 15.4.0-canary.61(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(sass@1.78.0)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      tslib: 2.4.1

  '@clerk/shared@1.4.1(react@18.3.1)':
    dependencies:
      glob-to-regexp: 0.4.1
      js-cookie: 3.0.1
      swr: 2.2.0(react@18.3.1)
    optionalDependencies:
      react: 18.3.1

  '@clerk/types@3.65.3':
    dependencies:
      csstype: 3.1.1

  '@clickhouse/client-common@1.6.0': {}

  '@clickhouse/client@1.6.0':
    dependencies:
      '@clickhouse/client-common': 1.6.0

  '@emnapi/runtime@1.4.3':
    dependencies:
      tslib: 2.7.0
    optional: true

  '@emotion/babel-plugin@11.12.0':
    dependencies:
      '@babel/helper-module-imports': 7.24.7
      '@babel/runtime': 7.25.6
      '@emotion/hash': 0.9.2
      '@emotion/memoize': 0.9.0
      '@emotion/serialize': 1.3.1
      babel-plugin-macros: 3.1.0
      convert-source-map: 1.9.0
      escape-string-regexp: 4.0.0
      find-root: 1.1.0
      source-map: 0.5.7
      stylis: 4.2.0
    transitivePeerDependencies:
      - supports-color

  '@emotion/cache@11.13.1':
    dependencies:
      '@emotion/memoize': 0.9.0
      '@emotion/sheet': 1.4.0
      '@emotion/utils': 1.4.0
      '@emotion/weak-memoize': 0.4.0
      stylis: 4.2.0

  '@emotion/hash@0.9.2': {}

  '@emotion/is-prop-valid@0.8.8':
    dependencies:
      '@emotion/memoize': 0.7.4
    optional: true

  '@emotion/is-prop-valid@1.2.2':
    dependencies:
      '@emotion/memoize': 0.8.1

  '@emotion/is-prop-valid@1.3.0':
    dependencies:
      '@emotion/memoize': 0.9.0

  '@emotion/memoize@0.7.4':
    optional: true

  '@emotion/memoize@0.8.1': {}

  '@emotion/memoize@0.9.0': {}

  '@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      '@emotion/babel-plugin': 11.12.0
      '@emotion/cache': 11.13.1
      '@emotion/serialize': 1.3.1
      '@emotion/use-insertion-effect-with-fallbacks': 1.1.0(react@18.3.1)
      '@emotion/utils': 1.4.0
      '@emotion/weak-memoize': 0.4.0
      hoist-non-react-statics: 3.3.2
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5
    transitivePeerDependencies:
      - supports-color

  '@emotion/serialize@1.3.1':
    dependencies:
      '@emotion/hash': 0.9.2
      '@emotion/memoize': 0.9.0
      '@emotion/unitless': 0.10.0
      '@emotion/utils': 1.4.0
      csstype: 3.1.3

  '@emotion/sheet@1.4.0': {}

  '@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      '@emotion/babel-plugin': 11.12.0
      '@emotion/is-prop-valid': 1.3.0
      '@emotion/react': 11.13.3(@types/react@18.3.5)(react@18.3.1)
      '@emotion/serialize': 1.3.1
      '@emotion/use-insertion-effect-with-fallbacks': 1.1.0(react@18.3.1)
      '@emotion/utils': 1.4.0
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5
    transitivePeerDependencies:
      - supports-color

  '@emotion/unitless@0.10.0': {}

  '@emotion/unitless@0.8.1': {}

  '@emotion/use-insertion-effect-with-fallbacks@1.1.0(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@emotion/utils@1.4.0': {}

  '@emotion/weak-memoize@0.4.0': {}

  '@eslint-community/eslint-utils@4.4.0(eslint@8.57.0)':
    dependencies:
      eslint: 8.57.0
      eslint-visitor-keys: 3.4.3

  '@eslint-community/regexpp@4.11.1': {}

  '@eslint/eslintrc@2.1.4':
    dependencies:
      ajv: 6.12.6
      debug: 4.3.7
      espree: 9.6.1
      globals: 13.24.0
      ignore: 5.3.2
      import-fresh: 3.3.0
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  '@eslint/js@8.57.0': {}

  '@floating-ui/core@1.6.8':
    dependencies:
      '@floating-ui/utils': 0.2.8

  '@floating-ui/dom@1.6.11':
    dependencies:
      '@floating-ui/core': 1.6.8
      '@floating-ui/utils': 0.2.8

  '@floating-ui/react-dom@1.3.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@floating-ui/dom': 1.6.11
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@floating-ui/react-dom@2.1.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@floating-ui/dom': 1.6.11
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@floating-ui/react@0.19.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@floating-ui/react-dom': 1.3.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      aria-hidden: 1.2.4
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      tabbable: 6.2.0

  '@floating-ui/utils@0.2.8': {}

  '@fortawesome/fontawesome-common-types@6.6.0': {}

  '@fortawesome/fontawesome-svg-core@6.6.0':
    dependencies:
      '@fortawesome/fontawesome-common-types': 6.6.0

  '@fortawesome/free-brands-svg-icons@6.6.0':
    dependencies:
      '@fortawesome/fontawesome-common-types': 6.6.0

  '@fortawesome/free-solid-svg-icons@6.6.0':
    dependencies:
      '@fortawesome/fontawesome-common-types': 6.6.0

  '@fortawesome/react-fontawesome@0.2.2(@fortawesome/fontawesome-svg-core@6.6.0)(react@18.3.1)':
    dependencies:
      '@fortawesome/fontawesome-svg-core': 6.6.0
      prop-types: 15.8.1
      react: 18.3.1

  '@headlessui/react@1.7.19(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@tanstack/react-virtual': 3.10.7(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      client-only: 0.0.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@headlessui/tailwindcss@0.2.1(tailwindcss@3.4.11)':
    dependencies:
      tailwindcss: 3.4.11

  '@heroicons/react@1.0.6(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@humanwhocodes/config-array@0.11.14':
    dependencies:
      '@humanwhocodes/object-schema': 2.0.3
      debug: 4.3.7
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  '@humanwhocodes/module-importer@1.0.1': {}

  '@humanwhocodes/object-schema@2.0.3': {}

  '@img/sharp-darwin-arm64@0.34.2':
    optionalDependencies:
      '@img/sharp-libvips-darwin-arm64': 1.1.0
    optional: true

  '@img/sharp-darwin-x64@0.34.2':
    optionalDependencies:
      '@img/sharp-libvips-darwin-x64': 1.1.0
    optional: true

  '@img/sharp-libvips-darwin-arm64@1.1.0':
    optional: true

  '@img/sharp-libvips-darwin-x64@1.1.0':
    optional: true

  '@img/sharp-libvips-linux-arm64@1.1.0':
    optional: true

  '@img/sharp-libvips-linux-arm@1.1.0':
    optional: true

  '@img/sharp-libvips-linux-ppc64@1.1.0':
    optional: true

  '@img/sharp-libvips-linux-s390x@1.1.0':
    optional: true

  '@img/sharp-libvips-linux-x64@1.1.0':
    optional: true

  '@img/sharp-libvips-linuxmusl-arm64@1.1.0':
    optional: true

  '@img/sharp-libvips-linuxmusl-x64@1.1.0':
    optional: true

  '@img/sharp-linux-arm64@0.34.2':
    optionalDependencies:
      '@img/sharp-libvips-linux-arm64': 1.1.0
    optional: true

  '@img/sharp-linux-arm@0.34.2':
    optionalDependencies:
      '@img/sharp-libvips-linux-arm': 1.1.0
    optional: true

  '@img/sharp-linux-s390x@0.34.2':
    optionalDependencies:
      '@img/sharp-libvips-linux-s390x': 1.1.0
    optional: true

  '@img/sharp-linux-x64@0.34.2':
    optionalDependencies:
      '@img/sharp-libvips-linux-x64': 1.1.0
    optional: true

  '@img/sharp-linuxmusl-arm64@0.34.2':
    optionalDependencies:
      '@img/sharp-libvips-linuxmusl-arm64': 1.1.0
    optional: true

  '@img/sharp-linuxmusl-x64@0.34.2':
    optionalDependencies:
      '@img/sharp-libvips-linuxmusl-x64': 1.1.0
    optional: true

  '@img/sharp-wasm32@0.34.2':
    dependencies:
      '@emnapi/runtime': 1.4.3
    optional: true

  '@img/sharp-win32-arm64@0.34.2':
    optional: true

  '@img/sharp-win32-ia32@0.34.2':
    optional: true

  '@img/sharp-win32-x64@0.34.2':
    optional: true

  '@isaacs/cliui@8.0.2':
    dependencies:
      string-width: 5.1.2
      string-width-cjs: string-width@4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: strip-ansi@6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: wrap-ansi@7.0.0

  '@jridgewell/gen-mapping@0.3.5':
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.5.0
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/set-array@1.2.1': {}

  '@jridgewell/sourcemap-codec@1.5.0': {}

  '@jridgewell/trace-mapping@0.3.25':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0

  '@kurkle/color@0.3.2': {}

  '@mui/core-downloads-tracker@5.16.7': {}

  '@mui/material@5.16.7(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      '@mui/core-downloads-tracker': 5.16.7
      '@mui/system': 5.16.7(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1)
      '@mui/types': 7.2.16(@types/react@18.3.5)
      '@mui/utils': 5.16.6(@types/react@18.3.5)(react@18.3.1)
      '@popperjs/core': 2.11.8
      '@types/react-transition-group': 4.4.11
      clsx: 2.1.1
      csstype: 3.1.3
      prop-types: 15.8.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-is: 18.3.1
      react-transition-group: 4.4.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
    optionalDependencies:
      '@emotion/react': 11.13.3(@types/react@18.3.5)(react@18.3.1)
      '@emotion/styled': 11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1)
      '@types/react': 18.3.5

  '@mui/private-theming@5.16.6(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      '@mui/utils': 5.16.6(@types/react@18.3.5)(react@18.3.1)
      prop-types: 15.8.1
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  '@mui/styled-engine@5.16.6(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      '@emotion/cache': 11.13.1
      csstype: 3.1.3
      prop-types: 15.8.1
      react: 18.3.1
    optionalDependencies:
      '@emotion/react': 11.13.3(@types/react@18.3.5)(react@18.3.1)
      '@emotion/styled': 11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1)

  '@mui/system@5.16.7(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      '@mui/private-theming': 5.16.6(@types/react@18.3.5)(react@18.3.1)
      '@mui/styled-engine': 5.16.6(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@emotion/styled@11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1))(react@18.3.1)
      '@mui/types': 7.2.16(@types/react@18.3.5)
      '@mui/utils': 5.16.6(@types/react@18.3.5)(react@18.3.1)
      clsx: 2.1.1
      csstype: 3.1.3
      prop-types: 15.8.1
      react: 18.3.1
    optionalDependencies:
      '@emotion/react': 11.13.3(@types/react@18.3.5)(react@18.3.1)
      '@emotion/styled': 11.13.0(@emotion/react@11.13.3(@types/react@18.3.5)(react@18.3.1))(@types/react@18.3.5)(react@18.3.1)
      '@types/react': 18.3.5

  '@mui/types@7.2.16(@types/react@18.3.5)':
    optionalDependencies:
      '@types/react': 18.3.5

  '@mui/utils@5.16.6(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      '@mui/types': 7.2.16(@types/react@18.3.5)
      '@types/prop-types': 15.7.12
      clsx: 2.1.1
      prop-types: 15.8.1
      react: 18.3.1
      react-is: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  '@next/env@15.4.0-canary.61': {}

  '@next/eslint-plugin-next@14.2.11':
    dependencies:
      glob: 10.3.10

  '@next/swc-darwin-arm64@15.4.0-canary.61':
    optional: true

  '@next/swc-darwin-x64@15.4.0-canary.61':
    optional: true

  '@next/swc-linux-arm64-gnu@15.4.0-canary.61':
    optional: true

  '@next/swc-linux-arm64-musl@15.4.0-canary.61':
    optional: true

  '@next/swc-linux-x64-gnu@15.4.0-canary.61':
    optional: true

  '@next/swc-linux-x64-musl@15.4.0-canary.61':
    optional: true

  '@next/swc-win32-arm64-msvc@15.4.0-canary.61':
    optional: true

  '@next/swc-win32-x64-msvc@15.4.0-canary.61':
    optional: true

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.17.1

  '@nolyfill/is-core-module@1.0.39': {}

  '@peculiar/asn1-schema@2.3.13':
    dependencies:
      asn1js: 3.0.5
      pvtsutils: 1.3.5
      tslib: 2.7.0

  '@peculiar/json-schema@1.1.12':
    dependencies:
      tslib: 2.7.0

  '@peculiar/webcrypto@1.4.1':
    dependencies:
      '@peculiar/asn1-schema': 2.3.13
      '@peculiar/json-schema': 1.1.12
      pvtsutils: 1.3.5
      tslib: 2.7.0
      webcrypto-core: 1.8.0

  '@pkgjs/parseargs@0.11.0':
    optional: true

  '@popperjs/core@2.11.8': {}

  '@radix-ui/colors@0.1.9': {}

  '@radix-ui/number@1.0.1':
    dependencies:
      '@babel/runtime': 7.25.6

  '@radix-ui/number@1.1.0': {}

  '@radix-ui/primitive@1.0.1':
    dependencies:
      '@babel/runtime': 7.25.6

  '@radix-ui/primitive@1.1.0': {}

  '@radix-ui/react-arrow@1.0.3(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5
      '@types/react-dom': 18.3.0

  '@radix-ui/react-arrow@1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/react-primitive': 2.0.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5
      '@types/react-dom': 18.3.0

  '@radix-ui/react-collection@1.0.3(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      '@radix-ui/react-compose-refs': 1.0.1(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-context': 1.0.1(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-slot': 1.0.2(@types/react@18.3.5)(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5
      '@types/react-dom': 18.3.0

  '@radix-ui/react-collection@1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-context': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-primitive': 2.0.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-slot': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5
      '@types/react-dom': 18.3.0

  '@radix-ui/react-compose-refs@1.0.1(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  '@radix-ui/react-compose-refs@1.1.0(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  '@radix-ui/react-context@1.0.1(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  '@radix-ui/react-context@1.1.0(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  '@radix-ui/react-direction@1.0.1(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  '@radix-ui/react-direction@1.1.0(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  '@radix-ui/react-dismissable-layer@1.0.4(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      '@radix-ui/primitive': 1.0.1
      '@radix-ui/react-compose-refs': 1.0.1(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-use-callback-ref': 1.0.1(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-use-escape-keydown': 1.0.3(@types/react@18.3.5)(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5
      '@types/react-dom': 18.3.0

  '@radix-ui/react-dismissable-layer@1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/primitive': 1.1.0
      '@radix-ui/react-compose-refs': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-primitive': 2.0.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-use-callback-ref': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-use-escape-keydown': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5
      '@types/react-dom': 18.3.0

  '@radix-ui/react-dropdown-menu@2.1.1(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/primitive': 1.1.0
      '@radix-ui/react-compose-refs': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-context': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-id': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-menu': 2.1.1(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-primitive': 2.0.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-use-controllable-state': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5
      '@types/react-dom': 18.3.0

  '@radix-ui/react-focus-guards@1.0.1(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  '@radix-ui/react-focus-guards@1.1.0(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  '@radix-ui/react-focus-scope@1.0.3(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      '@radix-ui/react-compose-refs': 1.0.1(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-use-callback-ref': 1.0.1(@types/react@18.3.5)(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5
      '@types/react-dom': 18.3.0

  '@radix-ui/react-focus-scope@1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-primitive': 2.0.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-use-callback-ref': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5
      '@types/react-dom': 18.3.0

  '@radix-ui/react-icons@1.3.0(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@radix-ui/react-id@1.0.1(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      '@radix-ui/react-use-layout-effect': 1.0.1(@types/react@18.3.5)(react@18.3.1)
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  '@radix-ui/react-id@1.1.0(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      '@radix-ui/react-use-layout-effect': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  '@radix-ui/react-menu@2.1.1(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/primitive': 1.1.0
      '@radix-ui/react-collection': 1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-compose-refs': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-context': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-direction': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-dismissable-layer': 1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-focus-guards': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-focus-scope': 1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-id': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-popper': 1.2.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-portal': 1.1.1(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-presence': 1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-primitive': 2.0.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-roving-focus': 1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-slot': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-use-callback-ref': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      aria-hidden: 1.2.4
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-remove-scroll: 2.5.7(@types/react@18.3.5)(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5
      '@types/react-dom': 18.3.0

  '@radix-ui/react-popover@1.1.1(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/primitive': 1.1.0
      '@radix-ui/react-compose-refs': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-context': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-dismissable-layer': 1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-focus-guards': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-focus-scope': 1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-id': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-popper': 1.2.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-portal': 1.1.1(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-presence': 1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-primitive': 2.0.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-slot': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-use-controllable-state': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      aria-hidden: 1.2.4
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-remove-scroll: 2.5.7(@types/react@18.3.5)(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5
      '@types/react-dom': 18.3.0

  '@radix-ui/react-popper@1.1.2(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      '@floating-ui/react-dom': 2.1.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-arrow': 1.0.3(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-compose-refs': 1.0.1(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-context': 1.0.1(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-use-callback-ref': 1.0.1(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-use-layout-effect': 1.0.1(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-use-rect': 1.0.1(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-use-size': 1.0.1(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/rect': 1.0.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5
      '@types/react-dom': 18.3.0

  '@radix-ui/react-popper@1.2.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@floating-ui/react-dom': 2.1.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-arrow': 1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-compose-refs': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-context': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-primitive': 2.0.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-use-callback-ref': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-use-layout-effect': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-use-rect': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-use-size': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/rect': 1.1.0
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5
      '@types/react-dom': 18.3.0

  '@radix-ui/react-portal@1.0.3(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5
      '@types/react-dom': 18.3.0

  '@radix-ui/react-portal@1.1.1(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/react-primitive': 2.0.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-use-layout-effect': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5
      '@types/react-dom': 18.3.0

  '@radix-ui/react-presence@1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-use-layout-effect': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5
      '@types/react-dom': 18.3.0

  '@radix-ui/react-primitive@1.0.3(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      '@radix-ui/react-slot': 1.0.2(@types/react@18.3.5)(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5
      '@types/react-dom': 18.3.0

  '@radix-ui/react-primitive@2.0.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/react-slot': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5
      '@types/react-dom': 18.3.0

  '@radix-ui/react-progress@1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/react-context': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-primitive': 2.0.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5
      '@types/react-dom': 18.3.0

  '@radix-ui/react-roving-focus@1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/primitive': 1.1.0
      '@radix-ui/react-collection': 1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-compose-refs': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-context': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-direction': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-id': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-primitive': 2.0.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-use-callback-ref': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-use-controllable-state': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5
      '@types/react-dom': 18.3.0

  '@radix-ui/react-scroll-area@1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@radix-ui/number': 1.1.0
      '@radix-ui/primitive': 1.1.0
      '@radix-ui/react-compose-refs': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-context': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-direction': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-presence': 1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-primitive': 2.0.0(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-use-callback-ref': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-use-layout-effect': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5
      '@types/react-dom': 18.3.0

  '@radix-ui/react-select@1.2.2(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      '@radix-ui/number': 1.0.1
      '@radix-ui/primitive': 1.0.1
      '@radix-ui/react-collection': 1.0.3(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-compose-refs': 1.0.1(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-context': 1.0.1(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-direction': 1.0.1(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-dismissable-layer': 1.0.4(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-focus-guards': 1.0.1(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-focus-scope': 1.0.3(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-id': 1.0.1(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-popper': 1.1.2(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-portal': 1.0.3(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@radix-ui/react-slot': 1.0.2(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-use-callback-ref': 1.0.1(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-use-controllable-state': 1.0.1(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-use-layout-effect': 1.0.1(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-use-previous': 1.0.1(@types/react@18.3.5)(react@18.3.1)
      '@radix-ui/react-visually-hidden': 1.0.3(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      aria-hidden: 1.2.4
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-remove-scroll: 2.5.5(@types/react@18.3.5)(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5
      '@types/react-dom': 18.3.0

  '@radix-ui/react-slot@1.0.2(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      '@radix-ui/react-compose-refs': 1.0.1(@types/react@18.3.5)(react@18.3.1)
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  '@radix-ui/react-slot@1.1.0(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  '@radix-ui/react-use-callback-ref@1.0.1(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  '@radix-ui/react-use-callback-ref@1.1.0(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  '@radix-ui/react-use-controllable-state@1.0.1(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      '@radix-ui/react-use-callback-ref': 1.0.1(@types/react@18.3.5)(react@18.3.1)
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  '@radix-ui/react-use-controllable-state@1.1.0(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      '@radix-ui/react-use-callback-ref': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  '@radix-ui/react-use-escape-keydown@1.0.3(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      '@radix-ui/react-use-callback-ref': 1.0.1(@types/react@18.3.5)(react@18.3.1)
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  '@radix-ui/react-use-escape-keydown@1.1.0(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      '@radix-ui/react-use-callback-ref': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  '@radix-ui/react-use-layout-effect@1.0.1(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  '@radix-ui/react-use-layout-effect@1.1.0(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  '@radix-ui/react-use-previous@1.0.1(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  '@radix-ui/react-use-rect@1.0.1(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      '@radix-ui/rect': 1.0.1
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  '@radix-ui/react-use-rect@1.1.0(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      '@radix-ui/rect': 1.1.0
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  '@radix-ui/react-use-size@1.0.1(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      '@radix-ui/react-use-layout-effect': 1.0.1(@types/react@18.3.5)(react@18.3.1)
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  '@radix-ui/react-use-size@1.1.0(@types/react@18.3.5)(react@18.3.1)':
    dependencies:
      '@radix-ui/react-use-layout-effect': 1.1.0(@types/react@18.3.5)(react@18.3.1)
      react: 18.3.1
    optionalDependencies:
      '@types/react': 18.3.5

  '@radix-ui/react-visually-hidden@1.0.3(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@babel/runtime': 7.25.6
      '@radix-ui/react-primitive': 1.0.3(@types/react-dom@18.3.0)(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5
      '@types/react-dom': 18.3.0

  '@radix-ui/rect@1.0.1':
    dependencies:
      '@babel/runtime': 7.25.6

  '@radix-ui/rect@1.1.0': {}

  '@reactflow/background@11.3.14(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@reactflow/core': 11.11.4(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      classcat: 5.0.5
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      zustand: 4.5.5(@types/react@18.3.5)(react@18.3.1)
    transitivePeerDependencies:
      - '@types/react'
      - immer

  '@reactflow/controls@11.2.14(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@reactflow/core': 11.11.4(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      classcat: 5.0.5
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      zustand: 4.5.5(@types/react@18.3.5)(react@18.3.1)
    transitivePeerDependencies:
      - '@types/react'
      - immer

  '@reactflow/core@11.11.4(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@types/d3': 7.4.3
      '@types/d3-drag': 3.0.7
      '@types/d3-selection': 3.0.10
      '@types/d3-zoom': 3.0.8
      classcat: 5.0.5
      d3-drag: 3.0.0
      d3-selection: 3.0.0
      d3-zoom: 3.0.0
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      zustand: 4.5.5(@types/react@18.3.5)(react@18.3.1)
    transitivePeerDependencies:
      - '@types/react'
      - immer

  '@reactflow/minimap@11.7.14(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@reactflow/core': 11.11.4(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@types/d3-selection': 3.0.10
      '@types/d3-zoom': 3.0.8
      classcat: 5.0.5
      d3-selection: 3.0.0
      d3-zoom: 3.0.0
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      zustand: 4.5.5(@types/react@18.3.5)(react@18.3.1)
    transitivePeerDependencies:
      - '@types/react'
      - immer

  '@reactflow/node-resizer@2.2.14(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@reactflow/core': 11.11.4(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      classcat: 5.0.5
      d3-drag: 3.0.0
      d3-selection: 3.0.0
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      zustand: 4.5.5(@types/react@18.3.5)(react@18.3.1)
    transitivePeerDependencies:
      - '@types/react'
      - immer

  '@reactflow/node-toolbar@1.3.14(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@reactflow/core': 11.11.4(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      classcat: 5.0.5
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      zustand: 4.5.5(@types/react@18.3.5)(react@18.3.1)
    transitivePeerDependencies:
      - '@types/react'
      - immer

  '@rtsao/scc@1.1.0': {}

  '@rushstack/eslint-patch@1.10.4': {}

  '@smithy/abort-controller@3.1.4':
    dependencies:
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@smithy/chunked-blob-reader-native@3.0.0':
    dependencies:
      '@smithy/util-base64': 3.0.0
      tslib: 2.7.0

  '@smithy/chunked-blob-reader@3.0.0':
    dependencies:
      tslib: 2.7.0

  '@smithy/config-resolver@3.0.8':
    dependencies:
      '@smithy/node-config-provider': 3.1.7
      '@smithy/types': 3.4.2
      '@smithy/util-config-provider': 3.0.0
      '@smithy/util-middleware': 3.0.6
      tslib: 2.7.0

  '@smithy/core@2.4.3':
    dependencies:
      '@smithy/middleware-endpoint': 3.1.3
      '@smithy/middleware-retry': 3.0.18
      '@smithy/middleware-serde': 3.0.6
      '@smithy/protocol-http': 4.1.3
      '@smithy/smithy-client': 3.3.2
      '@smithy/types': 3.4.2
      '@smithy/util-body-length-browser': 3.0.0
      '@smithy/util-middleware': 3.0.6
      '@smithy/util-utf8': 3.0.0
      tslib: 2.7.0

  '@smithy/credential-provider-imds@3.2.3':
    dependencies:
      '@smithy/node-config-provider': 3.1.7
      '@smithy/property-provider': 3.1.6
      '@smithy/types': 3.4.2
      '@smithy/url-parser': 3.0.6
      tslib: 2.7.0

  '@smithy/eventstream-codec@3.1.5':
    dependencies:
      '@aws-crypto/crc32': 5.2.0
      '@smithy/types': 3.4.2
      '@smithy/util-hex-encoding': 3.0.0
      tslib: 2.7.0

  '@smithy/eventstream-serde-browser@3.0.9':
    dependencies:
      '@smithy/eventstream-serde-universal': 3.0.8
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@smithy/eventstream-serde-config-resolver@3.0.6':
    dependencies:
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@smithy/eventstream-serde-node@3.0.8':
    dependencies:
      '@smithy/eventstream-serde-universal': 3.0.8
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@smithy/eventstream-serde-universal@3.0.8':
    dependencies:
      '@smithy/eventstream-codec': 3.1.5
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@smithy/fetch-http-handler@3.2.7':
    dependencies:
      '@smithy/protocol-http': 4.1.3
      '@smithy/querystring-builder': 3.0.6
      '@smithy/types': 3.4.2
      '@smithy/util-base64': 3.0.0
      tslib: 2.7.0

  '@smithy/hash-blob-browser@3.1.5':
    dependencies:
      '@smithy/chunked-blob-reader': 3.0.0
      '@smithy/chunked-blob-reader-native': 3.0.0
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@smithy/hash-node@3.0.6':
    dependencies:
      '@smithy/types': 3.4.2
      '@smithy/util-buffer-from': 3.0.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.7.0

  '@smithy/hash-stream-node@3.1.5':
    dependencies:
      '@smithy/types': 3.4.2
      '@smithy/util-utf8': 3.0.0
      tslib: 2.7.0

  '@smithy/invalid-dependency@3.0.6':
    dependencies:
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@smithy/is-array-buffer@2.2.0':
    dependencies:
      tslib: 2.7.0

  '@smithy/is-array-buffer@3.0.0':
    dependencies:
      tslib: 2.7.0

  '@smithy/md5-js@3.0.6':
    dependencies:
      '@smithy/types': 3.4.2
      '@smithy/util-utf8': 3.0.0
      tslib: 2.7.0

  '@smithy/middleware-content-length@3.0.8':
    dependencies:
      '@smithy/protocol-http': 4.1.3
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@smithy/middleware-endpoint@3.1.3':
    dependencies:
      '@smithy/middleware-serde': 3.0.6
      '@smithy/node-config-provider': 3.1.7
      '@smithy/shared-ini-file-loader': 3.1.7
      '@smithy/types': 3.4.2
      '@smithy/url-parser': 3.0.6
      '@smithy/util-middleware': 3.0.6
      tslib: 2.7.0

  '@smithy/middleware-retry@3.0.18':
    dependencies:
      '@smithy/node-config-provider': 3.1.7
      '@smithy/protocol-http': 4.1.3
      '@smithy/service-error-classification': 3.0.6
      '@smithy/smithy-client': 3.3.2
      '@smithy/types': 3.4.2
      '@smithy/util-middleware': 3.0.6
      '@smithy/util-retry': 3.0.6
      tslib: 2.7.0
      uuid: 9.0.1

  '@smithy/middleware-serde@3.0.6':
    dependencies:
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@smithy/middleware-stack@3.0.6':
    dependencies:
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@smithy/node-config-provider@3.1.7':
    dependencies:
      '@smithy/property-provider': 3.1.6
      '@smithy/shared-ini-file-loader': 3.1.7
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@smithy/node-http-handler@3.2.2':
    dependencies:
      '@smithy/abort-controller': 3.1.4
      '@smithy/protocol-http': 4.1.3
      '@smithy/querystring-builder': 3.0.6
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@smithy/property-provider@3.1.6':
    dependencies:
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@smithy/protocol-http@4.1.3':
    dependencies:
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@smithy/querystring-builder@3.0.6':
    dependencies:
      '@smithy/types': 3.4.2
      '@smithy/util-uri-escape': 3.0.0
      tslib: 2.7.0

  '@smithy/querystring-parser@3.0.6':
    dependencies:
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@smithy/service-error-classification@3.0.6':
    dependencies:
      '@smithy/types': 3.4.2

  '@smithy/shared-ini-file-loader@3.1.7':
    dependencies:
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@smithy/signature-v4@4.1.3':
    dependencies:
      '@smithy/is-array-buffer': 3.0.0
      '@smithy/protocol-http': 4.1.3
      '@smithy/types': 3.4.2
      '@smithy/util-hex-encoding': 3.0.0
      '@smithy/util-middleware': 3.0.6
      '@smithy/util-uri-escape': 3.0.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.7.0

  '@smithy/smithy-client@3.3.2':
    dependencies:
      '@smithy/middleware-endpoint': 3.1.3
      '@smithy/middleware-stack': 3.0.6
      '@smithy/protocol-http': 4.1.3
      '@smithy/types': 3.4.2
      '@smithy/util-stream': 3.1.6
      tslib: 2.7.0

  '@smithy/types@3.4.2':
    dependencies:
      tslib: 2.7.0

  '@smithy/url-parser@3.0.6':
    dependencies:
      '@smithy/querystring-parser': 3.0.6
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@smithy/util-base64@3.0.0':
    dependencies:
      '@smithy/util-buffer-from': 3.0.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.7.0

  '@smithy/util-body-length-browser@3.0.0':
    dependencies:
      tslib: 2.7.0

  '@smithy/util-body-length-node@3.0.0':
    dependencies:
      tslib: 2.7.0

  '@smithy/util-buffer-from@2.2.0':
    dependencies:
      '@smithy/is-array-buffer': 2.2.0
      tslib: 2.7.0

  '@smithy/util-buffer-from@3.0.0':
    dependencies:
      '@smithy/is-array-buffer': 3.0.0
      tslib: 2.7.0

  '@smithy/util-config-provider@3.0.0':
    dependencies:
      tslib: 2.7.0

  '@smithy/util-defaults-mode-browser@3.0.18':
    dependencies:
      '@smithy/property-provider': 3.1.6
      '@smithy/smithy-client': 3.3.2
      '@smithy/types': 3.4.2
      bowser: 2.11.0
      tslib: 2.7.0

  '@smithy/util-defaults-mode-node@3.0.18':
    dependencies:
      '@smithy/config-resolver': 3.0.8
      '@smithy/credential-provider-imds': 3.2.3
      '@smithy/node-config-provider': 3.1.7
      '@smithy/property-provider': 3.1.6
      '@smithy/smithy-client': 3.3.2
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@smithy/util-endpoints@2.1.2':
    dependencies:
      '@smithy/node-config-provider': 3.1.7
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@smithy/util-hex-encoding@3.0.0':
    dependencies:
      tslib: 2.7.0

  '@smithy/util-middleware@3.0.6':
    dependencies:
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@smithy/util-retry@3.0.6':
    dependencies:
      '@smithy/service-error-classification': 3.0.6
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@smithy/util-stream@3.1.6':
    dependencies:
      '@smithy/fetch-http-handler': 3.2.7
      '@smithy/node-http-handler': 3.2.2
      '@smithy/types': 3.4.2
      '@smithy/util-base64': 3.0.0
      '@smithy/util-buffer-from': 3.0.0
      '@smithy/util-hex-encoding': 3.0.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.7.0

  '@smithy/util-uri-escape@3.0.0':
    dependencies:
      tslib: 2.7.0

  '@smithy/util-utf8@2.3.0':
    dependencies:
      '@smithy/util-buffer-from': 2.2.0
      tslib: 2.7.0

  '@smithy/util-utf8@3.0.0':
    dependencies:
      '@smithy/util-buffer-from': 3.0.0
      tslib: 2.7.0

  '@smithy/util-waiter@3.1.5':
    dependencies:
      '@smithy/abort-controller': 3.1.4
      '@smithy/types': 3.4.2
      tslib: 2.7.0

  '@stitches/react@1.2.8(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@stripe/react-stripe-js@2.8.0(@stripe/stripe-js@1.54.2)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@stripe/stripe-js': 1.54.2
      prop-types: 15.8.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@stripe/stripe-js@1.54.2': {}

  '@swc/helpers@0.5.15':
    dependencies:
      tslib: 2.8.1

  '@tailwindcss/aspect-ratio@0.4.2(tailwindcss@3.4.11)':
    dependencies:
      tailwindcss: 3.4.11

  '@tailwindcss/forms@0.5.9(tailwindcss@3.4.11)':
    dependencies:
      mini-svg-data-uri: 1.4.4
      tailwindcss: 3.4.11

  '@tanstack/react-table@8.20.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@tanstack/table-core': 8.20.5
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@tanstack/react-virtual@3.10.7(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@tanstack/virtual-core': 3.10.7
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@tanstack/table-core@8.20.5': {}

  '@tanstack/virtual-core@3.10.7': {}

  '@tremor/react@3.18.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(tailwindcss@3.4.11)':
    dependencies:
      '@floating-ui/react': 0.19.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@headlessui/react': 1.7.19(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@headlessui/tailwindcss': 0.2.1(tailwindcss@3.4.11)
      date-fns: 3.6.0
      react: 18.3.1
      react-day-picker: 8.10.1(date-fns@3.6.0)(react@18.3.1)
      react-dom: 18.3.1(react@18.3.1)
      react-transition-state: 2.1.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      recharts: 2.12.7(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      tailwind-merge: 1.14.0
    transitivePeerDependencies:
      - tailwindcss

  '@types/body-parser@1.19.5':
    dependencies:
      '@types/connect': 3.4.38
      '@types/node': 18.19.50

  '@types/connect@3.4.38':
    dependencies:
      '@types/node': 18.19.50

  '@types/cookies@0.7.7':
    dependencies:
      '@types/connect': 3.4.38
      '@types/express': 4.17.14
      '@types/keygrip': 1.0.6
      '@types/node': 18.19.50

  '@types/d3-array@3.2.1': {}

  '@types/d3-axis@3.0.6':
    dependencies:
      '@types/d3-selection': 3.0.10

  '@types/d3-brush@3.0.6':
    dependencies:
      '@types/d3-selection': 3.0.10

  '@types/d3-chord@3.0.6': {}

  '@types/d3-color@3.1.3': {}

  '@types/d3-contour@3.0.6':
    dependencies:
      '@types/d3-array': 3.2.1
      '@types/geojson': 7946.0.14

  '@types/d3-delaunay@6.0.4': {}

  '@types/d3-dispatch@3.0.6': {}

  '@types/d3-drag@3.0.7':
    dependencies:
      '@types/d3-selection': 3.0.10

  '@types/d3-dsv@3.0.7': {}

  '@types/d3-ease@3.0.2': {}

  '@types/d3-fetch@3.0.7':
    dependencies:
      '@types/d3-dsv': 3.0.7

  '@types/d3-force@3.0.10': {}

  '@types/d3-format@3.0.4': {}

  '@types/d3-geo@3.1.0':
    dependencies:
      '@types/geojson': 7946.0.14

  '@types/d3-hierarchy@3.1.7': {}

  '@types/d3-interpolate@3.0.4':
    dependencies:
      '@types/d3-color': 3.1.3

  '@types/d3-path@3.1.0': {}

  '@types/d3-polygon@3.0.2': {}

  '@types/d3-quadtree@3.0.6': {}

  '@types/d3-random@3.0.3': {}

  '@types/d3-scale-chromatic@3.0.3': {}

  '@types/d3-scale@4.0.8':
    dependencies:
      '@types/d3-time': 3.0.3

  '@types/d3-selection@3.0.10': {}

  '@types/d3-shape@3.1.6':
    dependencies:
      '@types/d3-path': 3.1.0

  '@types/d3-time-format@4.0.3': {}

  '@types/d3-time@3.0.3': {}

  '@types/d3-timer@3.0.2': {}

  '@types/d3-transition@3.0.8':
    dependencies:
      '@types/d3-selection': 3.0.10

  '@types/d3-zoom@3.0.8':
    dependencies:
      '@types/d3-interpolate': 3.0.4
      '@types/d3-selection': 3.0.10

  '@types/d3@7.4.3':
    dependencies:
      '@types/d3-array': 3.2.1
      '@types/d3-axis': 3.0.6
      '@types/d3-brush': 3.0.6
      '@types/d3-chord': 3.0.6
      '@types/d3-color': 3.1.3
      '@types/d3-contour': 3.0.6
      '@types/d3-delaunay': 6.0.4
      '@types/d3-dispatch': 3.0.6
      '@types/d3-drag': 3.0.7
      '@types/d3-dsv': 3.0.7
      '@types/d3-ease': 3.0.2
      '@types/d3-fetch': 3.0.7
      '@types/d3-force': 3.0.10
      '@types/d3-format': 3.0.4
      '@types/d3-geo': 3.1.0
      '@types/d3-hierarchy': 3.1.7
      '@types/d3-interpolate': 3.0.4
      '@types/d3-path': 3.1.0
      '@types/d3-polygon': 3.0.2
      '@types/d3-quadtree': 3.0.6
      '@types/d3-random': 3.0.3
      '@types/d3-scale': 4.0.8
      '@types/d3-scale-chromatic': 3.0.3
      '@types/d3-selection': 3.0.10
      '@types/d3-shape': 3.1.6
      '@types/d3-time': 3.0.3
      '@types/d3-time-format': 4.0.3
      '@types/d3-timer': 3.0.2
      '@types/d3-transition': 3.0.8
      '@types/d3-zoom': 3.0.8

  '@types/express-serve-static-core@4.19.5':
    dependencies:
      '@types/node': 18.19.50
      '@types/qs': 6.9.16
      '@types/range-parser': 1.2.7
      '@types/send': 0.17.4

  '@types/express@4.17.14':
    dependencies:
      '@types/body-parser': 1.19.5
      '@types/express-serve-static-core': 4.19.5
      '@types/qs': 6.9.16
      '@types/serve-static': 1.15.7

  '@types/geojson@7946.0.14': {}

  '@types/hast@2.3.10':
    dependencies:
      '@types/unist': 2.0.11

  '@types/http-errors@2.0.4': {}

  '@types/json-schema@7.0.15': {}

  '@types/json5@0.0.29': {}

  '@types/keygrip@1.0.6': {}

  '@types/lodash.mergewith@4.6.7':
    dependencies:
      '@types/lodash': 4.17.7

  '@types/lodash@4.17.7': {}

  '@types/mime@1.3.5': {}

  '@types/node-fetch@2.6.11':
    dependencies:
      '@types/node': 18.19.50
      form-data: 4.0.0

  '@types/node-fetch@2.6.2':
    dependencies:
      '@types/node': 18.19.50
      form-data: 3.0.1

  '@types/node@16.18.6': {}

  '@types/node@18.19.50':
    dependencies:
      undici-types: 5.26.5

  '@types/parse-json@4.0.2': {}

  '@types/pg@8.12.0':
    dependencies:
      '@types/node': 18.19.50
      pg-protocol: 1.9.5
      pg-types: 4.0.2

  '@types/prop-types@15.7.12': {}

  '@types/qs@6.9.16': {}

  '@types/range-parser@1.2.7': {}

  '@types/react-dom@18.3.0':
    dependencies:
      '@types/react': 18.3.5

  '@types/react-reconciler@0.28.8':
    dependencies:
      '@types/react': 18.3.5

  '@types/react-transition-group@4.4.11':
    dependencies:
      '@types/react': 18.3.5

  '@types/react@18.3.5':
    dependencies:
      '@types/prop-types': 15.7.12
      csstype: 3.1.3

  '@types/semver@7.5.8': {}

  '@types/send@0.17.4':
    dependencies:
      '@types/mime': 1.3.5
      '@types/node': 18.19.50

  '@types/serve-static@1.15.7':
    dependencies:
      '@types/http-errors': 2.0.4
      '@types/node': 18.19.50
      '@types/send': 0.17.4

  '@types/stylis@4.2.5': {}

  '@types/unist@2.0.11': {}

  '@typescript-eslint/eslint-plugin@5.62.0(@typescript-eslint/parser@5.62.0(eslint@8.57.0)(typescript@5.6.2))(eslint@8.57.0)(typescript@5.6.2)':
    dependencies:
      '@eslint-community/regexpp': 4.11.1
      '@typescript-eslint/parser': 5.62.0(eslint@8.57.0)(typescript@5.6.2)
      '@typescript-eslint/scope-manager': 5.62.0
      '@typescript-eslint/type-utils': 5.62.0(eslint@8.57.0)(typescript@5.6.2)
      '@typescript-eslint/utils': 5.62.0(eslint@8.57.0)(typescript@5.6.2)
      debug: 4.3.7
      eslint: 8.57.0
      graphemer: 1.4.0
      ignore: 5.3.2
      natural-compare-lite: 1.4.0
      semver: 7.6.3
      tsutils: 3.21.0(typescript@5.6.2)
    optionalDependencies:
      typescript: 5.6.2
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/parser@5.62.0(eslint@8.57.0)(typescript@5.6.2)':
    dependencies:
      '@typescript-eslint/scope-manager': 5.62.0
      '@typescript-eslint/types': 5.62.0
      '@typescript-eslint/typescript-estree': 5.62.0(typescript@5.6.2)
      debug: 4.3.7
      eslint: 8.57.0
    optionalDependencies:
      typescript: 5.6.2
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/scope-manager@5.62.0':
    dependencies:
      '@typescript-eslint/types': 5.62.0
      '@typescript-eslint/visitor-keys': 5.62.0

  '@typescript-eslint/type-utils@5.62.0(eslint@8.57.0)(typescript@5.6.2)':
    dependencies:
      '@typescript-eslint/typescript-estree': 5.62.0(typescript@5.6.2)
      '@typescript-eslint/utils': 5.62.0(eslint@8.57.0)(typescript@5.6.2)
      debug: 4.3.7
      eslint: 8.57.0
      tsutils: 3.21.0(typescript@5.6.2)
    optionalDependencies:
      typescript: 5.6.2
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/types@5.62.0': {}

  '@typescript-eslint/typescript-estree@5.62.0(typescript@5.6.2)':
    dependencies:
      '@typescript-eslint/types': 5.62.0
      '@typescript-eslint/visitor-keys': 5.62.0
      debug: 4.3.7
      globby: 11.1.0
      is-glob: 4.0.3
      semver: 7.6.3
      tsutils: 3.21.0(typescript@5.6.2)
    optionalDependencies:
      typescript: 5.6.2
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/utils@5.62.0(eslint@8.57.0)(typescript@5.6.2)':
    dependencies:
      '@eslint-community/eslint-utils': 4.4.0(eslint@8.57.0)
      '@types/json-schema': 7.0.15
      '@types/semver': 7.5.8
      '@typescript-eslint/scope-manager': 5.62.0
      '@typescript-eslint/types': 5.62.0
      '@typescript-eslint/typescript-estree': 5.62.0(typescript@5.6.2)
      eslint: 8.57.0
      eslint-scope: 5.1.1
      semver: 7.6.3
    transitivePeerDependencies:
      - supports-color
      - typescript

  '@typescript-eslint/visitor-keys@5.62.0':
    dependencies:
      '@typescript-eslint/types': 5.62.0
      eslint-visitor-keys: 3.4.3

  '@ungap/structured-clone@1.2.0': {}

  '@xata.io/client@0.29.5(typescript@5.6.2)':
    dependencies:
      typescript: 5.6.2

  '@zag-js/dom-query@0.16.0': {}

  '@zag-js/element-size@0.10.5': {}

  '@zag-js/focus-visible@0.16.0':
    dependencies:
      '@zag-js/dom-query': 0.16.0

  abort-controller@3.0.0:
    dependencies:
      event-target-shim: 5.0.1

  acorn-jsx@5.3.2(acorn@8.12.1):
    dependencies:
      acorn: 8.12.1

  acorn@8.12.1: {}

  agentkeepalive@4.5.0:
    dependencies:
      humanize-ms: 1.2.1

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ansi-regex@5.0.1: {}

  ansi-regex@6.1.0: {}

  ansi-styles@3.2.1:
    dependencies:
      color-convert: 1.9.3

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@6.2.1: {}

  any-promise@1.3.0: {}

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  arg@5.0.2: {}

  argparse@2.0.1: {}

  aria-hidden@1.2.4:
    dependencies:
      tslib: 2.7.0

  aria-query@5.1.3:
    dependencies:
      deep-equal: 2.2.3

  array-buffer-byte-length@1.0.1:
    dependencies:
      call-bind: 1.0.7
      is-array-buffer: 3.0.4

  array-includes@3.1.8:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-object-atoms: 1.0.0
      get-intrinsic: 1.2.4
      is-string: 1.0.7

  array-union@2.1.0: {}

  array.prototype.findlast@1.2.5:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      es-shim-unscopables: 1.0.2

  array.prototype.findlastindex@1.2.5:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      es-shim-unscopables: 1.0.2

  array.prototype.flat@1.3.2:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-shim-unscopables: 1.0.2

  array.prototype.flatmap@1.3.2:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-shim-unscopables: 1.0.2

  array.prototype.tosorted@1.1.4:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      es-shim-unscopables: 1.0.2

  arraybuffer.prototype.slice@1.0.3:
    dependencies:
      array-buffer-byte-length: 1.0.1
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      get-intrinsic: 1.2.4
      is-array-buffer: 3.0.4
      is-shared-array-buffer: 1.0.3

  asn1js@3.0.5:
    dependencies:
      pvtsutils: 1.3.5
      pvutils: 1.1.3
      tslib: 2.7.0

  ast-types-flow@0.0.8: {}

  asynckit@0.4.0: {}

  available-typed-arrays@1.0.7:
    dependencies:
      possible-typed-array-names: 1.0.0

  axe-core@4.10.0: {}

  axios@1.7.7:
    dependencies:
      follow-redirects: 1.15.9
      form-data: 4.0.0
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  axobject-query@4.1.0: {}

  babel-plugin-macros@3.1.0:
    dependencies:
      '@babel/runtime': 7.25.6
      cosmiconfig: 7.1.0
      resolve: 1.22.8

  balanced-match@1.0.2: {}

  base-64@1.0.0: {}

  binary-extensions@2.3.0: {}

  bowser@2.11.0: {}

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.1:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  call-bind@1.0.7:
    dependencies:
      es-define-property: 1.0.0
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      set-function-length: 1.2.2

  callsites@3.1.0: {}

  camelcase-css@2.0.1: {}

  camelcase-keys@6.2.2:
    dependencies:
      camelcase: 5.3.1
      map-obj: 4.3.0
      quick-lru: 4.0.1

  camelcase@5.3.1: {}

  camelize@1.0.1: {}

  caniuse-lite@1.0.30001660: {}

  canvas-confetti@1.9.3: {}

  chalk@2.4.2:
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  character-entities-legacy@1.1.4: {}

  character-entities@1.2.4: {}

  character-reference-invalid@1.1.4: {}

  chart.js@4.4.4:
    dependencies:
      '@kurkle/color': 0.3.2

  chartjs-adapter-date-fns@3.0.0(chart.js@4.4.4)(date-fns@3.6.0):
    dependencies:
      chart.js: 4.4.4
      date-fns: 3.6.0

  chartjs-adapter-luxon@1.3.1(chart.js@4.4.4)(luxon@3.5.0):
    dependencies:
      chart.js: 4.4.4
      luxon: 3.5.0

  chartjs-plugin-streaming@2.0.0(chart.js@4.4.4):
    dependencies:
      chart.js: 4.4.4

  chartjs-plugin-zoom@2.0.1(chart.js@4.4.4):
    dependencies:
      chart.js: 4.4.4
      hammerjs: 2.0.8

  chokidar@3.6.0:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  class-variance-authority@0.7.0:
    dependencies:
      clsx: 2.0.0

  classcat@5.0.5: {}

  client-only@0.0.1: {}

  clsx@1.2.1: {}

  clsx@2.0.0: {}

  clsx@2.1.1: {}

  color-convert@1.9.3:
    dependencies:
      color-name: 1.1.3

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.3: {}

  color-name@1.1.4: {}

  color-string@1.9.1:
    dependencies:
      color-name: 1.1.4
      simple-swizzle: 0.2.2
    optional: true

  color2k@2.0.3: {}

  color@4.2.3:
    dependencies:
      color-convert: 2.0.1
      color-string: 1.9.1
    optional: true

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  comma-separated-tokens@1.0.8: {}

  commander@4.1.1: {}

  compute-scroll-into-view@3.0.3: {}

  concat-map@0.0.1: {}

  convert-source-map@1.9.0: {}

  cookie@0.5.0: {}

  copy-to-clipboard@3.3.3:
    dependencies:
      toggle-selection: 1.0.6

  cosmiconfig@7.1.0:
    dependencies:
      '@types/parse-json': 4.0.2
      import-fresh: 3.3.0
      parse-json: 5.2.0
      path-type: 4.0.0
      yaml: 1.10.2

  cross-spawn@7.0.3:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  css-box-model@1.2.1:
    dependencies:
      tiny-invariant: 1.3.3

  css-color-keywords@1.0.0: {}

  css-to-react-native@3.2.0:
    dependencies:
      camelize: 1.0.1
      css-color-keywords: 1.0.0
      postcss-value-parser: 4.2.0

  cssesc@3.0.0: {}

  csstype@3.1.1: {}

  csstype@3.1.3: {}

  d3-array@3.2.4:
    dependencies:
      internmap: 2.0.3

  d3-color@3.1.0: {}

  d3-dispatch@3.0.1: {}

  d3-drag@3.0.0:
    dependencies:
      d3-dispatch: 3.0.1
      d3-selection: 3.0.0

  d3-ease@3.0.1: {}

  d3-format@3.1.0: {}

  d3-interpolate@3.0.1:
    dependencies:
      d3-color: 3.1.0

  d3-path@3.1.0: {}

  d3-scale@4.0.2:
    dependencies:
      d3-array: 3.2.4
      d3-format: 3.1.0
      d3-interpolate: 3.0.1
      d3-time: 3.1.0
      d3-time-format: 4.1.0

  d3-selection@3.0.0: {}

  d3-shape@3.2.0:
    dependencies:
      d3-path: 3.1.0

  d3-time-format@4.1.0:
    dependencies:
      d3-time: 3.1.0

  d3-time@3.1.0:
    dependencies:
      d3-array: 3.2.4

  d3-timer@3.0.1: {}

  d3-transition@3.0.1(d3-selection@3.0.0):
    dependencies:
      d3-color: 3.1.0
      d3-dispatch: 3.0.1
      d3-ease: 3.0.1
      d3-interpolate: 3.0.1
      d3-selection: 3.0.0
      d3-timer: 3.0.1

  d3-zoom@3.0.0:
    dependencies:
      d3-dispatch: 3.0.1
      d3-drag: 3.0.0
      d3-interpolate: 3.0.1
      d3-selection: 3.0.0
      d3-transition: 3.0.1(d3-selection@3.0.0)

  damerau-levenshtein@1.0.8: {}

  data-view-buffer@1.0.1:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-data-view: 1.0.1

  data-view-byte-length@1.0.1:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-data-view: 1.0.1

  data-view-byte-offset@1.0.0:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-data-view: 1.0.1

  date-fns@3.6.0: {}

  dayjs@1.11.13: {}

  debug@3.2.7:
    dependencies:
      ms: 2.1.3

  debug@4.3.7:
    dependencies:
      ms: 2.1.3

  decimal.js-light@2.5.1: {}

  deep-equal@2.2.3:
    dependencies:
      array-buffer-byte-length: 1.0.1
      call-bind: 1.0.7
      es-get-iterator: 1.1.3
      get-intrinsic: 1.2.4
      is-arguments: 1.1.1
      is-array-buffer: 3.0.4
      is-date-object: 1.0.5
      is-regex: 1.1.4
      is-shared-array-buffer: 1.0.3
      isarray: 2.0.5
      object-is: 1.1.6
      object-keys: 1.1.1
      object.assign: 4.1.5
      regexp.prototype.flags: 1.5.2
      side-channel: 1.0.6
      which-boxed-primitive: 1.0.2
      which-collection: 1.0.2
      which-typed-array: 1.1.15

  deep-is@0.1.4: {}

  deepmerge@4.2.2: {}

  define-data-property@1.1.4:
    dependencies:
      es-define-property: 1.0.0
      es-errors: 1.3.0
      gopd: 1.0.1

  define-properties@1.2.1:
    dependencies:
      define-data-property: 1.1.4
      has-property-descriptors: 1.0.2
      object-keys: 1.1.1

  delayed-stream@1.0.0: {}

  detect-libc@2.0.4:
    optional: true

  detect-node-es@1.1.0: {}

  didyoumean@1.2.2: {}

  dir-glob@3.0.1:
    dependencies:
      path-type: 4.0.0

  dlv@1.1.3: {}

  doctrine@2.1.0:
    dependencies:
      esutils: 2.0.3

  doctrine@3.0.0:
    dependencies:
      esutils: 2.0.3

  dom-helpers@5.2.1:
    dependencies:
      '@babel/runtime': 7.25.6
      csstype: 3.1.3

  dot-case@3.0.4:
    dependencies:
      no-case: 3.0.4
      tslib: 2.7.0

  eastasianwidth@0.2.0: {}

  emoji-regex@8.0.0: {}

  emoji-regex@9.2.2: {}

  enhanced-resolve@5.17.1:
    dependencies:
      graceful-fs: 4.2.11
      tapable: 2.2.1

  error-ex@1.3.2:
    dependencies:
      is-arrayish: 0.2.1

  es-abstract@1.23.3:
    dependencies:
      array-buffer-byte-length: 1.0.1
      arraybuffer.prototype.slice: 1.0.3
      available-typed-arrays: 1.0.7
      call-bind: 1.0.7
      data-view-buffer: 1.0.1
      data-view-byte-length: 1.0.1
      data-view-byte-offset: 1.0.0
      es-define-property: 1.0.0
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      es-set-tostringtag: 2.0.3
      es-to-primitive: 1.2.1
      function.prototype.name: 1.1.6
      get-intrinsic: 1.2.4
      get-symbol-description: 1.0.2
      globalthis: 1.0.4
      gopd: 1.0.1
      has-property-descriptors: 1.0.2
      has-proto: 1.0.3
      has-symbols: 1.0.3
      hasown: 2.0.2
      internal-slot: 1.0.7
      is-array-buffer: 3.0.4
      is-callable: 1.2.7
      is-data-view: 1.0.1
      is-negative-zero: 2.0.3
      is-regex: 1.1.4
      is-shared-array-buffer: 1.0.3
      is-string: 1.0.7
      is-typed-array: 1.1.13
      is-weakref: 1.0.2
      object-inspect: 1.13.2
      object-keys: 1.1.1
      object.assign: 4.1.5
      regexp.prototype.flags: 1.5.2
      safe-array-concat: 1.1.2
      safe-regex-test: 1.0.3
      string.prototype.trim: 1.2.9
      string.prototype.trimend: 1.0.8
      string.prototype.trimstart: 1.0.8
      typed-array-buffer: 1.0.2
      typed-array-byte-length: 1.0.1
      typed-array-byte-offset: 1.0.2
      typed-array-length: 1.0.6
      unbox-primitive: 1.0.2
      which-typed-array: 1.1.15

  es-define-property@1.0.0:
    dependencies:
      get-intrinsic: 1.2.4

  es-errors@1.3.0: {}

  es-get-iterator@1.1.3:
    dependencies:
      call-bind: 1.0.7
      get-intrinsic: 1.2.4
      has-symbols: 1.0.3
      is-arguments: 1.1.1
      is-map: 2.0.3
      is-set: 2.0.3
      is-string: 1.0.7
      isarray: 2.0.5
      stop-iteration-iterator: 1.0.0

  es-iterator-helpers@1.0.19:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      es-set-tostringtag: 2.0.3
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      globalthis: 1.0.4
      has-property-descriptors: 1.0.2
      has-proto: 1.0.3
      has-symbols: 1.0.3
      internal-slot: 1.0.7
      iterator.prototype: 1.1.2
      safe-array-concat: 1.1.2

  es-object-atoms@1.0.0:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.0.3:
    dependencies:
      get-intrinsic: 1.2.4
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  es-shim-unscopables@1.0.2:
    dependencies:
      hasown: 2.0.2

  es-to-primitive@1.2.1:
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.0.5
      is-symbol: 1.0.4

  escape-string-regexp@1.0.5: {}

  escape-string-regexp@4.0.0: {}

  eslint-config-next@14.2.11(eslint@8.57.0)(typescript@5.6.2):
    dependencies:
      '@next/eslint-plugin-next': 14.2.11
      '@rushstack/eslint-patch': 1.10.4
      '@typescript-eslint/eslint-plugin': 5.62.0(@typescript-eslint/parser@5.62.0(eslint@8.57.0)(typescript@5.6.2))(eslint@8.57.0)(typescript@5.6.2)
      '@typescript-eslint/parser': 5.62.0(eslint@8.57.0)(typescript@5.6.2)
      eslint: 8.57.0
      eslint-import-resolver-node: 0.3.9
      eslint-import-resolver-typescript: 3.6.3(@typescript-eslint/parser@5.62.0(eslint@8.57.0)(typescript@5.6.2))(eslint-import-resolver-node@0.3.9)(eslint-plugin-import@2.30.0)(eslint@8.57.0)
      eslint-plugin-import: 2.30.0(@typescript-eslint/parser@5.62.0(eslint@8.57.0)(typescript@5.6.2))(eslint-import-resolver-typescript@3.6.3)(eslint@8.57.0)
      eslint-plugin-jsx-a11y: 6.10.0(eslint@8.57.0)
      eslint-plugin-react: 7.36.1(eslint@8.57.0)
      eslint-plugin-react-hooks: 4.6.2(eslint@8.57.0)
    optionalDependencies:
      typescript: 5.6.2
    transitivePeerDependencies:
      - eslint-import-resolver-webpack
      - eslint-plugin-import-x
      - supports-color

  eslint-config-prettier@8.10.0(eslint@8.57.0):
    dependencies:
      eslint: 8.57.0

  eslint-import-resolver-node@0.3.9:
    dependencies:
      debug: 3.2.7
      is-core-module: 2.15.1
      resolve: 1.22.8
    transitivePeerDependencies:
      - supports-color

  eslint-import-resolver-typescript@3.6.3(@typescript-eslint/parser@5.62.0(eslint@8.57.0)(typescript@5.6.2))(eslint-import-resolver-node@0.3.9)(eslint-plugin-import@2.30.0)(eslint@8.57.0):
    dependencies:
      '@nolyfill/is-core-module': 1.0.39
      debug: 4.3.7
      enhanced-resolve: 5.17.1
      eslint: 8.57.0
      eslint-module-utils: 2.11.0(@typescript-eslint/parser@5.62.0(eslint@8.57.0)(typescript@5.6.2))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.6.3)(eslint@8.57.0)
      fast-glob: 3.3.2
      get-tsconfig: 4.8.1
      is-bun-module: 1.2.1
      is-glob: 4.0.3
    optionalDependencies:
      eslint-plugin-import: 2.30.0(@typescript-eslint/parser@5.62.0(eslint@8.57.0)(typescript@5.6.2))(eslint-import-resolver-typescript@3.6.3)(eslint@8.57.0)
    transitivePeerDependencies:
      - '@typescript-eslint/parser'
      - eslint-import-resolver-node
      - eslint-import-resolver-webpack
      - supports-color

  eslint-module-utils@2.11.0(@typescript-eslint/parser@5.62.0(eslint@8.57.0)(typescript@5.6.2))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.6.3)(eslint@8.57.0):
    dependencies:
      debug: 3.2.7
    optionalDependencies:
      '@typescript-eslint/parser': 5.62.0(eslint@8.57.0)(typescript@5.6.2)
      eslint: 8.57.0
      eslint-import-resolver-node: 0.3.9
      eslint-import-resolver-typescript: 3.6.3(@typescript-eslint/parser@5.62.0(eslint@8.57.0)(typescript@5.6.2))(eslint-import-resolver-node@0.3.9)(eslint-plugin-import@2.30.0)(eslint@8.57.0)
    transitivePeerDependencies:
      - supports-color

  eslint-plugin-emotion@10.0.27: {}

  eslint-plugin-import@2.30.0(@typescript-eslint/parser@5.62.0(eslint@8.57.0)(typescript@5.6.2))(eslint-import-resolver-typescript@3.6.3)(eslint@8.57.0):
    dependencies:
      '@rtsao/scc': 1.1.0
      array-includes: 3.1.8
      array.prototype.findlastindex: 1.2.5
      array.prototype.flat: 1.3.2
      array.prototype.flatmap: 1.3.2
      debug: 3.2.7
      doctrine: 2.1.0
      eslint: 8.57.0
      eslint-import-resolver-node: 0.3.9
      eslint-module-utils: 2.11.0(@typescript-eslint/parser@5.62.0(eslint@8.57.0)(typescript@5.6.2))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.6.3)(eslint@8.57.0)
      hasown: 2.0.2
      is-core-module: 2.15.1
      is-glob: 4.0.3
      minimatch: 3.1.2
      object.fromentries: 2.0.8
      object.groupby: 1.0.3
      object.values: 1.2.0
      semver: 6.3.1
      tsconfig-paths: 3.15.0
    optionalDependencies:
      '@typescript-eslint/parser': 5.62.0(eslint@8.57.0)(typescript@5.6.2)
    transitivePeerDependencies:
      - eslint-import-resolver-typescript
      - eslint-import-resolver-webpack
      - supports-color

  eslint-plugin-jsx-a11y@6.10.0(eslint@8.57.0):
    dependencies:
      aria-query: 5.1.3
      array-includes: 3.1.8
      array.prototype.flatmap: 1.3.2
      ast-types-flow: 0.0.8
      axe-core: 4.10.0
      axobject-query: 4.1.0
      damerau-levenshtein: 1.0.8
      emoji-regex: 9.2.2
      es-iterator-helpers: 1.0.19
      eslint: 8.57.0
      hasown: 2.0.2
      jsx-ast-utils: 3.3.5
      language-tags: 1.0.9
      minimatch: 3.1.2
      object.fromentries: 2.0.8
      safe-regex-test: 1.0.3
      string.prototype.includes: 2.0.0

  eslint-plugin-react-hooks@4.6.2(eslint@8.57.0):
    dependencies:
      eslint: 8.57.0

  eslint-plugin-react@7.36.1(eslint@8.57.0):
    dependencies:
      array-includes: 3.1.8
      array.prototype.findlast: 1.2.5
      array.prototype.flatmap: 1.3.2
      array.prototype.tosorted: 1.1.4
      doctrine: 2.1.0
      es-iterator-helpers: 1.0.19
      eslint: 8.57.0
      estraverse: 5.3.0
      hasown: 2.0.2
      jsx-ast-utils: 3.3.5
      minimatch: 3.1.2
      object.entries: 1.1.8
      object.fromentries: 2.0.8
      object.values: 1.2.0
      prop-types: 15.8.1
      resolve: 2.0.0-next.5
      semver: 6.3.1
      string.prototype.matchall: 4.0.11
      string.prototype.repeat: 1.0.0

  eslint-scope@5.1.1:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0

  eslint-scope@7.2.2:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-visitor-keys@3.4.3: {}

  eslint@8.57.0:
    dependencies:
      '@eslint-community/eslint-utils': 4.4.0(eslint@8.57.0)
      '@eslint-community/regexpp': 4.11.1
      '@eslint/eslintrc': 2.1.4
      '@eslint/js': 8.57.0
      '@humanwhocodes/config-array': 0.11.14
      '@humanwhocodes/module-importer': 1.0.1
      '@nodelib/fs.walk': 1.2.8
      '@ungap/structured-clone': 1.2.0
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.3
      debug: 4.3.7
      doctrine: 3.0.0
      escape-string-regexp: 4.0.0
      eslint-scope: 7.2.2
      eslint-visitor-keys: 3.4.3
      espree: 9.6.1
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 6.0.1
      find-up: 5.0.0
      glob-parent: 6.0.2
      globals: 13.24.0
      graphemer: 1.4.0
      ignore: 5.3.2
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      is-path-inside: 3.0.3
      js-yaml: 4.1.0
      json-stable-stringify-without-jsonify: 1.0.1
      levn: 0.4.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
      strip-ansi: 6.0.1
      text-table: 0.2.0
    transitivePeerDependencies:
      - supports-color

  espree@9.6.1:
    dependencies:
      acorn: 8.12.1
      acorn-jsx: 5.3.2(acorn@8.12.1)
      eslint-visitor-keys: 3.4.3

  esquery@1.6.0:
    dependencies:
      estraverse: 5.3.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@4.3.0: {}

  estraverse@5.3.0: {}

  esutils@2.0.3: {}

  event-target-shim@5.0.1: {}

  eventemitter3@4.0.7: {}

  fast-deep-equal@3.1.3: {}

  fast-equals@5.0.1: {}

  fast-glob@3.3.2:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  fast-json-stable-stringify@2.1.0: {}

  fast-levenshtein@2.0.6: {}

  fast-xml-parser@4.4.1:
    dependencies:
      strnum: 1.0.5

  fastq@1.17.1:
    dependencies:
      reusify: 1.0.4

  fault@1.0.4:
    dependencies:
      format: 0.2.2

  file-entry-cache@6.0.1:
    dependencies:
      flat-cache: 3.2.0

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  find-root@1.1.0: {}

  find-up@5.0.0:
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0

  flat-cache@3.2.0:
    dependencies:
      flatted: 3.3.1
      keyv: 4.5.4
      rimraf: 3.0.2

  flatted@3.3.1: {}

  focus-lock@1.3.5:
    dependencies:
      tslib: 2.7.0

  follow-redirects@1.15.9: {}

  for-each@0.3.3:
    dependencies:
      is-callable: 1.2.7

  foreground-child@3.3.0:
    dependencies:
      cross-spawn: 7.0.3
      signal-exit: 4.1.0

  form-data-encoder@1.7.2: {}

  form-data@3.0.1:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  form-data@4.0.0:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  format@0.2.2: {}

  formdata-node@4.4.1:
    dependencies:
      node-domexception: 1.0.0
      web-streams-polyfill: 4.0.0-beta.3

  framer-motion@10.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      tslib: 2.7.0
    optionalDependencies:
      '@emotion/is-prop-valid': 0.8.8
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  framesync@6.1.2:
    dependencies:
      tslib: 2.4.0

  fs.realpath@1.0.0: {}

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  function.prototype.name@1.1.6:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      functions-have-names: 1.2.3

  functions-have-names@1.2.3: {}

  get-intrinsic@1.2.4:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2
      has-proto: 1.0.3
      has-symbols: 1.0.3
      hasown: 2.0.2

  get-nonce@1.0.1: {}

  get-symbol-description@1.0.2:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      get-intrinsic: 1.2.4

  get-tsconfig@4.8.1:
    dependencies:
      resolve-pkg-maps: 1.0.0

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  glob-to-regexp@0.4.1: {}

  glob@10.3.10:
    dependencies:
      foreground-child: 3.3.0
      jackspeak: 2.3.6
      minimatch: 9.0.5
      minipass: 7.1.2
      path-scurry: 1.11.1

  glob@10.4.5:
    dependencies:
      foreground-child: 3.3.0
      jackspeak: 3.4.3
      minimatch: 9.0.5
      minipass: 7.1.2
      package-json-from-dist: 1.0.0
      path-scurry: 1.11.1

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  globals@11.12.0: {}

  globals@13.24.0:
    dependencies:
      type-fest: 0.20.2

  globalthis@1.0.4:
    dependencies:
      define-properties: 1.2.1
      gopd: 1.0.1

  globby@11.1.0:
    dependencies:
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.3.2
      ignore: 5.3.2
      merge2: 1.4.1
      slash: 3.0.0

  gopd@1.0.1:
    dependencies:
      get-intrinsic: 1.2.4

  graceful-fs@4.2.11: {}

  graphemer@1.4.0: {}

  hammerjs@2.0.8: {}

  has-bigints@1.0.2: {}

  has-flag@3.0.0: {}

  has-flag@4.0.0: {}

  has-property-descriptors@1.0.2:
    dependencies:
      es-define-property: 1.0.0

  has-proto@1.0.3: {}

  has-symbols@1.0.3: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.0.3

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  hast-util-parse-selector@2.2.5: {}

  hastscript@6.0.0:
    dependencies:
      '@types/hast': 2.3.10
      comma-separated-tokens: 1.0.8
      hast-util-parse-selector: 2.2.5
      property-information: 5.6.0
      space-separated-tokens: 1.1.5

  highlight.js@10.7.3: {}

  hoist-non-react-statics@3.3.2:
    dependencies:
      react-is: 16.13.1

  humanize-ms@1.2.1:
    dependencies:
      ms: 2.1.3

  ignore@5.3.2: {}

  immutable@4.3.7: {}

  import-fresh@3.3.0:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  imurmurhash@0.1.4: {}

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.4: {}

  internal-slot@1.0.7:
    dependencies:
      es-errors: 1.3.0
      hasown: 2.0.2
      side-channel: 1.0.6

  internmap@2.0.3: {}

  invariant@2.2.4:
    dependencies:
      loose-envify: 1.4.0

  is-alphabetical@1.0.4: {}

  is-alphanumerical@1.0.4:
    dependencies:
      is-alphabetical: 1.0.4
      is-decimal: 1.0.4

  is-arguments@1.1.1:
    dependencies:
      call-bind: 1.0.7
      has-tostringtag: 1.0.2

  is-array-buffer@3.0.4:
    dependencies:
      call-bind: 1.0.7
      get-intrinsic: 1.2.4

  is-arrayish@0.2.1: {}

  is-arrayish@0.3.2:
    optional: true

  is-async-function@2.0.0:
    dependencies:
      has-tostringtag: 1.0.2

  is-bigint@1.0.4:
    dependencies:
      has-bigints: 1.0.2

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.3.0

  is-boolean-object@1.1.2:
    dependencies:
      call-bind: 1.0.7
      has-tostringtag: 1.0.2

  is-bun-module@1.2.1:
    dependencies:
      semver: 7.6.3

  is-callable@1.2.7: {}

  is-core-module@2.15.1:
    dependencies:
      hasown: 2.0.2

  is-data-view@1.0.1:
    dependencies:
      is-typed-array: 1.1.13

  is-date-object@1.0.5:
    dependencies:
      has-tostringtag: 1.0.2

  is-decimal@1.0.4: {}

  is-extglob@2.1.1: {}

  is-finalizationregistry@1.0.2:
    dependencies:
      call-bind: 1.0.7

  is-fullwidth-code-point@3.0.0: {}

  is-generator-function@1.0.10:
    dependencies:
      has-tostringtag: 1.0.2

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-hexadecimal@1.0.4: {}

  is-map@2.0.3: {}

  is-negative-zero@2.0.3: {}

  is-number-object@1.0.7:
    dependencies:
      has-tostringtag: 1.0.2

  is-number@7.0.0: {}

  is-path-inside@3.0.3: {}

  is-regex@1.1.4:
    dependencies:
      call-bind: 1.0.7
      has-tostringtag: 1.0.2

  is-set@2.0.3: {}

  is-shared-array-buffer@1.0.3:
    dependencies:
      call-bind: 1.0.7

  is-string@1.0.7:
    dependencies:
      has-tostringtag: 1.0.2

  is-symbol@1.0.4:
    dependencies:
      has-symbols: 1.0.3

  is-typed-array@1.1.13:
    dependencies:
      which-typed-array: 1.1.15

  is-weakmap@2.0.2: {}

  is-weakref@1.0.2:
    dependencies:
      call-bind: 1.0.7

  is-weakset@2.0.3:
    dependencies:
      call-bind: 1.0.7
      get-intrinsic: 1.2.4

  isarray@2.0.5: {}

  isexe@2.0.0: {}

  iterator.prototype@1.1.2:
    dependencies:
      define-properties: 1.2.1
      get-intrinsic: 1.2.4
      has-symbols: 1.0.3
      reflect.getprototypeof: 1.0.6
      set-function-name: 2.0.2

  its-fine@1.2.5(react@18.3.1):
    dependencies:
      '@types/react-reconciler': 0.28.8
      react: 18.3.1

  jackspeak@2.3.6:
    dependencies:
      '@isaacs/cliui': 8.0.2
    optionalDependencies:
      '@pkgjs/parseargs': 0.11.0

  jackspeak@3.4.3:
    dependencies:
      '@isaacs/cliui': 8.0.2
    optionalDependencies:
      '@pkgjs/parseargs': 0.11.0

  jiti@1.21.6: {}

  js-cookie@3.0.1: {}

  js-tokens@4.0.0: {}

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsesc@2.5.2: {}

  json-buffer@3.0.1: {}

  json-parse-even-better-errors@2.3.1: {}

  json-schema-traverse@0.4.1: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json5@1.0.2:
    dependencies:
      minimist: 1.2.8

  jsx-ast-utils@3.3.5:
    dependencies:
      array-includes: 3.1.8
      array.prototype.flat: 1.3.2
      object.assign: 4.1.5
      object.values: 1.2.0

  keyv@4.5.4:
    dependencies:
      json-buffer: 3.0.1

  konva@8.4.3: {}

  language-subtag-registry@0.3.23: {}

  language-tags@1.0.9:
    dependencies:
      language-subtag-registry: 0.3.23

  levn@0.4.1:
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0

  lilconfig@2.1.0: {}

  lilconfig@3.1.2: {}

  lines-and-columns@1.2.4: {}

  locate-path@6.0.0:
    dependencies:
      p-locate: 5.0.0

  lodash.merge@4.6.2: {}

  lodash.mergewith@4.6.2: {}

  lodash@4.17.21: {}

  logrocket@8.1.3: {}

  loose-envify@1.4.0:
    dependencies:
      js-tokens: 4.0.0

  lower-case@2.0.2:
    dependencies:
      tslib: 2.7.0

  lowlight@1.20.0:
    dependencies:
      fault: 1.0.4
      highlight.js: 10.7.3

  lru-cache@10.4.3: {}

  lucide-react@0.358.0(react@18.3.1):
    dependencies:
      react: 18.3.1

  luxon@3.5.0: {}

  mailgun.js@10.2.3:
    dependencies:
      axios: 1.7.7
      base-64: 1.0.0
      url-join: 4.0.1
    transitivePeerDependencies:
      - debug

  map-obj@4.3.0: {}

  merge2@1.4.1: {}

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mini-svg-data-uri@1.4.4: {}

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimatch@9.0.5:
    dependencies:
      brace-expansion: 2.0.1

  minimist@1.2.8: {}

  minipass@7.1.2: {}

  moment@2.30.1: {}

  ms@2.1.3: {}

  mz@2.7.0:
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0

  nanoid@3.3.7: {}

  nanoid@5.0.7: {}

  natural-compare-lite@1.4.0: {}

  natural-compare@1.4.0: {}

  next@15.4.0-canary.61(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(sass@1.78.0):
    dependencies:
      '@next/env': 15.4.0-canary.61
      '@swc/helpers': 0.5.15
      caniuse-lite: 1.0.30001660
      postcss: 8.4.31
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      styled-jsx: 5.1.6(react@18.3.1)
    optionalDependencies:
      '@next/swc-darwin-arm64': 15.4.0-canary.61
      '@next/swc-darwin-x64': 15.4.0-canary.61
      '@next/swc-linux-arm64-gnu': 15.4.0-canary.61
      '@next/swc-linux-arm64-musl': 15.4.0-canary.61
      '@next/swc-linux-x64-gnu': 15.4.0-canary.61
      '@next/swc-linux-x64-musl': 15.4.0-canary.61
      '@next/swc-win32-arm64-msvc': 15.4.0-canary.61
      '@next/swc-win32-x64-msvc': 15.4.0-canary.61
      sass: 1.78.0
      sharp: 0.34.2
    transitivePeerDependencies:
      - '@babel/core'
      - babel-plugin-macros

  no-case@3.0.4:
    dependencies:
      lower-case: 2.0.2
      tslib: 2.7.0

  node-domexception@1.0.0: {}

  node-fetch-native@1.0.1: {}

  node-fetch@2.7.0:
    dependencies:
      whatwg-url: 5.0.0

  normalize-path@3.0.0: {}

  object-assign@4.1.1: {}

  object-hash@3.0.0: {}

  object-inspect@1.13.2: {}

  object-is@1.1.6:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1

  object-keys@1.1.1: {}

  object.assign@4.1.5:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      has-symbols: 1.0.3
      object-keys: 1.1.1

  object.entries@1.1.8:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-object-atoms: 1.0.0

  object.fromentries@2.0.8:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-object-atoms: 1.0.0

  object.groupby@1.0.3:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3

  object.values@1.2.0:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-object-atoms: 1.0.0

  obuf@1.1.2: {}

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  openai@4.61.0:
    dependencies:
      '@types/node': 18.19.50
      '@types/node-fetch': 2.6.11
      '@types/qs': 6.9.16
      abort-controller: 3.0.0
      agentkeepalive: 4.5.0
      form-data-encoder: 1.7.2
      formdata-node: 4.4.1
      node-fetch: 2.7.0
      qs: 6.13.0
    transitivePeerDependencies:
      - encoding

  optionator@0.9.4:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-locate@5.0.0:
    dependencies:
      p-limit: 3.1.0

  package-json-from-dist@1.0.0: {}

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parse-entities@2.0.0:
    dependencies:
      character-entities: 1.2.4
      character-entities-legacy: 1.1.4
      character-reference-invalid: 1.1.4
      is-alphanumerical: 1.0.4
      is-decimal: 1.0.4
      is-hexadecimal: 1.0.4

  parse-json@5.2.0:
    dependencies:
      '@babel/code-frame': 7.24.7
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  path-exists@4.0.0: {}

  path-is-absolute@1.0.1: {}

  path-key@3.1.1: {}

  path-parse@1.0.7: {}

  path-scurry@1.11.1:
    dependencies:
      lru-cache: 10.4.3
      minipass: 7.1.2

  path-type@4.0.0: {}

  pg-cloudflare@1.2.5:
    optional: true

  pg-connection-string@2.8.5: {}

  pg-int8@1.0.1: {}

  pg-numeric@1.0.2: {}

  pg-pool@3.9.6(pg@8.15.6):
    dependencies:
      pg: 8.15.6

  pg-protocol@1.9.5: {}

  pg-types@2.2.0:
    dependencies:
      pg-int8: 1.0.1
      postgres-array: 2.0.0
      postgres-bytea: 1.0.0
      postgres-date: 1.0.7
      postgres-interval: 1.2.0

  pg-types@4.0.2:
    dependencies:
      pg-int8: 1.0.1
      pg-numeric: 1.0.2
      postgres-array: 3.0.4
      postgres-bytea: 3.0.0
      postgres-date: 2.1.0
      postgres-interval: 3.0.0
      postgres-range: 1.1.4

  pg@8.15.6:
    dependencies:
      pg-connection-string: 2.8.5
      pg-pool: 3.9.6(pg@8.15.6)
      pg-protocol: 1.9.5
      pg-types: 2.2.0
      pgpass: 1.0.5
    optionalDependencies:
      pg-cloudflare: 1.2.5

  pgpass@1.0.5:
    dependencies:
      split2: 4.2.0

  picocolors@1.1.0: {}

  picomatch@2.3.1: {}

  pify@2.3.0: {}

  pirates@4.0.6: {}

  possible-typed-array-names@1.0.0: {}

  postcss-import@15.1.0(postcss@8.4.47):
    dependencies:
      postcss: 8.4.47
      postcss-value-parser: 4.2.0
      read-cache: 1.0.0
      resolve: 1.22.8

  postcss-js@4.0.1(postcss@8.4.47):
    dependencies:
      camelcase-css: 2.0.1
      postcss: 8.4.47

  postcss-load-config@4.0.2(postcss@8.4.47):
    dependencies:
      lilconfig: 3.1.2
      yaml: 2.5.1
    optionalDependencies:
      postcss: 8.4.47

  postcss-nested@6.2.0(postcss@8.4.47):
    dependencies:
      postcss: 8.4.47
      postcss-selector-parser: 6.1.2

  postcss-selector-parser@6.1.2:
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  postcss-value-parser@4.2.0: {}

  postcss@8.4.31:
    dependencies:
      nanoid: 3.3.7
      picocolors: 1.1.0
      source-map-js: 1.2.1

  postcss@8.4.38:
    dependencies:
      nanoid: 3.3.7
      picocolors: 1.1.0
      source-map-js: 1.2.1

  postcss@8.4.47:
    dependencies:
      nanoid: 3.3.7
      picocolors: 1.1.0
      source-map-js: 1.2.1

  postgres-array@2.0.0: {}

  postgres-array@3.0.4: {}

  postgres-bytea@1.0.0: {}

  postgres-bytea@3.0.0:
    dependencies:
      obuf: 1.1.2

  postgres-date@1.0.7: {}

  postgres-date@2.1.0: {}

  postgres-interval@1.2.0:
    dependencies:
      xtend: 4.0.2

  postgres-interval@3.0.0: {}

  postgres-range@1.1.4: {}

  prelude-ls@1.2.1: {}

  prettier@2.8.8: {}

  prismjs@1.27.0: {}

  prismjs@1.29.0: {}

  prop-types@15.8.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  property-information@5.6.0:
    dependencies:
      xtend: 4.0.2

  proxy-from-env@1.1.0: {}

  punycode@2.3.1: {}

  pvtsutils@1.3.5:
    dependencies:
      tslib: 2.7.0

  pvutils@1.1.3: {}

  qs@6.13.0:
    dependencies:
      side-channel: 1.0.6

  queue-microtask@1.2.3: {}

  quick-lru@4.0.1: {}

  react-clientside-effect@1.2.6(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.25.6
      react: 18.3.1

  react-day-picker@8.10.1(date-fns@3.6.0)(react@18.3.1):
    dependencies:
      date-fns: 3.6.0
      react: 18.3.1

  react-dom@18.3.1(react@18.3.1):
    dependencies:
      loose-envify: 1.4.0
      react: 18.3.1
      scheduler: 0.23.2

  react-fast-compare@3.2.2: {}

  react-focus-lock@2.13.2(@types/react@18.3.5)(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.25.6
      focus-lock: 1.3.5
      prop-types: 15.8.1
      react: 18.3.1
      react-clientside-effect: 1.2.6(react@18.3.1)
      use-callback-ref: 1.3.2(@types/react@18.3.5)(react@18.3.1)
      use-sidecar: 1.1.2(@types/react@18.3.5)(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5

  react-hook-form@7.53.0(react@18.3.1):
    dependencies:
      react: 18.3.1

  react-is@16.13.1: {}

  react-is@18.3.1: {}

  react-konva-utils@0.3.2(konva@8.4.3)(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      konva: 8.4.3
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-konva: 18.2.10(konva@8.4.3)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      use-image: 1.1.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)

  react-konva@18.2.10(konva@8.4.3)(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@types/react-reconciler': 0.28.8
      its-fine: 1.2.5(react@18.3.1)
      konva: 8.4.3
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-reconciler: 0.29.2(react@18.3.1)
      scheduler: 0.23.2

  react-reconciler@0.29.2(react@18.3.1):
    dependencies:
      loose-envify: 1.4.0
      react: 18.3.1
      scheduler: 0.23.2

  react-remove-scroll-bar@2.3.6(@types/react@18.3.5)(react@18.3.1):
    dependencies:
      react: 18.3.1
      react-style-singleton: 2.2.1(@types/react@18.3.5)(react@18.3.1)
      tslib: 2.7.0
    optionalDependencies:
      '@types/react': 18.3.5

  react-remove-scroll@2.5.5(@types/react@18.3.5)(react@18.3.1):
    dependencies:
      react: 18.3.1
      react-remove-scroll-bar: 2.3.6(@types/react@18.3.5)(react@18.3.1)
      react-style-singleton: 2.2.1(@types/react@18.3.5)(react@18.3.1)
      tslib: 2.7.0
      use-callback-ref: 1.3.2(@types/react@18.3.5)(react@18.3.1)
      use-sidecar: 1.1.2(@types/react@18.3.5)(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5

  react-remove-scroll@2.5.7(@types/react@18.3.5)(react@18.3.1):
    dependencies:
      react: 18.3.1
      react-remove-scroll-bar: 2.3.6(@types/react@18.3.5)(react@18.3.1)
      react-style-singleton: 2.2.1(@types/react@18.3.5)(react@18.3.1)
      tslib: 2.7.0
      use-callback-ref: 1.3.2(@types/react@18.3.5)(react@18.3.1)
      use-sidecar: 1.1.2(@types/react@18.3.5)(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5

  react-remove-scroll@2.6.0(@types/react@18.3.5)(react@18.3.1):
    dependencies:
      react: 18.3.1
      react-remove-scroll-bar: 2.3.6(@types/react@18.3.5)(react@18.3.1)
      react-style-singleton: 2.2.1(@types/react@18.3.5)(react@18.3.1)
      tslib: 2.7.0
      use-callback-ref: 1.3.2(@types/react@18.3.5)(react@18.3.1)
      use-sidecar: 1.1.2(@types/react@18.3.5)(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5

  react-smooth@4.0.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      fast-equals: 5.0.1
      prop-types: 15.8.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-transition-group: 4.4.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)

  react-style-singleton@2.2.1(@types/react@18.3.5)(react@18.3.1):
    dependencies:
      get-nonce: 1.0.1
      invariant: 2.2.4
      react: 18.3.1
      tslib: 2.7.0
    optionalDependencies:
      '@types/react': 18.3.5

  react-syntax-highlighter@15.5.0(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.25.6
      highlight.js: 10.7.3
      lowlight: 1.20.0
      prismjs: 1.29.0
      react: 18.3.1
      refractor: 3.6.0

  react-transition-group@4.4.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.25.6
      dom-helpers: 5.2.1
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  react-transition-state@2.1.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  react@18.3.1:
    dependencies:
      loose-envify: 1.4.0

  reactflow@11.11.4(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@reactflow/background': 11.3.14(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@reactflow/controls': 11.2.14(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@reactflow/core': 11.11.4(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@reactflow/minimap': 11.7.14(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@reactflow/node-resizer': 2.2.14(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@reactflow/node-toolbar': 1.3.14(@types/react@18.3.5)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - '@types/react'
      - immer

  read-cache@1.0.0:
    dependencies:
      pify: 2.3.0

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  recharts-scale@0.4.5:
    dependencies:
      decimal.js-light: 2.5.1

  recharts@2.12.7(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      clsx: 2.1.1
      eventemitter3: 4.0.7
      lodash: 4.17.21
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-is: 16.13.1
      react-smooth: 4.0.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      recharts-scale: 0.4.5
      tiny-invariant: 1.3.3
      victory-vendor: 36.9.2

  reflect.getprototypeof@1.0.6:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      get-intrinsic: 1.2.4
      globalthis: 1.0.4
      which-builtin-type: 1.1.4

  refractor@3.6.0:
    dependencies:
      hastscript: 6.0.0
      parse-entities: 2.0.0
      prismjs: 1.27.0

  regenerator-runtime@0.14.1: {}

  regexp.prototype.flags@1.5.2:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-errors: 1.3.0
      set-function-name: 2.0.2

  resolve-from@4.0.0: {}

  resolve-pkg-maps@1.0.0: {}

  resolve@1.22.8:
    dependencies:
      is-core-module: 2.15.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  resolve@2.0.0-next.5:
    dependencies:
      is-core-module: 2.15.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  reusify@1.0.4: {}

  rimraf@3.0.2:
    dependencies:
      glob: 7.2.3

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  safe-array-concat@1.1.2:
    dependencies:
      call-bind: 1.0.7
      get-intrinsic: 1.2.4
      has-symbols: 1.0.3
      isarray: 2.0.5

  safe-regex-test@1.0.3:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-regex: 1.1.4

  sass@1.78.0:
    dependencies:
      chokidar: 3.6.0
      immutable: 4.3.7
      source-map-js: 1.2.1

  scheduler@0.23.2:
    dependencies:
      loose-envify: 1.4.0

  semver@6.3.1: {}

  semver@7.6.3: {}

  semver@7.7.2:
    optional: true

  set-function-length@1.2.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      gopd: 1.0.1
      has-property-descriptors: 1.0.2

  set-function-name@2.0.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      functions-have-names: 1.2.3
      has-property-descriptors: 1.0.2

  shallowequal@1.1.0: {}

  sharp@0.34.2:
    dependencies:
      color: 4.2.3
      detect-libc: 2.0.4
      semver: 7.7.2
    optionalDependencies:
      '@img/sharp-darwin-arm64': 0.34.2
      '@img/sharp-darwin-x64': 0.34.2
      '@img/sharp-libvips-darwin-arm64': 1.1.0
      '@img/sharp-libvips-darwin-x64': 1.1.0
      '@img/sharp-libvips-linux-arm': 1.1.0
      '@img/sharp-libvips-linux-arm64': 1.1.0
      '@img/sharp-libvips-linux-ppc64': 1.1.0
      '@img/sharp-libvips-linux-s390x': 1.1.0
      '@img/sharp-libvips-linux-x64': 1.1.0
      '@img/sharp-libvips-linuxmusl-arm64': 1.1.0
      '@img/sharp-libvips-linuxmusl-x64': 1.1.0
      '@img/sharp-linux-arm': 0.34.2
      '@img/sharp-linux-arm64': 0.34.2
      '@img/sharp-linux-s390x': 0.34.2
      '@img/sharp-linux-x64': 0.34.2
      '@img/sharp-linuxmusl-arm64': 0.34.2
      '@img/sharp-linuxmusl-x64': 0.34.2
      '@img/sharp-wasm32': 0.34.2
      '@img/sharp-win32-arm64': 0.34.2
      '@img/sharp-win32-ia32': 0.34.2
      '@img/sharp-win32-x64': 0.34.2
    optional: true

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  side-channel@1.0.6:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      get-intrinsic: 1.2.4
      object-inspect: 1.13.2

  signal-exit@4.1.0: {}

  simple-swizzle@0.2.2:
    dependencies:
      is-arrayish: 0.3.2
    optional: true

  slash@3.0.0: {}

  snake-case@3.0.4:
    dependencies:
      dot-case: 3.0.4
      tslib: 2.7.0

  snakecase-keys@3.2.1:
    dependencies:
      map-obj: 4.3.0
      to-snake-case: 1.0.0

  snakecase-keys@5.4.4:
    dependencies:
      map-obj: 4.3.0
      snake-case: 3.0.4
      type-fest: 2.19.0

  source-map-js@1.2.1: {}

  source-map@0.5.7: {}

  space-separated-tokens@1.1.5: {}

  split2@4.2.0: {}

  stop-iteration-iterator@1.0.0:
    dependencies:
      internal-slot: 1.0.7

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string-width@5.1.2:
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0

  string.prototype.includes@2.0.0:
    dependencies:
      define-properties: 1.2.1
      es-abstract: 1.23.3

  string.prototype.matchall@4.0.11:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      get-intrinsic: 1.2.4
      gopd: 1.0.1
      has-symbols: 1.0.3
      internal-slot: 1.0.7
      regexp.prototype.flags: 1.5.2
      set-function-name: 2.0.2
      side-channel: 1.0.6

  string.prototype.repeat@1.0.0:
    dependencies:
      define-properties: 1.2.1
      es-abstract: 1.23.3

  string.prototype.trim@1.2.9:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-object-atoms: 1.0.0

  string.prototype.trimend@1.0.8:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-object-atoms: 1.0.0

  string.prototype.trimstart@1.0.8:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-object-atoms: 1.0.0

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-ansi@7.1.0:
    dependencies:
      ansi-regex: 6.1.0

  strip-bom@3.0.0: {}

  strip-json-comments@3.1.1: {}

  stripe@12.18.0:
    dependencies:
      '@types/node': 18.19.50
      qs: 6.13.0

  strnum@1.0.5: {}

  styled-components@6.1.13(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@emotion/is-prop-valid': 1.2.2
      '@emotion/unitless': 0.8.1
      '@types/stylis': 4.2.5
      css-to-react-native: 3.2.0
      csstype: 3.1.3
      postcss: 8.4.38
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      shallowequal: 1.1.0
      stylis: 4.3.2
      tslib: 2.6.2

  styled-jsx@5.1.6(react@18.3.1):
    dependencies:
      client-only: 0.0.1
      react: 18.3.1

  stylis@4.2.0: {}

  stylis@4.3.2: {}

  sucrase@3.35.0:
    dependencies:
      '@jridgewell/gen-mapping': 0.3.5
      commander: 4.1.1
      glob: 10.4.5
      lines-and-columns: 1.2.4
      mz: 2.7.0
      pirates: 4.0.6
      ts-interface-checker: 0.1.13

  supports-color@5.5.0:
    dependencies:
      has-flag: 3.0.0

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  swr@2.2.0(react@18.3.1):
    dependencies:
      react: 18.3.1
      use-sync-external-store: 1.2.2(react@18.3.1)

  swr@2.2.5(react@18.3.1):
    dependencies:
      client-only: 0.0.1
      react: 18.3.1
      use-sync-external-store: 1.2.2(react@18.3.1)

  tabbable@6.2.0: {}

  tailwind-merge@1.14.0: {}

  tailwind-merge@2.5.2: {}

  tailwindcss-animate@1.0.7(tailwindcss@3.4.11):
    dependencies:
      tailwindcss: 3.4.11

  tailwindcss@3.4.11:
    dependencies:
      '@alloc/quick-lru': 5.2.0
      arg: 5.0.2
      chokidar: 3.6.0
      didyoumean: 1.2.2
      dlv: 1.1.3
      fast-glob: 3.3.2
      glob-parent: 6.0.2
      is-glob: 4.0.3
      jiti: 1.21.6
      lilconfig: 2.1.0
      micromatch: 4.0.8
      normalize-path: 3.0.0
      object-hash: 3.0.0
      picocolors: 1.1.0
      postcss: 8.4.47
      postcss-import: 15.1.0(postcss@8.4.47)
      postcss-js: 4.0.1(postcss@8.4.47)
      postcss-load-config: 4.0.2(postcss@8.4.47)
      postcss-nested: 6.2.0(postcss@8.4.47)
      postcss-selector-parser: 6.1.2
      resolve: 1.22.8
      sucrase: 3.35.0
    transitivePeerDependencies:
      - ts-node

  tapable@2.2.1: {}

  text-table@0.2.0: {}

  thenify-all@1.6.0:
    dependencies:
      thenify: 3.3.1

  thenify@3.3.1:
    dependencies:
      any-promise: 1.3.0

  tiny-invariant@1.3.3: {}

  to-fast-properties@2.0.0: {}

  to-no-case@1.0.2: {}

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  to-snake-case@1.0.0:
    dependencies:
      to-space-case: 1.0.0

  to-space-case@1.0.0:
    dependencies:
      to-no-case: 1.0.2

  toggle-selection@1.0.6: {}

  tr46@0.0.3: {}

  ts-interface-checker@0.1.13: {}

  tsconfig-paths@3.15.0:
    dependencies:
      '@types/json5': 0.0.29
      json5: 1.0.2
      minimist: 1.2.8
      strip-bom: 3.0.0

  tslib@1.14.1: {}

  tslib@2.4.0: {}

  tslib@2.4.1: {}

  tslib@2.6.2: {}

  tslib@2.7.0: {}

  tslib@2.8.1: {}

  tsutils@3.21.0(typescript@5.6.2):
    dependencies:
      tslib: 1.14.1
      typescript: 5.6.2

  type-check@0.4.0:
    dependencies:
      prelude-ls: 1.2.1

  type-fest@0.20.2: {}

  type-fest@2.19.0: {}

  typed-array-buffer@1.0.2:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-typed-array: 1.1.13

  typed-array-byte-length@1.0.1:
    dependencies:
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-proto: 1.0.3
      is-typed-array: 1.1.13

  typed-array-byte-offset@1.0.2:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-proto: 1.0.3
      is-typed-array: 1.1.13

  typed-array-length@1.0.6:
    dependencies:
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-proto: 1.0.3
      is-typed-array: 1.1.13
      possible-typed-array-names: 1.0.0

  typescript@5.6.2: {}

  unbox-primitive@1.0.2:
    dependencies:
      call-bind: 1.0.7
      has-bigints: 1.0.2
      has-symbols: 1.0.3
      which-boxed-primitive: 1.0.2

  undici-types@5.26.5: {}

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  url-join@4.0.1: {}

  use-callback-ref@1.3.2(@types/react@18.3.5)(react@18.3.1):
    dependencies:
      react: 18.3.1
      tslib: 2.7.0
    optionalDependencies:
      '@types/react': 18.3.5

  use-image@1.1.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  use-sidecar@1.1.2(@types/react@18.3.5)(react@18.3.1):
    dependencies:
      detect-node-es: 1.1.0
      react: 18.3.1
      tslib: 2.7.0
    optionalDependencies:
      '@types/react': 18.3.5

  use-sync-external-store@1.2.2(react@18.3.1):
    dependencies:
      react: 18.3.1

  util-deprecate@1.0.2: {}

  uuid@9.0.1: {}

  victory-vendor@36.9.2:
    dependencies:
      '@types/d3-array': 3.2.1
      '@types/d3-ease': 3.0.2
      '@types/d3-interpolate': 3.0.4
      '@types/d3-scale': 4.0.8
      '@types/d3-shape': 3.1.6
      '@types/d3-time': 3.0.3
      '@types/d3-timer': 3.0.2
      d3-array: 3.2.4
      d3-ease: 3.0.1
      d3-interpolate: 3.0.1
      d3-scale: 4.0.2
      d3-shape: 3.2.0
      d3-time: 3.1.0
      d3-timer: 3.0.1

  web-streams-polyfill@4.0.0-beta.3: {}

  webcrypto-core@1.8.0:
    dependencies:
      '@peculiar/asn1-schema': 2.3.13
      '@peculiar/json-schema': 1.1.12
      asn1js: 3.0.5
      pvtsutils: 1.3.5
      tslib: 2.7.0

  webidl-conversions@3.0.1: {}

  whatwg-url@5.0.0:
    dependencies:
      tr46: 0.0.3
      webidl-conversions: 3.0.1

  which-boxed-primitive@1.0.2:
    dependencies:
      is-bigint: 1.0.4
      is-boolean-object: 1.1.2
      is-number-object: 1.0.7
      is-string: 1.0.7
      is-symbol: 1.0.4

  which-builtin-type@1.1.4:
    dependencies:
      function.prototype.name: 1.1.6
      has-tostringtag: 1.0.2
      is-async-function: 2.0.0
      is-date-object: 1.0.5
      is-finalizationregistry: 1.0.2
      is-generator-function: 1.0.10
      is-regex: 1.1.4
      is-weakref: 1.0.2
      isarray: 2.0.5
      which-boxed-primitive: 1.0.2
      which-collection: 1.0.2
      which-typed-array: 1.1.15

  which-collection@1.0.2:
    dependencies:
      is-map: 2.0.3
      is-set: 2.0.3
      is-weakmap: 2.0.2
      is-weakset: 2.0.3

  which-typed-array@1.1.15:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-tostringtag: 1.0.2

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  word-wrap@1.2.5: {}

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@8.1.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0

  wrappy@1.0.2: {}

  xtend@4.0.2: {}

  yaml@1.10.2: {}

  yaml@2.5.1: {}

  yocto-queue@0.1.0: {}

  zustand@4.5.5(@types/react@18.3.5)(react@18.3.1):
    dependencies:
      use-sync-external-store: 1.2.2(react@18.3.1)
    optionalDependencies:
      '@types/react': 18.3.5
      react: 18.3.1
