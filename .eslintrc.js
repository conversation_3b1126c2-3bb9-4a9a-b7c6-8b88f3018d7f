module.exports = {
  extends: ['next', 'prettier', 'plugin:@typescript-eslint/recommended'],
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint', 'emotion'],
  root: true,
  env: {
    es6: true,
    browser: true,
    node: true,
    jest: true
  },
  globals: {
    Atomics: 'readonly',
    SharedArrayBuffer: 'readonly'
  },
  parserOptions: {
    ecmaFeatures: {
      jsx: true
    },
    ecmaVersion: 2018,
    sourceType: 'module'
  },
  rules: {
    'emotion/jsx-import': 'error',
    'import/prefer-default-export': 'off',
    'react-hooks/exhaustive-deps': 'off',
    'react-hooks/rule': 'off',
    'react/no-array-index-key': 'off',
    'react/no-deprecated': 'off',
    'react/require-default-props': 'off',
    'react/jsx-filename-extension': [
      1,
      { extensions: ['.js', '.jsx', '.tsx', '.ts'] }
    ],
    'jsx-a11y/href-no-hash': [0],
    'max-len': ['warn', { code: 1000 }],
    'no-alert': 'off',
    'react/jsx-props-no-spreading': 'off',
    'react/jsx-wrap-multilines': 'off',
    'react/forbid-prop-types': 'off',
    'react/button-has-type': 'off',
    'react/no-unescaped-entities': 'off',
    '@typescript-eslint/ban-ts-ignore': 'off',
    '@typescript-eslint/ban-ts-comment': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
    '@typescript-eslint/no-var-requires': 'off',
    '@typescript-eslint/no-non-null-assertion': 'off',
    '@next/next/no-img-element': 'off',
    'react/boolean-prop-naming': 'off',
    'no-unused-vars': ['off', { argsIgnorePattern: '^_' }],
    'no-console': 'off',
    'consistent-return': 'off',
    // Add this new rule
    '@typescript-eslint/no-unnecessary-condition': 'off'
  },
  // Add this overrides section
  overrides: [
    {
      files: ['flowsheets/**/*.ts', 'flowsheets/**/*.tsx'],
      rules: {
        '@typescript-eslint/no-unnecessary-condition': 'off',
        '@typescript-eslint/no-unused-vars': 'off'
      }
    }
  ]
};
