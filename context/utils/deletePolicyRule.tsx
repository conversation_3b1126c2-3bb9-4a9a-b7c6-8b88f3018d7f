import { IPolicy } from 'constants/security/policies';

export function _deleteRuleFromPolicy(policy: IPolicy, ruleId: string) {
  if (!ruleId) {
    throw new Error('ruleId is required');
  }

  if (!policy) {
    throw new Error('policy is required');
  }

  const updatedStatements: any = {};
  for (const category in policy.statements) {
    const rules = policy.statements[category].filter(
      (rule: any) => rule.ruleId !== ruleId
    );
    updatedStatements[category] = rules;
  }
  const newPolicy = {
    ...policy,
    statements: updatedStatements
  };

  return newPolicy;
}
