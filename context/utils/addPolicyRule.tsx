import { IPolicy, IPolicyRule } from '../../constants/security';

export const _addRuleToPolicy = (policy: IPolicy, rule: IPolicyRule) => {
  if (!policy) {
    throw new Error('[_addRuleToPolicy] policy is required');
  }

  if (!rule) {
    throw new Error('[_addRuleToPolicy] rule input is required');
  }

  const { ruleCategory, ruleId, name, label } = rule;

  const ruleAlreadyExists = policy.statements[ruleCategory].some(
    (rule: IPolicyRule) => rule.ruleId === ruleId
  );

  if (ruleAlreadyExists) {
    console.warn('[_addRuleToPolicy] rule already exists, skipping');
    return policy;
  } else {
    const newRule: IPolicyRule = {
      name: name,
      ruleId: ruleId,
      label: label,
      ruleCategory: ruleCategory
    };

    const newStatements = {
      ...policy.statements,
      [ruleCategory]: [...policy.statements[ruleCategory], newRule]
    };

    const newPolicy = {
      ...policy,
      statements: newStatements
    };

    return newPolicy;
  }
};
