import { createContext, useContext } from "react";

interface TemplateContextType {}

export const TemplateContext = createContext<TemplateContextType>(
  {} as TemplateContextType
);

interface ProviderProps {
  children: React.ReactNode;
}

const TemplateProvider = ({ children }: ProviderProps) => {
  return (
    <TemplateContext.Provider value={{}}>{children}</TemplateContext.Provider>
  );
};

export const useTemplate = () => {
  const context = useContext(TemplateContext);
  if (!context) {
    throw new Error("useTemplate must be used within an TemplateProvider");
  }
  return context;
};

export default TemplateProvider;
