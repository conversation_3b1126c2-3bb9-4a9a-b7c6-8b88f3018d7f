import React, { createContext, useContext, useMemo } from 'react';
import useHubspotChat from 'hooks/useHubspotChat';

const ChatContext = createContext(null);

export const ChatProvider = ({ children }) => {
  const chat = useHubspotChat();

  // Here's where you use useMemo
  const value = useMemo(
    () => ({
      chat
    }),
    [chat]
  );
  return <ChatContext.Provider value={value}>{children}</ChatContext.Provider>;
};

export const useChat = () => useContext(ChatContext);
