import { useUser as useClerkUser } from '@clerk/nextjs';
import React, { createContext, useContext, useEffect } from 'react';
import LogRocket from 'logrocket';

interface IUser {
  id: string;
  fullName?: string;
  email?: string;
  [key: string]: any;
}

export interface IUserContext {
  userId: string | null;
  user: IUser | null;
}

const UserContext = createContext<IUserContext | undefined>(undefined);

export const UserContextProvider: React.FC<{ children: React.ReactNode }> = ({
  children
}) => {
  const { isSignedIn, user } = useClerkUser();

  useEffect(() => {
    if (isSignedIn && user) {
      LogRocket.identify(user.id, {
        name: user.fullName,
        email: user.primaryEmailAddress?.emailAddress
      });
    }
  }, [isSignedIn, user]);

  const value: IUserContext = {
    userId: isSignedIn ? user?.id : null,
    user: isSignedIn
      ? {
          id: user?.id,
          fullName: user?.fullName,
          email: user?.primaryEmailAddress?.emailAddress,
          ...user
        }
      : null
  };

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
};

export const useUserContext = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUserContext must be used within a UserContextProvider');
  }
  return context;
};
