import { createContext, useContext, useState } from 'react';
import { Logs } from 'src/types';
import { useTeamsContext } from './TeamsContext';

interface ToolDetailsContextType {
  getDetails: (tool: {
    serviceName: string;
    toolPid: string;
    from: number;
    to: number;
  }) => Promise<Logs[]>;
}

export const ToolDetailsContext = createContext<ToolDetailsContextType>({
  getDetails: async () => {
    throw new Error('ToolDetailsContext not implemented');
  }
} as ToolDetailsContextType);

interface ProviderProps {
  children: React.ReactNode;
}

const ToolDetailsProvider = ({ children }: ProviderProps) => {
  const [details, setDetails] = useState<{
    [key: string]: { logs: Logs[]; lastUpdate: Date };
  }>({});

  const { teamId } = useTeamsContext();

  const getDetails = async (tool: {
    serviceName: string;
    toolPid: string;
    from: number;
    to: number;
  }) => {
    const { serviceName, toolPid, from, to } = tool;
    const key = `${serviceName}-${toolPid}`;

    const logs = await fetch(`/api/tool-details`, {
      body: JSON.stringify({
        serviceName,
        toolPid,
        start_unixDate: from,
        end_unixDate: to,
        teamId
      }),
      headers: {
        'Content-Type': 'application/json'
      },
      method: 'POST'
    }).then((res) => res.json());
    setDetails({
      ...details,
      [key]: {
        logs: [...logs, ...(details[key]?.logs || [])].sort(
          (a, b) => a.timestamp - b.timestamp
        ),
        lastUpdate: new Date()
      }
    });

    return logs;
  };

  return (
    <ToolDetailsContext.Provider
      value={{
        getDetails
      }}
    >
      {children}
    </ToolDetailsContext.Provider>
  );
};

export const useToolDetails = () => {
  const context = useContext(ToolDetailsContext);
  if (!context) {
    throw new Error(
      'useToolDetails must be used within an ToolDetailsProvider'
    );
  }
  return context;
};

export default ToolDetailsProvider;
