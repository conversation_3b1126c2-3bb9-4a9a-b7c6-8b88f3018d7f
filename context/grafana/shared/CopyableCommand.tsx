import { useState } from 'react';

export const CopyableCommand: React.FC<{ command: string }> = ({ command }) => {
  const [copied, setCopied] = useState(false);
  
  const handleCopy = () => {
    navigator.clipboard.writeText(command);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };
  
  return (
    <div 
      className="bg-gray-900 p-4 rounded-lg mb-4 relative group cursor-pointer"
      onClick={handleCopy}
    >
      <code className="text-white text-xs">{command}</code>
      <div 
        className={`absolute right-2 top-2 p-2 bg-gray-800 rounded transition-opacity ${copied ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'}`}
      >
        {copied ? (
          <span className="text-xs text-green-400">Copied!</span>
        ) : (
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect>
            <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path>
          </svg>
        )}
      </div>
    </div>
  );
};
