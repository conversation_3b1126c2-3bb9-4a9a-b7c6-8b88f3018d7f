import React, { useState } from 'react';
import { ShadButton } from '@/components/ui/ShadButton';
import { StepProps } from '../shared/types';
import { ConfettiComponent } from '@/components/ConfettiComponent';

export const ViewDemoStep: React.FC<StepProps & { grafanaUrl: string, showConfetti?: boolean }> = ({ 
  isActive, 
  grafanaUrl, 
  onComplete,
  isCompleted,
  showConfetti = false
}) => {
  const [isCompletedLocal, setIsCompletedLocal] = useState(false);
  const [showConfettiState, setShowConfettiState] = useState(false);
  
  const handleComplete = () => {
    setIsCompletedLocal(true);
    if (showConfetti) {
      setShowConfettiState(true);
    }
    onComplete();
  };
  
  if (!isActive) return null;
  
  return (
    <>
      <div className="text-white text-sm mb-4">
        <div className="flex flex-col space-y-3">
          <div className="flex items-start">
            <span className="inline-block w-5 mr-2 text-right">1.</span>
            <span>Open the Grafana dashboard using the button below</span>
          </div>
          <div className="flex items-start">
            <span className="inline-block w-5 mr-2 text-right">2.</span>
            <span>In Grafana, click 'Realtime Execution View' to see an overview of all running and completed pipelines</span>
          </div>
          <div className="flex items-start">
            <span className="inline-block w-5 mr-2 text-right">3.</span>
            <span>Find your pipeline named demo_{'{your_username}'} to see detailed metrics and pipeline status</span>
          </div>
        </div>
      </div>
      
      <div className="flex mt-4 space-x-4">
        <ShadButton 
          onClick={() => window.open(grafanaUrl, '_blank')}
          className="transition-transform duration-200 hover:scale-105 hover:shadow-lg"
        >
          Open Grafana Dashboard
          <img
            src="https://assets.streamlinehq.com/image/private/w_300,h_300,ar_1/f_auto/v1/icons/3/grafana-ipeuuhi0ws3fbikv7ojrk.png/grafana-625ktq51icaz2carazn0n.png?_a=DATAdtAAZAA0"
            alt="Grafana Logo"
            className="ml-2 w-6 h-6"
          />
        </ShadButton>
        
        <ShadButton
          onClick={handleComplete}
          className="transition-transform duration-200 hover:scale-105 hover:shadow-lg"
        >
          I've found my pipeline
        </ShadButton>
      </div>
      
      <ConfettiComponent isVisible={showConfettiState} />
    </>
  );
};
