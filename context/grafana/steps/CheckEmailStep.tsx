import React from 'react';
import { ShadButton } from '@/components/ui/ShadButton';
import { StepProps } from '../shared/types';

export const CheckEmailStep: React.FC<StepProps> = ({ isActive, onComplete }) => 
  isActive ? (
    <div className="space-y-4">
      <div className="text-white text-sm">
        <div className="flex flex-col space-y-3 mt-2">
          <div className="flex items-start">
            <span className="inline-block w-6 mr-2 text-right">1.</span>
            <span>Open the Grafana invite in your email inbox and click "Accept Invitation"</span>
          </div>
          <div className="flex items-start">
            <span className="inline-block w-6 mr-2 text-right">2.</span>
            <span>Sign up for <PERSON><PERSON> by entering your account details</span>
          </div>
          <div className="flex items-start">
            <span className="inline-block w-6 mr-2 text-right">3.</span>
            <span>Once you're done, return to this page and click the button below to continue to Step 5</span>
          </div>
        </div>
      </div>
      <ShadButton onClick={onComplete} className="mt-4">
        I've set up my Grafana account
      </ShadButton>
    </div>
  ) : null;
