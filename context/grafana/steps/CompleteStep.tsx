import { ShadButton } from '@/components/ui/ShadButton';
import { StepProps } from '../shared/types';
import { ConfettiComponent } from '@/components/ConfettiComponent';

export const CompleteStep: React.FC<StepProps & { grafanaUrl: string }> = ({ isActive, grafanaUrl }) =>
  isActive ? (
    <>
      <div className="text-white text-sm mb-4">
        Installation Complete! You can now open the Grafana dashboard to see your pipeline
      </div>
      <ShadButton onClick={() => window.open(grafanaUrl, '_blank')}
        className="transition-transform duration-200 hover:scale-105 hover:shadow-lg"
      >
        Open Grafana Dashboard
        <img
          src="https://assets.streamlinehq.com/image/private/w_300,h_300,ar_1/f_auto/v1/icons/3/grafana-ipeuuhi0ws3fbikv7ojrk.png/grafana-625ktq51icaz2carazn0n.png?_a=DATAdtAAZAA0" // Replace with actual logo URL or path
          alt="Grafana Logo"
          className="ml-2 w-6 h-6"
        />
      </ShadButton>
      <ConfettiComponent
        isVisible={
          true
        }
      />
    </>
  ) : null;
