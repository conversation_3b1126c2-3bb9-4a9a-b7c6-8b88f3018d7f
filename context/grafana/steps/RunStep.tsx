import { useState, useEffect } from 'react';
import { ShadButton } from '@/components/ui/ShadButton';
import { CopyableCommand } from '../shared/CopyableCommand';
import { StepProps } from '../shared/types';

export const RunStep: React.FC<StepProps & { command: string }> = ({ isActive, command, email, username, onComplete, isCompleted }) => {
  const [isCompletedLocal, setIsCompletedLocal] = useState(false);
  
  // Debug log to check props
  useEffect(() => {
    if (isActive) {
      console.log("RunStep active with props:", { isCompleted, command, email });
    }
  }, [isActive, isCompleted, command, email]);

  const handleComplete = () => {
    console.log("Complete button clicked");
    setIsCompletedLocal(true);
    if (onComplete) {
      onComplete();
    } else {
      console.error("onComplete function is not defined");
    }
  };

  if (!isActive) return null;
  
  return (
    <>
      <div className="text-white text-sm mb-4">
        <div className="flex flex-col space-y-3">
          <div className="flex items-start">
            <span className="inline-block w-5 mr-2 text-right">1.</span>
            <span>Start running Tracer by entering the command below into the GitHub terminal</span>
          </div>
          <div className="flex items-start">
            <span className="inline-block w-5 mr-2 text-right">2.</span>
            <span>When you're ready, click "I've started Tracer" to continue</span>
          </div>
        </div>
      </div>
      
      <CopyableCommand command={command} />
      
      <ShadButton
        onClick={handleComplete}
        className="transition-transform duration-200 hover:scale-105 hover:shadow-lg mt-4"
      >
        I've started Tracer
      </ShadButton>
    </>
  );
};
