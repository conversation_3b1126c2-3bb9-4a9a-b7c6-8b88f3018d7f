import React, { useState } from 'react';
import { ShadButton } from '@/components/ui/ShadButton';
import { CopyableCommand } from '../shared/CopyableCommand';
import { StepProps } from '../shared/types';
import { ConfettiComponent } from '@/components/ConfettiComponent';

export const RunPipelineStep: React.FC<StepProps> = ({ 
  isActive, 
  onComplete,
  isCompleted 
}) => {
  const [isCompletedLocal, setIsCompletedLocal] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);
  const command = 'nextflow run nf-core/rnaseq -c custom.config -profile docker,test --outdir results -resume';
  
  const handleComplete = () => {
    setIsCompletedLocal(true);
    setShowConfetti(true);
    onComplete();
  };
  
  if (!isActive) return null;
  
  return (
    <>
      <div className="text-white text-sm mb-4">
        <div className="flex flex-col space-y-3">
          <div className="flex items-start">
            <span className="inline-block w-5 mr-2 text-right">1.</span>
            <span>Run your first real pipeline by entering the command below in the GitHub Openspace terminal</span>
          </div>
          <div className="flex items-start">
            <span className="inline-block w-5 mr-2 text-right">2.</span>
            <span>Nice work! You've just run Tracer from start to finish and are all set!<br />
              If you want to run different pipelines, please follow the additional steps in your GitHub Codespace.</span>
          </div>
        </div>
      </div>
      
      <CopyableCommand command={command} />
      
      {!isCompleted && !isCompletedLocal && (
        <ShadButton
          onClick={handleComplete}
          className="transition-transform duration-200 hover:scale-105 hover:shadow-lg mt-4"
        >
          I've run the pipeline
        </ShadButton>
      )}
      
      <ConfettiComponent isVisible={showConfetti} />
    </>
  );
};

