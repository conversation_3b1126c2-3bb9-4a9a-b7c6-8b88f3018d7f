import React, { useState } from 'react';
import { ShadButton } from '@/components/ui/ShadButton';
import { CopyableCommand } from '../shared/CopyableCommand';
import { StepProps } from '../shared/types';

export const InstallStep: React.FC<StepProps> = ({ isActive, onComplete, isCompleted }) => {
  const [isCompletedLocal, setIsCompletedLocal] = useState(false);

  /* 
  Do not remove these commented out commands:
  // const command = 'curl -sSL https://install.tracer.cloud | bash -s -- demo && . ~/.bashrc';
  // const command = 'curl -sSL https://install.tracer.cloud/installation-script.sh | bash && source ~/.bashrc';
  */
  const command = 'curl -sSL https://install.tracer.cloud | bash -s -- demo && . ~/.bashrc';

  const handleComplete = () => {
    setIsCompletedLocal(true);
    onComplete();
  };

  if (!isActive) return null;
  
  return (
    <>
      <div className="text-white text-sm mb-4">
        <div className="flex flex-col space-y-3">
          <div className="flex items-start">
            <span className="inline-block w-5 mr-2 text-right">1.</span>
            <span>Press button below "Open GitHub Codespaces" and log into GitHub</span>
          </div>
          <div className="flex items-start">
            <span className="inline-block w-5 mr-2 text-right">2.</span>
            <span>Create a new Codespace</span>
          </div>
          <div className="flex items-start">
            <span className="inline-block w-5 mr-2 text-right">3.</span>
            <span>Once the Codespace has finished loading, run the command below in the GitHub terminal</span>
          </div>
          <div className="flex items-start">
            <span className="inline-block w-5 mr-2 text-right">4.</span>
            <span>Look at that! Tracer is now installed. When you're ready, click "I've installed Tracer" to continue</span>
          </div>
        </div>
      </div>
      
      <CopyableCommand command={command} />
      
      <div className="flex mt-4 space-x-4">
        <ShadButton 
          onClick={() => window.open('https://codespaces.new/Tracer-Cloud/tracer-test-pipelines-bioinformatics?quickstart=1', '_blank')}
          className="transition-transform duration-200 hover:scale-105 hover:shadow-lg"
        >
          Open GitHub Codespaces
          <img
            src="https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png"
            alt="GitHub Logo"
            className="ml-2 w-6 h-6"
          />
        </ShadButton>
        
        <ShadButton
          onClick={handleComplete}
          className="transition-transform duration-200 hover:scale-105 hover:shadow-lg"
        >
          I've installed Tracer
        </ShadButton>
      </div>
    </>
  );
};
