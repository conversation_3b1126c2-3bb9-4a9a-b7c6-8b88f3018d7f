import { Team } from 'src/types';

export const fetchTeamsApi = async (): Promise<Team[]> => {
  // disabling API calls per 2025 April 30th
  return [];
  try {
    const response = await fetch('/api/teams');

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (!Array.isArray(data)) {
      throw new Error('API did not return an array of teams');
    }

    // Validate and transform the data
    const teams: Team[] = data.map((team: any) => ({
      id: team.id,
      name: team.name,
      members: team.members,
      ownerUserId: team.ownerUserId,
      createdAt: new Date(team.createdAt),
      externalInvitations: team.externalInvitations,
      type: team.type
    }));

    return teams;
  } catch (error) {
    console.error('Error fetching teams:', error);
    // Return an empty array to keep the app running
    return [];
  }
};

export const addTeamApi = async (teamName: string) => {
  const response = await fetch(`/api/teams`, {
    method: 'POST',
    body: JSON.stringify({ name: teamName }),
    headers: {
      'Content-Type': 'application/json'
    }
  });
  const data = await response.json();
  return data;
};

export const renameTeamApi = async (teamId: string, teamName: string) => {
  const response = await fetch(`/api/teams`, {
    method: 'PATCH',
    body: JSON.stringify({ teamId, name: teamName }),
    headers: {
      'Content-Type': 'application/json'
    }
  });
  const data = await response.json();
  return data;
};

export const removeUserApi = async (teamId: string, userId: string) => {
  const response = await fetch(`/api/teams/remove-user`, {
    method: 'POST',
    body: JSON.stringify({ teamId, userId }),
    headers: {
      'Content-Type': 'application/json'
    }
  });
  const data = await response.json();
  return data;
};

export const deleteTeamApi = async (teamId: string) => {
  const response = await fetch(`/api/teams`, {
    method: 'DELETE',
    body: JSON.stringify({ teamId }),
    headers: {
      'Content-Type': 'application/json'
    }
  });
  const data = await response.json();
  return data;
};

export const leaveTeamApi = async (teamId: string) => {
  const response = await fetch(`/api/teams/leave`, {
    method: 'POST',
    body: JSON.stringify({ teamId }),
    headers: {
      'Content-Type': 'application/json'
    }
  });
  const data = await response.json();
  return data;
};
