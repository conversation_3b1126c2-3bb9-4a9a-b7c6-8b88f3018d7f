import { useRouter } from 'next/router';
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState
} from 'react';
import { Team, TeamMember } from 'src/types';
import { useUserContext } from '../UserContext';
import {
  addTeam<PERSON>pi,
  deleteTeam<PERSON>pi,
  fetchTeams<PERSON><PERSON>,
  leaveTeam<PERSON>pi,
  removeUser<PERSON><PERSON>,
  renameTeam<PERSON><PERSON>
} from './api';

export interface ITeamsContext {
  availableTeams: Team[];
  teamId: string | null;
  selectedTeam: Team | null;
  addTeam: (teamName: string) => Promise<void>;
  changeTeam: (teamId: string) => Promise<void>;
  changeTeamByName: (teamName: string) => Promise<void>;
  changeTeamAndNavigate: (teamId: string, navigateTo: string) => Promise<void>;
  renameTeam: (teamId: string, teamName: string) => Promise<void>;
  removeUser: (email: string) => Promise<void>;
  deleteTeam: (teamId: string) => Promise<void>;
  leaveTeam: (teamId: string) => Promise<void>;
  otherUsers: TeamMember[];
  isLoading: boolean;
  forceRefresh: () => void;
  lastRefreshed: number;
  error: string | null;
}

const TeamsContext = createContext<ITeamsContext | undefined>(undefined);

export const TeamsContextProvider = ({
  children
}: {
  children: React.ReactNode;
}) => {
  const router = useRouter();
  const { userId } = useUserContext();
  const [teams, setTeams] = useState<Team[] | any>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [lastRefreshed, setLastRefreshed] = useState(Date.now());
  const [error, setError] = useState<string | null>(null);
  const [selectedTeamId, setSelectedTeamId] = useState<string | null>(null);

  const fetchUsersAndTeams = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const data = await fetchTeamsApi();
      setTeams(data);
    } catch (error) {
      setError(error);
    }

    setLastRefreshed(Date.now());

    setIsLoading(false);
  }, [userId]);

  useEffect(() => {
    fetchUsersAndTeams(); // Initial fetch
    const interval = setInterval(fetchUsersAndTeams, 30000);
    return () => clearInterval(interval);
  }, [fetchUsersAndTeams]);

  useEffect(() => {
    if (selectedTeamId === null) {
      setSelectedTeamId(userId);
    }
  }, [userId]);

  const forceRefresh = async () => {
    fetchUsersAndTeams();
  };

  const changeTeam = async (teamId: string) => {
    if (teamId === selectedTeamId) {
      return;
    }

    setSelectedTeamId(teamId);
  };

  const changeTeamByName = async (teamName: string) => {
    const team = teams.find((team) => team.name === teamName);
    if (team) {
      changeTeam(team.id);
    }
  };

  const changeTeamAndNavigate = async (
    teamId: string,
    navigateTo: string = '/app/pipelines'
  ) => {
    if (teamId === selectedTeamId) {
      return;
    }
    await changeTeam(teamId);
    router.push(navigateTo);
  };

  const removeUser = async (userId: string) => {
    await removeUserApi(selectedTeamId, userId);
    forceRefresh();
  };

  const renameTeam = async (teamId: string, teamName: string) => {
    await renameTeamApi(teamId, teamName);
    forceRefresh();
  };

  const addTeam = async (teamName: string) => {
    await addTeamApi(teamName);
    forceRefresh();
  };

  const deleteTeam = async (teamId: string) => {
    await deleteTeamApi(teamId);
    forceRefresh();
  };

  const leaveTeam = async (teamId: string) => {
    await leaveTeamApi(teamId);
    forceRefresh();
  };

  const value: ITeamsContext = {
    availableTeams: teams,
    teamId: selectedTeamId,
    selectedTeam: teams?.find((team) => team.id === selectedTeamId) || null,
    otherUsers:
      teams?.find((team) => team.id === selectedTeamId)?.members || [],
    lastRefreshed,
    isLoading,
    forceRefresh,
    addTeam,
    changeTeam,
    changeTeamByName,
    changeTeamAndNavigate,
    renameTeam,
    removeUser,
    deleteTeam,
    leaveTeam,
    error
  };

  return (
    <TeamsContext.Provider value={value}>{children}</TeamsContext.Provider>
  );
};

export const useTeamsContext = () => {
  const context = useContext(TeamsContext);
  if (context === undefined) {
    throw new Error(
      'useTeamsContext must be used within a TeamsContextProvider'
    );
  }
  return context;
};
