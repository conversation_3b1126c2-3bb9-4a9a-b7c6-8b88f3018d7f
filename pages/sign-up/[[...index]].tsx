import { StyledAppPageWrapper } from '@/components/common/Layout/StyledApp';
import { useUserContext } from '@/context/UserContext';
import { SignUp } from '@clerk/nextjs';
import useDisableScroll from 'hooks/useDisableScroll';

export default function SignUpPage() {
  useDisableScroll();
  const { userId } = useUserContext();
  const isLoggedIn = !!userId;

  return (
    <StyledAppPageWrapper className="pl-0">
      {!isLoggedIn && (
        <div className="flex justify-center items-center h-[70vh] w-full">
          <SignUp
            redirectUrl="https://sandbox.tracer.cloud/grafana"
            afterSignUpUrl="https://sandbox.tracer.cloud/grafana"
          />
        </div>
      )}
    </StyledAppPageWrapper>
  );
}
