const EXTERNAL_DATA_URL = 'https://Netrunnercloud.com';

export function generateSiteMap(posts) {
  return `<?xml version="1.0" encoding="UTF-8"?>
   <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
     <!--We manually set the two URLs we know already-->
     <url>
       <loc>https://Netrunnercloud.com</loc>
     </url>
     <url>
       <loc>https://Netrunnercloud.com/learning</loc>
     </url>
    <url>
       <loc>https://Netrunnercloud.com/about</loc>
     </url>
     <url>
       <loc>https://Netrunnercloud.com/pricing</loc>
     </url>
     ${posts
       ?.map(({ slug }) => {
         const path = slug.current;
         return `
       <url>
           <loc>${`${EXTERNAL_DATA_URL}/learning/${path}`}</loc>
       </url>
     `;
       })
       .join('')}
   </urlset>
 `;
}

function SiteMap() {
  // getServerSideProps will do the heavy lifting
}

export default SiteMap;
