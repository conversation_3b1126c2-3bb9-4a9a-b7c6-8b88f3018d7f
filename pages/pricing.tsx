/* This example requires Tailwind CSS v2.0+ */
import { Fragment } from 'react';
import { CheckIcon, MinusIcon } from '@heroicons/react/solid';
import { BetaModalBtnPricingPage } from 'components/ui/Modal/BetaModal';

const tiers = [
  {
    name: 'Academia',
    href: '#',
    priceYearly: 0,
    description:
      "Join a community of engineers and scientists. Leverage Netrunner's free capabilities."
  },
  {
    name: 'Professional',
    href: '#',
    priceYearly: '12,000',
    description: 'Get started quickly with Netrunner’s core capabilities.'
  },
  {
    name: 'Enterprise',
    href: '#',
    priceYearly: '20,000',
    description: 'Tailor Netrunner for enterprise applications of any scale.'
  }
];

const sections = [
  {
    name: 'Features',
    features: [
      {
        name: 'Access Anywhere - Work From Any Web Browser',
        tiers: { Academia: true, Professional: true, Enterprise: true }
      },
      {
        name: 'Real-time Collaboration',
        tiers: { Academia: true, Professional: true, Enterprise: true }
      },
      {
        name: 'Process Flow Diagrams',
        tiers: { Academia: true, Professional: true, Enterprise: true }
      },
      {
        name: 'Live Team Chat',
        tiers: { Academia: false, Professional: true, Enterprise: true }
      },
      {
        name: 'Templates of Frequently Used Manufacturing Architectures',
        tiers: { Academia: false, Professional: true, Enterprise: true }
      }
    ]
  },
  {
    name: 'Advanced functions',
    features: [
      {
        name: 'API',
        tiers: { Academia: false, Professional: true, Enterprise: true }
      },
      {
        name: 'Energy & Financial Analytics',
        tiers: { Academia: false, Professional: true, Enterprise: true }
      },
      {
        name: 'Federated Authentication',
        tiers: { Professional: false, Enterprise: true }
      },
      {
        name: 'Consolidated Billing',
        tiers: { Enterprise: true }
      }
    ]
  },
  {
    name: 'Support',
    features: [
      {
        name: 'Technical Support',
        tiers: {
          Academia: '48 hours',
          Professional: 'Direct Support',
          Enterprise: 'Priority Direct Support'
        }
      },
      {
        name: 'Centralized IP Control',
        tiers: { Professional: true, Enterprise: 'Advanced' }
      },
      {
        name: 'Approval Workflows',
        tiers: { Professional: true, Enterprise: 'Custom' }
      },
      {
        name: 'Guided Onboarding',
        tiers: { Enterprise: true }
      }
    ]
  }
];

function classNames(...classes) {
  return classes.filter(Boolean).join(' ');
}

export const GetTierButton = ({ tier, onClick }) => {
  return (
    <div
      onClick={onClick}
      className="mt-6 block border border-brandPurple bg-brandPurple w-full py-2 text-sm font-semibold text-white text-center hover:bg-gray-900 hover:border-brandPurple"
    >
      {tier && 'Join Private Beta'}
    </div>
  );
};

const MobilePricing = () => {
  return (
    <div className="max-w-2xl mx-auto space-y-16 lg:hidden fadeIn bg-white border border-gray-200 pt-8">
      {tiers.map((tier, tierIdx) => (
        <section key={tier.name}>
          <div className="px-4 mb-8">
            <h2 className="text-lg leading-6 font-medium text-black">
              {tier.name}
            </h2>
            <p className="mt-4">
              <span className="text-4xl font-extrabold text-black">
                ${tier.priceYearly}
              </span>{' '}
              <span className="text-base font-medium text-black">
                /user/year
              </span>
            </p>
            <p className="mt-4 text-sm text-black">{tier.description}</p>
            {/* <GetTierButton tier={tier} /> */}
            <BetaModalBtnPricingPage tier={tier} />
          </div>

          {sections.map((section) => (
            <table
              key={section.name}
              className="w-full bg-white border-gray-200"
            >
              <caption className="bg-gray-50 border-t border-gray-200 py-3 px-4 text-sm font-medium text-black text-left">
                {section.name}
              </caption>
              <thead>
                <tr>
                  <th className="sr-only" scope="col">
                    Feature
                  </th>
                  <th className="sr-only" scope="col">
                    Included
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {section.features.map((feature) => (
                  <tr key={feature.name} className="border-t border-gray-200">
                    <th
                      className="py-5 px-4 text-sm font-normal text-gray-500 text-left"
                      scope="row"
                    >
                      {feature.name}
                    </th>
                    <td className="py-5 pr-4">
                      {typeof feature.tiers[tier.name] === 'string' ? (
                        <span className="block text-sm text-gray-700 text-right">
                          {feature.tiers[tier.name]}
                        </span>
                      ) : (
                        <>
                          {feature.tiers[tier.name] === true ? (
                            <CheckIcon
                              className="ml-auto h-5 w-5 text-green-500"
                              aria-hidden="true"
                            />
                          ) : (
                            <MinusIcon
                              className="ml-auto h-5 w-5 text-gray-400"
                              aria-hidden="true"
                            />
                          )}

                          <span className="sr-only">
                            {feature.tiers[tier.name] === true ? 'Yes' : 'No'}
                          </span>
                        </>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          ))}

          <div
            className={classNames(
              tierIdx < tiers.length - 1 ? 'py-5 border-b' : 'pt-5',
              'border-t border-gray-200 px-4'
            )}
          >
            <a
              href={tier.href}
              className="block w-full bg-brandPurple border border-brandPurple py-2 text-sm font-semibold text-white text-center hover:bg-gray-900 hover:border-gray-900"
            >
              Buy {tier.name}
            </a>
          </div>
        </section>
      ))}
    </div>
  );
};

export default function PricingPage() {
  return (
    <div className="bg-white">
      <div className="max-w-7xl mx-auto bg-white py-16 sm:py-24 sm:px-6 lg:px-8 bg-[url('/assets/top-right-dots.svg')] bg-no-repeat bg-right-top">
        <div className="sm:flex sm:flex-col sm:align-center pb-16 px-4 md:pb-24">
          <h1 className="text-4xl md:text-5xl font-extrabold text-black text-center">
            Netrunner Pricing
          </h1>
        </div>
        {/* xs to lg */}
        <MobilePricing />

        {/* lg+ */}
        <div className="hidden md:block">
          <table className="w-full h-px table-fixed bg-white border border-gray-200 fadeIn">
            <caption className="sr-only">Pricing plan comparison</caption>
            <thead>
              <tr>
                <th
                  className="pb-4 pt-4 px-6 text-sm font-medium text-black text-left"
                  scope="col"
                >
                  <span className="sr-only">Feature by</span>
                  <span>Plans</span>
                </th>
                {tiers.map((tier) => (
                  <th
                    key={tier.name}
                    className="w-1/4 pb-4 px-6 pt-4 text-lg leading-6 font-medium text-black text-left"
                    scope="col"
                  >
                    {tier.name}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="border-t border-gray-200 divide-y divide-gray-200">
              <tr>
                <th
                  className="py-8 px-6 text-sm font-medium text-black text-left align-top"
                  scope="row"
                >
                  Pricing
                </th>
                {tiers.map((tier) => (
                  <td key={tier.name} className="h-full py-8 px-6 align-top">
                    <div className="relative h-full table">
                      <p>
                        <span className="text-4xl font-extrabold text-black">
                          ${tier.priceYearly}
                        </span>{' '}
                        <span className="text-base font-medium text-black">
                          /user/year
                        </span>
                      </p>
                      <p className="mt-4 mb-16 text-sm text-black">
                        {tier.description}
                      </p>
                      <a
                        href={tier.href}
                        className="absolute bottom-0 flex-grow block w-full bg-brandPurple border border-brandPurple 5 py-2 text-sm font-semibold text-white text-center hover:bg-gray-900 hover:border-gray-900"
                      >
                        Get Beta Access
                      </a>
                    </div>
                  </td>
                ))}
              </tr>
              {sections.map((section) => (
                <Fragment key={section.name}>
                  <tr>
                    <th
                      className="bg-gray-50 py-3 pl-6 text-sm font-medium text-black text-left"
                      colSpan={4}
                      scope="colgroup"
                    >
                      {section.name}
                    </th>
                  </tr>
                  {section.features.map((feature) => (
                    <tr key={feature.name}>
                      <th
                        className="py-5 px-6 text-sm font-normal text-gray-500 text-left"
                        scope="row"
                      >
                        {feature.name}
                      </th>
                      {tiers.map((tier) => (
                        <td key={tier.name} className="py-5 px-6">
                          {typeof feature.tiers[tier.name] === 'string' ? (
                            <span className="block text-sm text-gray-700">
                              {feature.tiers[tier.name]}
                            </span>
                          ) : (
                            <>
                              {feature.tiers[tier.name] === true ? (
                                <CheckIcon
                                  className="h-5 w-5 text-green-500"
                                  aria-hidden="true"
                                />
                              ) : (
                                <MinusIcon
                                  className="h-5 w-5 text-gray-400"
                                  aria-hidden="true"
                                />
                              )}

                              <span className="sr-only">
                                {feature.tiers[tier.name] === true
                                  ? 'Included'
                                  : 'Not included'}{' '}
                                in {tier.name}
                              </span>
                            </>
                          )}
                        </td>
                      ))}
                    </tr>
                  ))}
                </Fragment>
              ))}
            </tbody>
            <tfoot>
              <tr className="border-t border-gray-200">
                <th className="sr-only" scope="row">
                  Choose your plan
                </th>
                {tiers.map((tier) => (
                  <td key={tier.name} className="py-5 px-6">
                    <a
                      href={tier.href}
                      className="block w-full bg-brandPurple border border-brandPurple py-2 text-sm font-semibold text-white text-center hover:bg-gray-900 hover:border-gray-900"
                    >
                      Get Beta Access
                    </a>
                  </td>
                ))}
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </div>
  );
}
