export const RUN_STATUS_NEW = 'new_run';
export const RUN_STATUS_FINISHED = 'finished_run';
export const RUN_STATUS_ONGOING = 'ongoing_run';
export const RUN_STATUS_STALE = 'stale_run';
export const RUN_STATUS_ERROR = 'error';
export const RUN_STATUS_UNKNOWN = 'unknown_run_status';
export const RUN_STATUS_MESSAGE = 'run_status_message';
export const RUN_STATUS_ALERT = 'alert';
export const RUN_STATUS_SET_TAG = 'tag_update';
export const RUN_STATUS_TOOL_EXECUTION = 'tool_execution';
export const RUN_STATUS_FINISHED_TOOL_EXECUTION = 'finished_tool_execution';
export const RUN_STATUS_SYSLOG_EVENT = 'syslog_event';
export const TRACER_STATUS_START_DAEMON = 'daemon_start';
export const TRACER_STATUS_FINISHED_INSTALLATION = 'installation_finished';
export const TRACER_STATUS_START_INSTALLATION = 'installation_start';
export const TRACER_STATUS_FAILED_INSTALLATION = 'installation_failed';
export const PROCESS_STATUS_METRIC_EVENT = 'metric_event';
export const PROCESS_STATUS_TOOL_METRIC_EVENT = 'tool_metric_event';
export const PROCESS_STATUS_CUSTOM_METRIC_EVENT = 'custom_metric_event';
export const PROCESS_STATUS_TEST_EVENT = 'test_event';
