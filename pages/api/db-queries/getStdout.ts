import { create, getAll } from 'src/clickhouse';
import { ServiceId } from 'src/types';

export const getRunStdout = async (
  runId: string
): Promise<
  {
    lines: string;
    isError: boolean;
  }[]
> => {
  const lines = await getAll(
    `SELECT lines, is_error FROM stdout WHERE run_id = {runId: String} ORDER BY timestamp DESC LIMIT 50`,
    { runId }
  );

  lines.reverse();

  return lines.map((line: any) => ({
    lines: line.lines,
    isError: line.is_error
  }));
};

export const addStdoutLines = async ({
  serviceId,
  runId,
  runName,
  lines,
  timestamp,
  apiKey,
  isError
}: {
  serviceId: ServiceId;
  runId: string;
  runName: string;
  lines: string;
  timestamp: number;
  apiKey: string;
  isError: boolean;
}): Promise<void> => {
  await create('stdout', {
    run_id: runId,
    run_name: runName,
    api_key: api<PERSON><PERSON>,
    team_id: serviceId.teamId,
    service_name: serviceId.name,
    timestamp,
    lines,
    is_error: isError
  });
  return Promise.resolve();
};
