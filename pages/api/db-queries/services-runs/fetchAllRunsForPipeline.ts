import {
  RUN_STATUS_FINISHED,
  RUN_STATUS_NEW,
  RUN_STATUS_ONGOING,
  RUN_STATUS_SET_TAG
} from 'pages/api/constants/pipeline-status';
import { getAll } from 'src/clickhouse';
import { LogsRecord, ServiceId } from '../../../../src/types';
import { fetchMaxMetricsAgg } from '../fetchMaxMetricsAgg';
import { getMetrics } from '../getMetrics';

export interface IRun {
  run_name: string;
  run_id: string;
  run_message: string;
  start_unixDate: number;
  end_unixDate: number | null;
  start_isoDate: string;
  end_isoDate: string;
  run_end_definition_type: string;
  last_run_event_type: string;
  serviceName: string;
  metrics?: any[] | null;
  metrics_count: number;
  duration: number | string;
  aggs?: any;
  tags: string[];
  metricAggregations?: any;
}

function initializeRun(event: LogsRecord, serviceName: string): IRun {
  return {
    run_name: event.run_name || '',
    run_id: event.run_id || '',
    run_message: event.message,
    start_unixDate: Number(event.timestamp),
    start_isoDate: new Date(Number(event.timestamp)).toISOString(),
    end_isoDate: '',
    end_unixDate: null,
    run_end_definition_type: '',
    last_run_event_type: event.process_status,
    serviceName,
    tags: [],
    metrics_count: 0,
    duration: ''
  };
}

function finalizeCurrentRun(run: IRun, nextRunStartUnixDate?: number) {
  const endUnixDate = run.end_unixDate || nextRunStartUnixDate || null;
  run.end_unixDate = Number(endUnixDate);
  run.end_isoDate = new Date(Number(endUnixDate)).toISOString();
  run.duration = calculateDuration(run.start_unixDate, endUnixDate);
  run.run_end_definition_type = run.end_unixDate
    ? RUN_STATUS_FINISHED
    : RUN_STATUS_ONGOING;
}

function finishCurrentRun(run: IRun, endUnixDate: number) {
  run.end_unixDate = Number(endUnixDate);
  run.end_isoDate = new Date(Number(endUnixDate)).toISOString();
  run.duration = calculateDuration(run.start_unixDate, endUnixDate);
  run.run_end_definition_type = RUN_STATUS_FINISHED;
}

export function calculateDuration(
  startUnixDate: number,
  endUnixDate: number
): number {
  const durationInSeconds = Number(endUnixDate - startUnixDate) / 1000;
  return durationInSeconds;
}

export async function fetchAllRunsForPipeline(
  serviceId: ServiceId,
  includeMetrics
): Promise<IRun[]> {
  if (!serviceId.name || !serviceId.teamId) {
    throw new Error('Service name and user is required');
  }

  const columns = [
    'service_name',
    'run_id',
    'run_name',
    'timestamp',
    'record_type',
    'process_status',
    'human_date',
    'process_type',
    'event_type'
  ];

  const allRunEvents = await getAll(
    `SELECT ${columns.join(
      ', '
    )} FROM logs WHERE service_name = {serviceName: String} AND user_id = {teamId: String} AND process_status IS NOT NULL AND process_status IN ('new_run', 'finished_run', 'tag_update') ORDER BY timestamp ASC`,
    {
      serviceName: serviceId.name,
      teamId: serviceId.teamId
    }
  );

  if (allRunEvents.length === 0) {
    false &&
      console.warn(
        `[fetchAllRunsForPipeline.ts] No logs found for service: ${serviceId.name} for team: ${serviceId.teamId}`
      );
    return [];
  }

  const runs: IRun[] = [];
  let currentRun: IRun | null = null;

  allRunEvents.forEach((event: LogsRecord) => {
    if (event.process_status === RUN_STATUS_NEW) {
      if (currentRun) {
        finalizeCurrentRun(currentRun, event.timestamp);
        runs.push(currentRun);
      }
      currentRun = initializeRun(event, serviceId.name);
    } else if (event.process_status === RUN_STATUS_FINISHED && currentRun) {
      finishCurrentRun(currentRun, event.timestamp);
      runs.push(currentRun);
      currentRun = null;
    } else if (currentRun && event.process_status === RUN_STATUS_SET_TAG) {
      currentRun.tags = event.properties.tags as string[];
    }
  });

  if (currentRun) {
    finalizeCurrentRun(currentRun);
    runs.push(currentRun);
  }

  // Sort runs by start date in descending order
  const sortedRuns = runs.sort((a, b) => b.start_unixDate - a.start_unixDate);

  if (includeMetrics) {
    for (const run of sortedRuns) {
      if (run.start_unixDate && run.end_unixDate) {
        const metrics = await getMetrics(
          serviceId,
          run.start_unixDate,
          run.end_unixDate
        );
        run.metrics = metrics;
        run.metrics_count = metrics.length;
      } else if (
        run.start_unixDate &&
        run.run_end_definition_type === RUN_STATUS_ONGOING
      ) {
        const metrics = await getMetrics(
          serviceId,
          run.start_unixDate,
          Math.floor(Date.now())
        );
        run.metrics = metrics;
        run.metrics_count = metrics.length;
      }
    }
  }

  const runsWithMetricAggregations = await fetchMaxMetricsAgg(sortedRuns);

  return runsWithMetricAggregations;
}
