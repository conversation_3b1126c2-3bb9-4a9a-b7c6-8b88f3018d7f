import { RUN_STATUS_UNKNOWN } from 'pages/api/constants/pipeline-status';
import { getAll, getFirst } from 'src/clickhouse';
import { ServiceId } from 'src/types';
import { IRun, fetchAllRunsForPipeline } from './fetchAllRunsForPipeline';

export interface IService {
  serviceName: string;
  apiKey: string;
  serviceId: string;
  highestCpu: number | null;
  highestMemory: number | null;
  currentProcessStatus: string;
  runs: IRun[];
  typeEnvironment: string;
  isPipelineStale: boolean;
  username: string;
  userImgUrl: string;
}


async function fetchServiceAttribute(
  serviceId: ServiceId,
  recordType: string,
  sortField?: string
) {
  const result = getFirst(
    `SELECT ${sortField} FROM logs WHERE service_name = {serviceName: String} AND user_id = {teamId: String} AND record_type = {recordType: String} ORDER BY {sortField: String} DESC LIMIT 1`,
    {
      serviceName: serviceId.name,
      teamId: serviceId.teamId,
      recordType,
      sortField: sortField || 'timestamp'
    }
  );

  return result
}

async function fetchMostRecentLogTimestamp(
  serviceId: ServiceId
): Promise<number | null> {
  const result = await getFirst(
    `SELECT timestamp FROM logs WHERE service_name = {serviceName: String} AND user_id = {teamId: String} ORDER BY timestamp DESC LIMIT 1`,
    {
      serviceName: serviceId.name,
      teamId: serviceId.teamId
    }
  );

  return result?.[0]?.timestamp || null;
}

async function fetchIsPipelineStale(
  serviceId: ServiceId,
  stalenessThresholdMinutes = 15
): Promise<boolean> {
  const mostRecentTimestamp = await fetchMostRecentLogTimestamp(serviceId);
  if (mostRecentTimestamp === null) {
    return false;
  }
  const currentTime = Date.now();
  const stalenessThresholdMillis = stalenessThresholdMinutes * 60 * 1000;
  return currentTime - mostRecentTimestamp > stalenessThresholdMillis;
}

async function fetchLastProcessStatus(serviceId: ServiceId) {
  const result = await getFirst(
    `SELECT process_status FROM logs WHERE service_name = {serviceName: String} AND user_id = {teamId: String} AND record_type = 'PROCESS_STATUS' ORDER BY timestamp DESC LIMIT 1`,
    {
      serviceName: serviceId.name,
      teamId: serviceId.teamId
    }
  );

  return result?.process_status || RUN_STATUS_UNKNOWN;
}

async function getServiceDetails({
  serviceUniqueId,
  apiKey,
  typeEnvironment,
  serviceId,
  includeMetrics,
  username,
  userImgUrl
}: {
  serviceUniqueId: ServiceId;
  typeEnvironment: string;
  apiKey: string;
  includeMetrics?: boolean;
  serviceId: string;
  username: string;
  userImgUrl: string;
}): Promise<IService> {
  const [
    highestCpuRecord,
    highestMemoryRecord,
    currentProcessStatus,
    isPipelineStale,
    runs
  ] = await Promise.all([
    fetchServiceAttribute(
      serviceUniqueId,
      'metric_event',
      'system_cpu_utilization'
    ),
    fetchServiceAttribute(
      serviceUniqueId,
      'metric_event',
      'system_memory_utilization'
    ),
    fetchLastProcessStatus(serviceUniqueId),
    fetchIsPipelineStale(serviceUniqueId),
    fetchAllRunsForPipeline(serviceUniqueId, includeMetrics)
  ]);

  return {
    serviceName: serviceUniqueId.name,
    serviceId,
    highestCpu: highestCpuRecord?.system_cpu_utilization ?? null,
    highestMemory: highestMemoryRecord?.system_memory_utilization ?? null,
    currentProcessStatus,
    runs,
    apiKey,
    typeEnvironment,
    isPipelineStale,
    username,
    userImgUrl
  };
}

export async function getServicesFromDb(teamId: string, includeMetrics) {
  if (!teamId) throw new Error('User ID is required'); // More explicit error message
  try {
    const services = await getAll(
      `SELECT * FROM ApiKeys WHERE userId = {teamId: String} ORDER BY "xata.createdAt" DESC`,
      {
        teamId
      }
    );


    const uniqueServices = services.map((service) => ({
      serviceUniqueId: { name: service.serviceName, teamId },
      serviceName: service.serviceName,
      typeEnvironment: service.typeEnvironment,
      apiKey: service.apiKey,
      serviceId: service.id,
      username: service?.username,
      userImgUrl: service?.userImgUrl,
      includeMetrics
    }));

    const servicesDetails: IService[] = await Promise.all(
      uniqueServices.map(getServiceDetails)
    );

    return { services: servicesDetails };
  } catch (error) {
    console.error('Failed to fetch services logs:', error);
    throw error;
  }
}
