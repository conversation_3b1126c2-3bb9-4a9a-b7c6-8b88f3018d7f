export function getRandomElement(array) {
  return array[Math.floor(Math.random() * array.length)];
}

export function generateRunName() {
  const adjectives = ['snowy', 'silent', 'desert', 'mystic', 'ancient'];
  const animals = [
    'owl',
    'wolf',
    'lion',
    'tiger',
    'hawk', // Existing animals
    'eagle',
    'fox',
    'bear',
    'penguin',
    'dolphin', // New animals
    'elephant',
    'leopard',
    'giraffe',
    'rhino',
    'panther', // More new animals
    'falcon',
    'lynx',
    'moose',
    'otter',
    'raccoon' // And even more
  ];
  const randomNumber = Math.floor(Math.random() * 100);
  return `${getRandomElement(adjectives)}-${getRandomElement(
    animals
  )}-${randomNumber}`;
}

export function generateRunId() {
  return crypto.randomUUID();
}