import { getFirst } from 'src/clickhouse';

export async function getTeamIdAndServiceNameFromApiKey(
  apiKey: string
): Promise<{ teamId: string; serviceName: string; isActiveApiKey: boolean }> {
  const apiKeyRecord = await getFirst(
    `SELECT * FROM ApiKeys WHERE apiKey = {apiKey: String} AND isActive = true`,
    { apiKey }
  );

  if (!apiKeyRecord) {
    throw new Error('API key not found or not active');
  }

  return {
    teamId: apiKeyRecord.userId,
    serviceName: apiKeyRecord.serviceName,
    isActiveApiKey: apiKeyRecord.isActive
  };
}

export async function getApiKeyFromTeamIdAndServiceName({teamId, serviceName}: {
  teamId: string,
  serviceName: string
}): Promise<string> {
  const apiKeyRecord = await getFirst(
    `SELECT * FROM ApiKeys WHERE userId = {teamId: String} AND serviceName = {serviceName: String}`,
    { teamId, serviceName }
  );

  if (!apiKeyRecord) {
    throw new Error('API key not found');
  }

  return apiKeyRecord.apiKey;
}
