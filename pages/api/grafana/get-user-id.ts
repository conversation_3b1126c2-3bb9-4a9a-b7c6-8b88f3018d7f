import { NextApiRequest, NextApiResponse } from 'next';
import {
  HTTP_BAD_REQUEST,
  HTTP_INTERNAL_SERVER_ERROR,
  HTTP_OK
} from '../constants/http-status';

const GRAFANA_API_URL = 'https://tracerbio.grafana.net/api';
const GRAFANA_TOKEN = process.env.GRAFANA_TOKEN!;

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(HTTP_BAD_REQUEST).json({
      success: false,
      error: `Method ${req.method} Not Allowed`
    });
  }

  try {
    const { email } = req.body;

    if (!email) {
      return res.status(HTTP_BAD_REQUEST).json({
        success: false,
        error: 'Email is required',
      });
    }

    // Get all users in the organization
    const usersRes = await fetch(`${GRAFANA_API_URL}/org/users`, {
      headers: {
        Authorization: `Bearer ${GRAFANA_TOKEN}`,
        'Content-Type': 'application/json',
      },
    });

    if (!usersRes.ok) {
      throw new Error('Failed to get users');
    }

    const users = await usersRes.json();
    const user = users.find((u: any) => u.email === email);

    if (!user) {
      return res.status(HTTP_BAD_REQUEST).json({
        success: false,
        error: 'User not found',
      });
    }

    return res.status(HTTP_OK).json({
      success: true,
      data: {
        userId: user.userId,
        email: user.email,
        name: user.name,
        role: user.role
      }
    });
  } catch (error) {
    return res.status(HTTP_INTERNAL_SERVER_ERROR).json({
      success: false,
      error: 'Unexpected server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
} 