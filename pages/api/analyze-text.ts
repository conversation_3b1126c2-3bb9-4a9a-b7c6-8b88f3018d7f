// app/api/chat/route.ts
import { NextApiRequest, NextApiResponse } from 'next';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const text = req.body.text;

  console.log('req.body.text', req.body.text);

  try {
    const completion = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        { role: 'system', content: text || 'You are a helpful assistant.' }
        // Assuming you want to include the user's message from the request body
        // Ensure your request body includes a `message` property or adjust accordingly
      ]
    });

    const message = completion.choices[0].message.content;
    console.log('message', message);

    // Send the completion data back as the response
    // Depending on the structure of the completion object, adjust accordingly
    // Assuming `completion` has a property `choices` which contains the response texts
    if (message) {
      res.status(200).json({ response: message });
    } else {
      res.status(200).json({ error: 'No completion found' });
    }
  } catch (error) {
    console.error('Error calling OpenAI:', error);
    res.status(500).json({ error: 'Failed to create chat completion' });
  }
}
