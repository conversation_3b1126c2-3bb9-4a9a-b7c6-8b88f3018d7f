import { NextApiRequest, NextApiResponse } from "next";
import { HTTP_BAD_REQUEST, HTTP_INTERNAL_SERVER_ERROR, HTTP_OK } from "../constants/http-status";
import { prepareUsersDefaultApiKeys } from "../db-queries/getTeams";


export const getHandler = async (req: NextApiRequest, res: NextApiResponse) => {

  const userId = req.query.userId;

  if (!userId || typeof userId !== 'string' || userId.trim().length === 0) {
    return res.status(HTTP_BAD_REQUEST).json({ error: 'userId is required' });
  }

  await prepareUsersDefaultApiKeys(userId);
  return res.status(HTTP_OK).json({ success: true });
}

export const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  try {
    switch (req.method) {
      case 'GET':
        return getHandler(req, res);
      default:
        return res
          .status(HTTP_BAD_REQUEST)
          .json({ error: 'Invalid request method' });
    }
  } catch (error) {
    return res
      .status(HTTP_INTERNAL_SERVER_ERROR)
      .json({ error: 'Internal server error' });
  }
}