// api/teams.ts
import { getAuth } from '@clerk/nextjs/server';
import { NextApiRequest, NextApiResponse } from 'next';
import { getTeams } from '../db-queries/getTeams';
import {
  HTTP_BAD_REQUEST,
  HTTP_INTERNAL_SERVER_ERROR,
  HTTP_OK
} from '../db-queries/httpStatusCodes';
import { HTTP_UNAUTHORIZED } from '../constants/http-status';
import { Team } from 'src/types';

export const getHandler = async (
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> => {
  try {
    const user = getAuth(req);

    if (!user || !user.userId) {
      return res.status(HTTP_UNAUTHORIZED).json({
        error: 'User is not authenticated',
        details: 'Please log in to access this resource'
      });
    }

    const teams: Team[] = await getTeams(user.userId);

    if (!teams) {
      return res.status(HTTP_BAD_REQUEST).json({
        error: 'Unable to retrieve teams',
        details:
          'No teams found for the user or an error occurred while fetching teams'
      });
    }

    return res.status(HTTP_OK).json(teams);
  } catch (error) {
    console.error('Error in getHandler:', error);

    return res.status(HTTP_INTERNAL_SERVER_ERROR).json({
      error: 'Internal server error',
      details: 'An unexpected error occurred while processing your request'
    });
  }
};
