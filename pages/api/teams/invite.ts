// api/teams.ts
import { clerkClient } from '@clerk/nextjs';
import { getAuth } from '@clerk/nextjs/server';
import { NextApiRequest, NextApiResponse } from 'next';
import {
  acceptTeamInvitation,
  addTeamMember,
  createExternalInvitation,
  getInvitations,
  getTeam,
  removeTeamMember
} from '../db-queries/getTeams';
import {
  HTTP_BAD_REQUEST,
  HTTP_INTERNAL_SERVER_ERROR,
  HTTP_OK
} from '../db-queries/httpStatusCodes';
import { mg } from '../email';

const sendExternalInvitation = async ({
  targetEmail,
  teamName,
  userName,
  teamId
}: {
  targetEmail: string;
  teamName: string;
  userName: string;
  teamId: string;
}) => {
  const code = await createExternalInvitation({ email: targetEmail, teamId });

  const url =
    process.env.NEXT_PUBLIC_DEV_STAGE === 'dev'
      ? 'https://develop.app.tracer.bio'
      : 'https://tracer.bio';
  const message = `Hello! You have been invited to join ${teamName} on tracer.bio by ${userName}. Your invitation code: ${code} Click here to register: ${url}`;
  const htmlMessage = `<p>Hello! You have been invited to join ${teamName} on tracer.bio by ${userName}. Your invitation code:</p><h4>${code}</h4><p>Click <a href="${url}">here</a> to register.</p>`;

  mg.messages
    .create(process.env.MAILGUN_DOMAIN, {
      from: 'tracer.bio <<EMAIL>>',
      to: [targetEmail],
      subject: `You have been invited to join ${teamName} on tracer.bio`,
      text: message,
      html: htmlMessage
    })
    .then((msg) => {
      if (msg.status === 200) {
        console.log(`Email notification sent successfully to ${targetEmail}`);
      } else {
        console.error('Error sending email notification (3)', msg);
      }
    })
    .catch((error) => {
      console.error('Error sending email notification (1)', error);
    });
};

const getHandler = async (req: NextApiRequest, res: NextApiResponse) => {
  const user = getAuth(req);

  if (!user || !user.userId) {
    return res
      .status(HTTP_BAD_REQUEST)
      .json({ error: 'User is not authenticated' });
  }

  const invites = await getInvitations(user.userId);

  return res.status(HTTP_OK).json(invites);
};

const postHandler = async (req: NextApiRequest, res: NextApiResponse) => {
  const user = getAuth(req);

  if (!user || !user.userId) {
    return res
      .status(HTTP_BAD_REQUEST)
      .json({ error: 'User is not authenticated' });
  }

  const invitedUserEmail = req.body.email;
  const teamId = req.body.teamId;

  if (
    !invitedUserEmail ||
    typeof invitedUserEmail !== 'string' ||
    invitedUserEmail.trim().length === 0
  ) {
    return res.status(HTTP_BAD_REQUEST).json({ error: 'email is required' });
  }

  if (!teamId || typeof teamId !== 'string' || teamId.trim().length === 0) {
    return res.status(HTTP_BAD_REQUEST).json({ error: 'teamId is required' });
  }

  const team = await getTeam({ userId: user.userId, teamId });

  if (!team) {
    return res.status(HTTP_BAD_REQUEST).json({ error: 'Team not found' });
  }

  const users = await clerkClient.users.getUserList({
    emailAddress: [invitedUserEmail]
  });

  const userBackendObject = await clerkClient.users.getUser(user.userId);

  if (users.length === 0) {
    await sendExternalInvitation({
      targetEmail: invitedUserEmail,
      teamName: team.name,
      userName: userBackendObject.username,
      teamId
    });
    return res.status(HTTP_OK).json({ success: true, type: "EXTERNAL" });
  }

  const invitedUserID = users[0].id;

  const invitations = await getInvitations(invitedUserID);

  if (invitations.some((invite) => invite.teamId === teamId)) {
    return res.status(HTTP_BAD_REQUEST).json({ error: 'User already invited' });
  }

  await addTeamMember(invitedUserID, teamId, 'invited');

  return res.status(HTTP_OK).json({ success: true, type: "REGISTERED_USER" });
};

const patchHandler = async (req: NextApiRequest, res: NextApiResponse) => {
  const user = getAuth(req);

  if (!user || !user.userId) {
    return res
      .status(HTTP_BAD_REQUEST)
      .json({ error: 'User is not authenticated' });
  }

  const teamId = req.body.teamId;

  if (!teamId || typeof teamId !== 'string' || teamId.trim().length === 0) {
    return res.status(HTTP_BAD_REQUEST).json({ error: 'teamId is required' });
  }

  await acceptTeamInvitation(user.userId, teamId);

  return res.status(HTTP_OK).json({ success: true });
};

const deleteHandler = async (req: NextApiRequest, res: NextApiResponse) => {
  const user = getAuth(req);

  if (!user || !user.userId) {
    return res
      .status(HTTP_BAD_REQUEST)
      .json({ error: 'User is not authenticated' });
  }

  const teamId = req.body.teamId;

  if (!teamId || typeof teamId !== 'string' || teamId.trim().length === 0) {
    return res.status(HTTP_BAD_REQUEST).json({ error: 'teamId is required' });
  }

  await removeTeamMember(user.userId, teamId);

  return res.status(HTTP_OK).json({ success: true });
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    switch (req.method) {
      case 'POST':
        return postHandler(req, res);
      case 'PATCH':
        return patchHandler(req, res);
      case 'DELETE':
        return deleteHandler(req, res);
      case 'GET':
        return getHandler(req, res);
      default:
        return res
          .status(HTTP_BAD_REQUEST)
          .json({ error: 'Invalid request method' });
    }
  } catch (error) {
    console.error('[api/teams.ts] Error retrieving tools details:', error);
    res.status(HTTP_INTERNAL_SERVER_ERROR).json({
      error: '[api/teams.ts] Failed to retrieve tool details'
    });
  }
}
