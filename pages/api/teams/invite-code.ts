// api/teams.ts
import { getAuth } from '@clerk/nextjs/server';
import { NextApiRequest, NextApiResponse } from 'next';
import {
  addTeamMember,
  deleteExternalInvitation,
  getExternalInvitationByCode,
  getTeamByIdMember
} from '../db-queries/getTeams';
import {
  HTTP_BAD_REQUEST,
  HTTP_INTERNAL_SERVER_ERROR,
  HTTP_OK
} from '../db-queries/httpStatusCodes';

const postHandler = async (req: NextApiRequest, res: NextApiResponse) => {
  const user = getAuth(req);

  if (!user || !user.userId) {
    return res
      .status(HTTP_BAD_REQUEST)
      .json({ error: 'User is not authenticated' });
  }

  const { code } = req.body;

  if (!code || typeof code !== 'string' || code.trim().length === 0) {
    return res.status(HTTP_BAD_REQUEST).json({ error: 'code is required' });
  }

  const invitation = await getExternalInvitationByCode(code);

  if (!invitation) {
    return res.status(HTTP_BAD_REQUEST).json({ success: false });
  }

  const team = await getTeamByIdMember({
    memberUserId: user.userId,
    teamId: invitation.teamId
  });

  if (team) {
    return res.status(HTTP_BAD_REQUEST).json({ success: false });
  }

  await deleteExternalInvitation(code);

  await addTeamMember(user.userId, invitation.teamId, 'joined');

  return res.status(HTTP_OK).json({ success: true });
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    switch (req.method) {
      case 'POST':
        return postHandler(req, res);
      default:
        return res
          .status(HTTP_BAD_REQUEST)
          .json({ error: 'Invalid request method' });
    }
  } catch (error) {
    console.error('[api/teams.ts] Error retrieving tools details:', error);
    res.status(HTTP_INTERNAL_SERVER_ERROR).json({
      error: '[api/teams.ts] Failed to retrieve tool details'
    });
  }
}
