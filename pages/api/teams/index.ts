// api/teams.ts
import { getAuth } from '@clerk/nextjs/server';
import { NextApiRequest, NextApiResponse } from 'next';
import {
  addTeam,
  deleteTeam,
  getTeam,
  updateTeamName
} from '../db-queries/getTeams';
import {
  HTTP_BAD_REQUEST,
  HTTP_INTERNAL_SERVER_ERROR,
  HTTP_OK
} from '../db-queries/httpStatusCodes';
import { getHandler } from './getHandler';

const postHandler = async (req: NextApiRequest, res: NextApiResponse) => {
  const user = getAuth(req);

  if (!user || !user.userId) {
    return res
      .status(HTTP_BAD_REQUEST)
      .json({ error: 'User is not authenticated' });
  }

  const name = req.body.name;

  if (!name || typeof name !== 'string' || name.trim().length === 0) {
    return res.status(HTTP_BAD_REQUEST).json({ error: 'name is required' });
  }

  await addTeam({ userId: user.userId, name });

  return res.status(HTTP_OK).json({ success: true });
};

const patchHandler = async (req: NextApiRequest, res: NextApiResponse) => {
  const user = getAuth(req);

  if (!user || !user.userId) {
    return res
      .status(HTTP_BAD_REQUEST)
      .json({ error: 'User is not authenticated' });
  }

  const { teamId, name } = req.body;

  if (!teamId || typeof teamId !== 'string' || teamId.trim().length === 0) {
    return res.status(HTTP_BAD_REQUEST).json({ error: 'teamId is required' });
  }

  if (!name || typeof name !== 'string' || name.trim().length === 0) {
    return res.status(HTTP_BAD_REQUEST).json({ error: 'name is required' });
  }

  const team = await getTeam({ userId: user.userId, teamId });

  if (!team) {
    return res.status(HTTP_BAD_REQUEST).json({ error: 'Team not found' });
  }

  await updateTeamName(user.userId, teamId, name);

  return res.status(HTTP_OK).json({ success: true });
};

const deleteHandler = async (req: NextApiRequest, res: NextApiResponse) => {
  const user = getAuth(req);

  if (!user || !user.userId) {
    return res
      .status(HTTP_BAD_REQUEST)
      .json({ error: 'User is not authenticated' });
  }

  const teamId = req.body.teamId;

  if (!teamId || typeof teamId !== 'string' || teamId.trim().length === 0) {
    return res.status(HTTP_BAD_REQUEST).json({ error: 'teamId is required' });
  }

  const team = await getTeam({ userId: user.userId, teamId });

  if (!team) {
    return res.status(HTTP_BAD_REQUEST).json({ error: 'Team not found' });
  }

  await deleteTeam(user.userId, teamId);

  return res.status(HTTP_OK).json({ success: true });
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    switch (req.method) {
      case 'POST':
        return postHandler(req, res);
      case 'GET':
        return getHandler(req, res);
      case 'PATCH':
        return patchHandler(req, res);
      case 'DELETE':
        return deleteHandler(req, res);
      default:
        return res
          .status(HTTP_BAD_REQUEST)
          .json({ error: 'Invalid request method' });
    }
  } catch (error) {
    console.error('[api/teams.ts] Error retrieving tools details:', error);
    res.status(HTTP_INTERNAL_SERVER_ERROR).json({
      error: '[api/teams.ts] Failed to retrieve tool details'
    });
  }
}
