import type { NextApiRequest, NextApiResponse } from 'next';
import Stripe from 'stripe';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is not defined');
}

const stripeServerSide = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2022-11-15'
});

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  try {
    const host = req.headers.origin;
    const referer = req.headers.referer;
    const body = JSON.parse(req.body);

    const session = await stripeServerSide.checkout.sessions.create({
      mode: 'payment',
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: body?.title,
              images: [body.image],
              description: body?.description
            },
            unit_amount_decimal: '100'
          },
          quantity: 1
        }
      ],
      success_url: `${host}/thank-you`,
      cancel_url: `${referer}?status=cancel`
    });
    return res.status(200).json({ id: session.id });
  } catch (error) {
    return res.status(500).json({
      message: 'Something went wrong!! Please try again after sometime'
    });
  }
};

export default handler;
