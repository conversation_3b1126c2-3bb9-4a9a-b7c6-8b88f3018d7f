import { NextApiRequest, NextApiResponse } from 'next';

function subscribeToEmailList(email, source, environment) {
  return console.log(
    '[email-signup.ts] subscribeToEmailList',
    email,
    source,
    environment
  );
}

export default async function emailSignupHandler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { email, source, environment } = req.body;

  if (!email) {
    return res.status(400).json({ error: 'Email is required' });
  }

  if (!validateEmail(email)) {
    return res.status(400).json({ error: 'Invalid email format' });
  }

  try {
    await subscribeToEmailList(email, source, environment); // replace with your own email service function
    return res.status(200).json({ message: 'Success' });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: 'Server error' });
  }
}

function validateEmail(email: string): boolean {
  // Use a regular expression to validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
