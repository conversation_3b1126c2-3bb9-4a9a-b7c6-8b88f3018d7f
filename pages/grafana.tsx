import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { StepContainer } from '../context/grafana/shared/StepContainer';
import { CredentialsStep } from '../context/grafana/steps/CredentialsStep';
import { CheckEmailStep } from '../context/grafana/steps/CheckEmailStep';
import { InstallStep } from '../context/grafana/steps/InstallStep';
import { RunStep } from '../context/grafana/steps/RunStep';
import { ViewDemoStep } from '../context/grafana/steps/ViewDemoStep';
import { Step, StepConfig } from '../context/grafana/shared/types';
// import { checkUserExists } from '../lib/grafanaService';
// import { StyledSubTitle } from '@/components/common/Layout/StyledApp';

export default function GrafanaDashboard() {
    const { user } = useUser();
    const [currentStep, setCurrentStep] = useState<Step | null>(null);
    const [error, setError] = useState<string | null>(null);
    const [response, setResponse] = useState<any>(null);
    const [completedSteps, setCompletedSteps] = useState<Step[]>([]);
    const email = user?.emailAddresses[0]?.emailAddress || '';
    const username = email.split('@')[0];
    const runCommand = `tracer init --pipeline-name demo_${username} --environment demo --pipeline-type rnaseq --user-operator ${email} --is-dev false`;
    const grafanaUrl = `https://tracerbio.grafana.net/d/del60tad55hc0f/start-here?orgId=1&from=now-6h&to=now&timezone=browser`;

    // Set the first step active on initial load
    useEffect(() => {
        if (currentStep === null) {
            setCurrentStep('install');
        }
    }, [currentStep]);

    const steps: Record<Step, StepConfig> = {
        install: {
            title: 'Step 1: Open GitHub Codespaces to install Tracer',
            component: InstallStep,
        },
        run: {
            title: 'Step 2: Run Tracer in Codespaces',
            component: (props) => <RunStep 
                {...props} 
                command={runCommand} 
                onComplete={() => handleStepComplete('run')}
            />,
        },
        credentials: {
            title: 'Step 3: Generate your Grafana credentials',
            component: CredentialsStep,
        },
        checkEmail: {
            title: 'Step 4: Check your email to launch Grafana',
            component: CheckEmailStep,
        },
        complete: {
            title: 'Step 5: Open Grafana to view your demo pipeline',
            component: (props) => <ViewDemoStep 
                {...props} 
                grafanaUrl={grafanaUrl}
                showConfetti={true} 
            />,
        },
    };

    const isStepActive = (step: Step) => {
        if (currentStep === null) return false;
        
        // New order: install, run, credentials, checkEmail, complete
        const order: Step[] = ['install', 'run', 'credentials', 'checkEmail', 'complete'];
        return order.indexOf(step) <= order.indexOf(currentStep);
    };

    const isStepCompleted = (step: Step) => {
        return completedSteps.includes(step);
    };

    const handleStepComplete = (step: Step) => {
        // Update completed steps
        setCompletedSteps((prev) => [...prev, step]);
        
        // New order: install, run, credentials, checkEmail, complete
        const order: Step[] = ['install', 'run', 'credentials', 'checkEmail', 'complete'];
        
        // Move to the next stepy
        const nextIndex = order.indexOf(step) + 1;
        if (nextIndex < order.length) {
            setCurrentStep(order[nextIndex]);
        }
    };

    return (
        <div className="container mx-auto py-8 px-4">
            <h1 className="text-2xl font-bold text-center text-white mb-8">
                Experience Tracer’s Demo in Just 5 Easy Steps!
            </h1>
            
            <div className="space-y-4">
                {Object.entries(steps).map(([step, { title, component: Component }]) => (
                    <StepContainer
                        key={step}
                        title={title}
                        isActive={isStepActive(step as Step)}
                        isCompleted={isStepCompleted(step as Step)}
                    >
                        <Component
                            isActive={currentStep === step as Step}
                            isCompleted={isStepCompleted(step as Step)}
                            email={email}
                            username={username}
                            onComplete={() => handleStepComplete(step as Step)}
                            setError={setError}
                            setResponse={setResponse}
                        />
                        {error && step === currentStep && (
                            <div className="mt-4 p-4 bg-red-900 rounded-md text-white">
                                <div className="font-semibold">Error:</div>
                                <div>{error}</div>
                            </div>
                        )}
                        {response && response.success && step === 'credentials' && currentStep === 'checkEmail' && (
                            <div className="mt-4 p-4 bg-green-900 rounded-md text-white">
                                <div className="font-semibold">Success:</div>
                                <div>{response.message}</div>
                            </div>
                        )}
                    </StepContainer>
                ))}
            </div>
        </div>
    );
}
